import { SSHConnection, RemoteFile, RemoteDirectory } from '../types';

// Implementación simple sin dependencias SSH para evitar problemas de webpack
export class SimpleSSHService {
  private connections: Map<string, SSHConnection> = new Map();
  private mockFiles: RemoteFile[] = [
    {
      name: 'Downloads',
      path: '/home/<USER>/Downloads',
      type: 'directory',
      size: 0,
      modified: new Date(),
      permissions: 'drwxr-xr-x'
    },
    {
      name: 'Movies',
      path: '/home/<USER>/Movies',
      type: 'directory',
      size: 0,
      modified: new Date(),
      permissions: 'drwxr-xr-x'
    },
    {
      name: 'Series',
      path: '/home/<USER>/Series',
      type: 'directory',
      size: 0,
      modified: new Date(),
      permissions: 'drwxr-xr-x'
    },
    {
      name: 'example.mp4',
      path: '/home/<USER>/example.mp4',
      type: 'file',
      size: 1024 * 1024 * 500, // 500MB
      modified: new Date(),
      permissions: '-rw-r--r--'
    }
  ];

  /**
   * Conecta a un servidor SSH (simulado)
   */
  async connect(connection: SSHConnection): Promise<boolean> {
    try {
      console.log('🔐 Conectando a SSH:', connection.host);
      
      // Simular delay de conexión
      await new Promise(resolve => setTimeout(resolve, 1000));
      
      // Simular validación básica
      if (!connection.host || !connection.username) {
        throw new Error('Host y usuario son requeridos');
      }
      
      // Marcar como conectado
      const connectedConnection = {
        ...connection,
        isConnected: true,
        currentPath: `/home/<USER>
      };
      
      this.connections.set(connection.id, connectedConnection);
      
      console.log('✅ Conectado exitosamente a:', connection.host);
      return true;
      
    } catch (error) {
      console.error('❌ Error conectando SSH:', error);
      throw error;
    }
  }

  /**
   * Desconecta de un servidor SSH
   */
  async disconnect(connectionId: string): Promise<void> {
    const connection = this.connections.get(connectionId);
    if (connection) {
      connection.isConnected = false;
      console.log('🔌 Desconectado de:', connection.host);
    }
  }

  /**
   * Lista archivos en un directorio remoto (simulado)
   */
  async listFiles(connectionId: string, path: string): Promise<RemoteDirectory> {
    const connection = this.connections.get(connectionId);
    if (!connection || !connection.isConnected) {
      throw new Error('No hay conexión SSH activa');
    }

    console.log('📁 Listando archivos en:', path);

    // Simular delay de red
    await new Promise(resolve => setTimeout(resolve, 300));

    // Generar archivos simulados basados en la ruta
    const files: RemoteFile[] = [];
    
    if (path === `/home/<USER>'/') {
      // Directorio home
      files.push(...this.mockFiles);
    } else if (path.includes('Downloads')) {
      files.push(
        {
          name: 'movie1.mp4',
          path: path + '/movie1.mp4',
          type: 'file',
          size: 1024 * 1024 * 1200, // 1.2GB
          modified: new Date(Date.now() - 86400000),
          permissions: '-rw-r--r--'
        },
        {
          name: 'series_s01e01.mp4',
          path: path + '/series_s01e01.mp4',
          type: 'file',
          size: 1024 * 1024 * 800, // 800MB
          modified: new Date(Date.now() - 172800000),
          permissions: '-rw-r--r--'
        }
      );
    } else if (path.includes('Movies')) {
      files.push(
        {
          name: 'Action',
          path: path + '/Action',
          type: 'directory',
          size: 0,
          modified: new Date(),
          permissions: 'drwxr-xr-x'
        },
        {
          name: 'Comedy',
          path: path + '/Comedy',
          type: 'directory',
          size: 0,
          modified: new Date(),
          permissions: 'drwxr-xr-x'
        }
      );
    }

    // Agregar directorio padre si no estamos en root
    if (path !== `/home/<USER>'/') {
      const parentPath = path.split('/').slice(0, -1).join('/') || '/';
      files.unshift({
        name: '..',
        path: parentPath,
        type: 'directory',
        size: 0,
        modified: new Date(),
        permissions: 'drwxr-xr-x'
      });
    }

    return {
      path,
      files,
      parent: path !== `/home/<USER>'/').slice(0, -1).join('/') : undefined
    };
  }

  /**
   * Crea un directorio remoto (simulado)
   */
  async createDirectory(connectionId: string, path: string): Promise<boolean> {
    const connection = this.connections.get(connectionId);
    if (!connection || !connection.isConnected) {
      throw new Error('No hay conexión SSH activa');
    }

    console.log('📁 Creando directorio:', path);
    
    // Simular delay
    await new Promise(resolve => setTimeout(resolve, 200));
    
    console.log('✅ Directorio creado:', path);
    return true;
  }

  /**
   * Elimina un archivo o directorio remoto (simulado)
   */
  async deleteFile(connectionId: string, path: string): Promise<boolean> {
    const connection = this.connections.get(connectionId);
    if (!connection || !connection.isConnected) {
      throw new Error('No hay conexión SSH activa');
    }

    console.log('🗑️ Eliminando:', path);
    
    // Simular delay
    await new Promise(resolve => setTimeout(resolve, 300));
    
    console.log('✅ Eliminado:', path);
    return true;
  }

  /**
   * Obtiene información de conexión
   */
  getConnection(connectionId: string): SSHConnection | undefined {
    return this.connections.get(connectionId);
  }

  /**
   * Verifica si hay una conexión activa
   */
  isConnected(connectionId: string): boolean {
    const connection = this.connections.get(connectionId);
    return connection?.isConnected || false;
  }

  /**
   * Ejecuta un comando remoto (simulado)
   */
  async executeCommand(connectionId: string, command: string): Promise<string> {
    const connection = this.connections.get(connectionId);
    if (!connection || !connection.isConnected) {
      throw new Error('No hay conexión SSH activa');
    }

    console.log('⚡ Ejecutando comando:', command);
    
    // Simular delay de ejecución
    await new Promise(resolve => setTimeout(resolve, 500));
    
    // Simular respuestas básicas
    if (command.includes('wget')) {
      return 'wget: comando iniciado en segundo plano';
    } else if (command.includes('ls')) {
      return 'archivo1.mp4\narchivo2.mp4\ndirectorio1/';
    } else if (command.includes('pwd')) {
      return connection.currentPath;
    } else {
      return `Comando ejecutado: ${command}`;
    }
  }

  /**
   * Obtiene el espacio libre en disco (simulado)
   */
  async getDiskSpace(connectionId: string, path: string): Promise<{ total: number; free: number; used: number }> {
    const connection = this.connections.get(connectionId);
    if (!connection || !connection.isConnected) {
      throw new Error('No hay conexión SSH activa');
    }

    // Simular información de disco
    return {
      total: 1024 * 1024 * 1024 * 500, // 500GB
      free: 1024 * 1024 * 1024 * 200,  // 200GB libre
      used: 1024 * 1024 * 1024 * 300   // 300GB usado
    };
  }
}
