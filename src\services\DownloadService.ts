import { DownloadTask, DownloadQueue, M3UEntry } from '../types';

export class DownloadService {
  private queue: DownloadQueue = {
    tasks: [],
    isProcessing: false,
    totalTasks: 0,
    completedTasks: 0,
    failedTasks: 0
  };
  
  private listeners: Array<(queue: DownloadQueue) => void> = [];

  /**
   * Agrega una tarea de descarga a la cola
   */
  addDownloadTask(
    entry: M3UEntry, 
    destinationPath: string, 
    connectionId: string
  ): string {
    const task: DownloadTask = {
      id: Date.now().toString() + Math.random().toString(36).substr(2, 9),
      entry,
      destinationPath,
      connectionId,
      status: 'pending',
      progress: 0,
      speed: 0,
      eta: 0,
      startTime: new Date(),
      error: undefined
    };

    this.queue.tasks.push(task);
    this.queue.totalTasks++;
    
    console.log('📥 Tarea agregada a la cola:', entry.title);
    this.notifyListeners();
    
    // Auto-iniciar procesamiento si no está activo
    if (!this.queue.isProcessing) {
      this.startProcessing();
    }
    
    return task.id;
  }

  /**
   * Agrega múltiples tareas de descarga (descarga masiva)
   */
  addBulkDownloadTasks(
    entries: M3UEntry[], 
    destinationPath: string, 
    connectionId: string,
    seriesTitle?: string
  ): string[] {
    const taskIds: string[] = [];
    
    console.log('📦 Iniciando descarga masiva:', entries.length, 'elementos');
    
    entries.forEach((entry, index) => {
      // Generar nombre de archivo basado en el tipo de contenido
      let filename = this.generateFilename(entry, index, seriesTitle);
      let fullPath = `${destinationPath}/${filename}`;
      
      const taskId = this.addDownloadTask(entry, fullPath, connectionId);
      taskIds.push(taskId);
    });
    
    return taskIds;
  }

  /**
   * Genera un nombre de archivo apropiado para la descarga
   */
  private generateFilename(entry: M3UEntry, index: number, seriesTitle?: string): string {
    let filename = entry.title || `video_${index + 1}`;
    
    // Limpiar caracteres especiales
    filename = filename.replace(/[<>:"/\\|?*]/g, '_');
    
    // Si es una serie, usar formato organizado
    if (entry.type === 'series' && seriesTitle) {
      const seasonMatch = entry.title.match(/S(\d+)E(\d+)/i);
      if (seasonMatch) {
        const season = seasonMatch[1].padStart(2, '0');
        const episode = seasonMatch[2].padStart(2, '0');
        filename = `${seriesTitle}_S${season}E${episode}.mp4`;
      } else {
        filename = `${seriesTitle}_${filename}.mp4`;
      }
    } else {
      // Agregar extensión si no la tiene
      if (!filename.includes('.')) {
        filename += '.mp4';
      }
    }
    
    return filename;
  }

  /**
   * Inicia el procesamiento de la cola de descargas
   */
  private async startProcessing(): Promise<void> {
    if (this.queue.isProcessing) return;
    
    this.queue.isProcessing = true;
    console.log('🚀 Iniciando procesamiento de cola de descargas');
    
    while (this.queue.tasks.some(task => task.status === 'pending')) {
      const nextTask = this.queue.tasks.find(task => task.status === 'pending');
      if (!nextTask) break;
      
      await this.processTask(nextTask);
      await new Promise(resolve => setTimeout(resolve, 100)); // Pequeña pausa entre tareas
    }
    
    this.queue.isProcessing = false;
    console.log('✅ Procesamiento de cola completado');
    this.notifyListeners();
  }

  /**
   * Procesa una tarea individual de descarga (simulado)
   */
  private async processTask(task: DownloadTask): Promise<void> {
    console.log('⬇️ Iniciando descarga:', task.entry.title);
    
    task.status = 'downloading';
    task.startTime = new Date();
    this.notifyListeners();
    
    try {
      // Simular descarga con progreso
      for (let progress = 0; progress <= 100; progress += 5) {
        task.progress = progress;
        task.speed = Math.random() * 10 + 5; // 5-15 MB/s simulado
        task.eta = ((100 - progress) / 5) * 200; // ETA simulado en ms
        
        this.notifyListeners();
        await new Promise(resolve => setTimeout(resolve, 200)); // Simular tiempo de descarga
        
        // Simular posible fallo (5% de probabilidad)
        if (Math.random() < 0.05 && progress > 20) {
          throw new Error('Error de conexión simulado');
        }
      }
      
      // Descarga completada
      task.status = 'completed';
      task.progress = 100;
      task.speed = 0;
      task.eta = 0;
      this.queue.completedTasks++;
      
      console.log('✅ Descarga completada:', task.entry.title);
      
    } catch (error) {
      // Descarga fallida
      task.status = 'failed';
      task.error = error instanceof Error ? error.message : 'Error desconocido';
      this.queue.failedTasks++;
      
      console.error('❌ Descarga fallida:', task.entry.title, error);
    }
    
    this.notifyListeners();
  }

  /**
   * Pausa una tarea de descarga
   */
  pauseTask(taskId: string): void {
    const task = this.queue.tasks.find(t => t.id === taskId);
    if (task && task.status === 'downloading') {
      task.status = 'paused';
      console.log('⏸️ Descarga pausada:', task.entry.title);
      this.notifyListeners();
    }
  }

  /**
   * Reanuda una tarea pausada
   */
  resumeTask(taskId: string): void {
    const task = this.queue.tasks.find(t => t.id === taskId);
    if (task && task.status === 'paused') {
      task.status = 'pending';
      console.log('▶️ Descarga reanudada:', task.entry.title);
      this.notifyListeners();
      
      if (!this.queue.isProcessing) {
        this.startProcessing();
      }
    }
  }

  /**
   * Cancela una tarea de descarga
   */
  cancelTask(taskId: string): void {
    const task = this.queue.tasks.find(t => t.id === taskId);
    if (task) {
      task.status = 'cancelled';
      console.log('🚫 Descarga cancelada:', task.entry.title);
      this.notifyListeners();
    }
  }

  /**
   * Elimina una tarea de la cola
   */
  removeTask(taskId: string): void {
    const index = this.queue.tasks.findIndex(t => t.id === taskId);
    if (index !== -1) {
      const task = this.queue.tasks[index];
      this.queue.tasks.splice(index, 1);
      
      // Actualizar contadores
      if (task.status === 'completed') this.queue.completedTasks--;
      if (task.status === 'failed') this.queue.failedTasks--;
      this.queue.totalTasks--;
      
      console.log('🗑️ Tarea eliminada:', task.entry.title);
      this.notifyListeners();
    }
  }

  /**
   * Limpia todas las tareas completadas y fallidas
   */
  clearCompletedTasks(): void {
    const beforeCount = this.queue.tasks.length;
    this.queue.tasks = this.queue.tasks.filter(
      task => task.status !== 'completed' && task.status !== 'failed'
    );
    
    const removedCount = beforeCount - this.queue.tasks.length;
    this.queue.completedTasks = 0;
    this.queue.failedTasks = 0;
    this.queue.totalTasks = this.queue.tasks.length;
    
    console.log('🧹 Limpieza completada:', removedCount, 'tareas eliminadas');
    this.notifyListeners();
  }

  /**
   * Obtiene el estado actual de la cola
   */
  getQueue(): DownloadQueue {
    return { ...this.queue };
  }

  /**
   * Suscribe un listener para cambios en la cola
   */
  subscribe(listener: (queue: DownloadQueue) => void): () => void {
    this.listeners.push(listener);
    
    // Retorna función para desuscribirse
    return () => {
      const index = this.listeners.indexOf(listener);
      if (index !== -1) {
        this.listeners.splice(index, 1);
      }
    };
  }

  /**
   * Notifica a todos los listeners sobre cambios en la cola
   */
  private notifyListeners(): void {
    this.listeners.forEach(listener => {
      try {
        listener({ ...this.queue });
      } catch (error) {
        console.error('Error notificando listener:', error);
      }
    });
  }

  /**
   * Obtiene estadísticas de la cola
   */
  getStats(): {
    total: number;
    pending: number;
    downloading: number;
    completed: number;
    failed: number;
    paused: number;
    cancelled: number;
  } {
    const stats = {
      total: this.queue.tasks.length,
      pending: 0,
      downloading: 0,
      completed: 0,
      failed: 0,
      paused: 0,
      cancelled: 0
    };

    this.queue.tasks.forEach(task => {
      stats[task.status]++;
    });

    return stats;
  }
}
