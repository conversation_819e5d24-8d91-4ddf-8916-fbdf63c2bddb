import React, { useState, useEffect } from 'react';
import styled from 'styled-components';
import { Panel, PanelGroup, PanelResizeHandle } from 'react-resizable-panels';
import M3UPanel from './components/M3UPanel';
import SFTPPanel from './components/SFTPPanel';
import DownloadPanel from './components/DownloadPanel';
import { AppState, M3UPlaylist, SSHConnection, DownloadQueue, M3UEntry } from './types';
import { DownloadManager } from './services/DownloadManager';
import { SSHService } from './services/SSHService';

const AppContainer = styled.div`
  width: 100vw;
  height: 100vh;
  background: #1a1a1a;
  color: #ffffff;
  font-family: 'Segoe UI', Tahoma, Geneva, Verdana, sans-serif;
  display: flex;
  flex-direction: column;
`;

const Header = styled.div`
  height: 60px;
  background: #2d2d2d;
  border-bottom: 1px solid #404040;
  display: flex;
  align-items: center;
  padding: 0 20px;
  box-shadow: 0 2px 4px rgba(0, 0, 0, 0.3);
`;

const Title = styled.h1`
  font-size: 18px;
  font-weight: 600;
  color: #ffffff;
  margin: 0;
`;

const MainContent = styled.div`
  flex: 1;
  display: flex;
  flex-direction: column;
`;

const PanelsContainer = styled.div`
  flex: 1;
  display: flex;
`;

const ResizeHandle = styled(PanelResizeHandle)`
  width: 4px;
  background: #404040;
  cursor: col-resize;
  transition: background-color 0.2s;

  &:hover {
    background: #0078d4;
  }

  &[data-resize-handle-active] {
    background: #0078d4;
  }
`;

const VerticalResizeHandle = styled(PanelResizeHandle)`
  height: 4px;
  background: #404040;
  cursor: row-resize;
  transition: background-color 0.2s;

  &:hover {
    background: #0078d4;
  }

  &[data-resize-handle-active] {
    background: #0078d4;
  }
`;

const App: React.FC = () => {
  const [downloadManager] = useState(() => new DownloadManager());
  const [sshService] = useState(() => new SSHService());
  const [appState, setAppState] = useState<AppState>({
    m3uPlaylists: [],
    sshConnections: [],
    downloadQueue: {
      tasks: [],
      isRunning: false,
      totalTasks: 0,
      completedTasks: 0,
      failedTasks: 0,
    },
  });

  useEffect(() => {
    // Configurar el servicio SSH en el download manager
    downloadManager.setSSHService(sshService);

    // Suscribirse a cambios en la cola de descargas
    const handleQueueUpdate = (queue: DownloadQueue) => {
      setAppState(prev => ({
        ...prev,
        downloadQueue: queue,
      }));
    };

    downloadManager.addListener(handleQueueUpdate);

    // Cargar estado inicial de la cola
    const initialQueue = downloadManager.getQueueState();
    handleQueueUpdate(initialQueue);

    return () => {
      downloadManager.removeListener(handleQueueUpdate);
    };
  }, [downloadManager, sshService]);

  const updatePlaylist = (playlist: M3UPlaylist) => {
    setAppState(prev => ({
      ...prev,
      m3uPlaylists: [
        ...prev.m3uPlaylists.filter(p => p.id !== playlist.id),
        playlist
      ],
      currentPlaylist: playlist.id,
    }));
  };

  const updateConnection = (connection: SSHConnection) => {
    setAppState(prev => ({
      ...prev,
      sshConnections: [
        ...prev.sshConnections.filter(c => c.id !== connection.id),
        connection
      ],
      currentConnection: connection.id,
    }));
  };

  const handleDownloadRequest = (entries: M3UEntry[], seriesTitle?: string) => {
    const destination = appState.selectedDestination;
    if (!destination) {
      alert('Por favor selecciona un destino en el panel SFTP');
      return;
    }

    if (seriesTitle) {
      // Descarga de serie completa
      downloadManager.addSeriesDownload(entries, destination, seriesTitle);
    } else {
      // Descarga individual o múltiple
      downloadManager.addBulkDownload(entries, destination);
    }
  };

  // Funciones para controlar la cola de descargas
  const handleStartQueue = () => {
    downloadManager.startQueue();
  };

  const handleStopQueue = () => {
    downloadManager.stopQueue();
  };

  const handlePauseTask = (taskId: string) => {
    downloadManager.pauseTask(taskId);
  };

  const handleResumeTask = (taskId: string) => {
    downloadManager.resumeTask(taskId);
  };

  const handleRemoveTask = (taskId: string) => {
    downloadManager.removeTask(taskId);
  };

  const handleClearCompleted = () => {
    downloadManager.clearCompleted();
  };

  const setSelectedDestination = (path: string) => {
    setAppState(prev => ({
      ...prev,
      selectedDestination: path,
    }));
  };

  return (
    <AppContainer>
      <Header>
        <Title>M3U Import Cloud X2</Title>
      </Header>
      <MainContent>
        <PanelGroup direction="vertical">
          <Panel defaultSize={75} minSize={50}>
            <PanelsContainer>
              <PanelGroup direction="horizontal">
                <Panel defaultSize={50} minSize={30}>
                  <M3UPanel
                    playlists={appState.m3uPlaylists}
                    currentPlaylist={appState.currentPlaylist}
                    onPlaylistUpdate={updatePlaylist}
                    onDownloadRequest={handleDownloadRequest}
                  />
                </Panel>
                <ResizeHandle />
                <Panel defaultSize={50} minSize={30}>
                  <SFTPPanel
                    connections={appState.sshConnections}
                    currentConnection={appState.currentConnection}
                    selectedDestination={appState.selectedDestination}
                    onConnectionUpdate={updateConnection}
                    onDestinationSelect={setSelectedDestination}
                  />
                </Panel>
              </PanelGroup>
            </PanelsContainer>
          </Panel>
          <VerticalResizeHandle />
          <Panel defaultSize={25} minSize={15}>
            <DownloadPanel
              downloadQueue={appState.downloadQueue}
              onStartQueue={handleStartQueue}
              onStopQueue={handleStopQueue}
              onPauseTask={handlePauseTask}
              onResumeTask={handleResumeTask}
              onRemoveTask={handleRemoveTask}
              onClearCompleted={handleClearCompleted}
            />
          </Panel>
        </PanelGroup>
      </MainContent>
    </AppContainer>
  );
};

export default App;
