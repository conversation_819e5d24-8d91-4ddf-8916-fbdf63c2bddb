import React, { useState, useEffect } from 'react';
import styled from 'styled-components';
import { AppState, M3UPlaylist, SSHConnection, DownloadQueue, M3UEntry } from './types';
import M3UPanelSimple from './components/M3UPanelSimple';
import SFTPPanelSimple from './components/SFTPPanelSimple';
import DownloadPanelSimple from './components/DownloadPanelSimple';

const AppContainer = styled.div`
  width: 100vw;
  height: 100vh;
  background: #1a1a1a;
  color: #ffffff;
  font-family: 'Segoe UI', Tahoma, Geneva, Verdana, sans-serif;
  display: flex;
  flex-direction: column;
`;

const Header = styled.div`
  height: 60px;
  background: #2d2d2d;
  border-bottom: 1px solid #404040;
  display: flex;
  align-items: center;
  padding: 0 20px;
  box-shadow: 0 2px 4px rgba(0, 0, 0, 0.3);
`;

const Title = styled.h1`
  font-size: 18px;
  font-weight: 600;
  color: #ffffff;
  margin: 0;
`;

const MainContent = styled.div`
  flex: 1;
  display: flex;
  flex-direction: column;
`;

const PanelsContainer = styled.div`
  flex: 1;
  display: flex;
`;

// Handles de redimensionamiento removidos para simplificar

const App: React.FC = () => {
  const [appState, setAppState] = useState<AppState>({
    m3uPlaylists: [],
    sshConnections: [],
    downloadQueue: {
      tasks: [],
      isRunning: false,
      totalTasks: 0,
      completedTasks: 0,
      failedTasks: 0,
    },
  });

  // Funciones simplificadas para la demo
  const updatePlaylist = (playlist: M3UPlaylist) => {
    console.log('📺 Playlist actualizada:', playlist.name);
    setAppState(prev => ({
      ...prev,
      m3uPlaylists: [
        ...prev.m3uPlaylists.filter(p => p.id !== playlist.id),
        playlist
      ],
      currentPlaylist: playlist.id,
    }));
  };

  const updateConnection = (connection: SSHConnection) => {
    console.log('🔐 Conexión SSH actualizada:', connection.name);
    setAppState(prev => ({
      ...prev,
      sshConnections: [
        ...prev.sshConnections.filter(c => c.id !== connection.id),
        connection
      ],
      currentConnection: connection.id,
    }));
  };

  const handleDownloadRequest = (entries: M3UEntry[], seriesTitle?: string) => {
    console.log('📥 Solicitud de descarga:', entries.length, 'elementos');
    alert(`Funcionalidad de descarga temporalmente deshabilitada.\nElementos: ${entries.length}\nSerie: ${seriesTitle || 'N/A'}`);
  };

  // Funciones simplificadas para controlar la cola de descargas
  const handleStartQueue = () => console.log('▶️ Iniciar cola');
  const handleStopQueue = () => console.log('⏹️ Detener cola');
  const handlePauseTask = (taskId: string) => console.log('⏸️ Pausar tarea:', taskId);
  const handleResumeTask = (taskId: string) => console.log('▶️ Reanudar tarea:', taskId);
  const handleRemoveTask = (taskId: string) => console.log('🗑️ Eliminar tarea:', taskId);
  const handleClearCompleted = () => console.log('🧹 Limpiar completadas');

  const setSelectedDestination = (path: string) => {
    console.log('📁 Destino seleccionado:', path);
    setAppState(prev => ({
      ...prev,
      selectedDestination: path,
    }));
  };

  console.log('🎨 Rendering App JSX...');

  return (
    <AppContainer>
      <div style={{ display: 'flex', height: '100vh' }}>
        {/* Panel M3U */}
        <div style={{ width: '33%', minWidth: '300px' }}>
          <M3UPanelSimple
            playlists={appState.m3uPlaylists}
            currentPlaylist={appState.currentPlaylist}
            onPlaylistUpdate={updatePlaylist}
            onDownloadRequest={handleDownloadRequest}
          />
        </div>

        {/* Panel SFTP */}
        <div style={{ width: '33%', minWidth: '300px' }}>
          <SFTPPanelSimple
            connections={appState.sshConnections}
            currentConnection={appState.currentConnection}
            selectedDestination={appState.selectedDestination}
            onConnectionUpdate={updateConnection}
            onDestinationSelect={setSelectedDestination}
          />
        </div>

        {/* Panel de Descargas */}
        <div style={{ width: '34%', minWidth: '300px' }}>
          <DownloadPanelSimple
            downloadQueue={appState.downloadQueue}
            onStartQueue={handleStartQueue}
            onStopQueue={handleStopQueue}
            onPauseTask={handlePauseTask}
            onResumeTask={handleResumeTask}
            onRemoveTask={handleRemoveTask}
            onClearCompleted={handleClearCompleted}
          />
        </div>
      </div>
    </AppContainer>
  );
};

export default App;
