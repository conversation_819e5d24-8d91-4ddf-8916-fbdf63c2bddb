import React from 'react';
import styled from 'styled-components';
import { DownloadTask, DownloadQueue } from '../types';

interface DownloadPanelProps {
  downloadQueue: DownloadQueue;
  onStartQueue: () => void;
  onStopQueue: () => void;
  onPauseTask: (taskId: string) => void;
  onResumeTask: (taskId: string) => void;
  onRemoveTask: (taskId: string) => void;
  onClearCompleted: () => void;
}

const PanelContainer = styled.div`
  height: 100%;
  background: #252525;
  border-top: 1px solid #404040;
  display: flex;
  flex-direction: column;
`;

const Header = styled.div`
  padding: 15px;
  background: #2d2d2d;
  border-bottom: 1px solid #404040;
`;

const Title = styled.h2`
  font-size: 16px;
  font-weight: 600;
  margin: 0 0 10px 0;
  color: #ffffff;
`;

const ButtonGroup = styled.div`
  display: flex;
  gap: 10px;
  margin-bottom: 10px;
`;

const Button = styled.button<{ variant?: 'primary' | 'secondary' | 'danger' }>`
  padding: 8px 16px;
  background: ${props => {
    switch (props.variant) {
      case 'danger': return '#dc3545';
      case 'secondary': return '#6c757d';
      default: return '#0078d4';
    }
  }};
  color: white;
  border: none;
  border-radius: 4px;
  cursor: pointer;
  font-size: 12px;
  transition: background-color 0.2s;

  &:hover {
    background: ${props => {
      switch (props.variant) {
        case 'danger': return '#c82333';
        case 'secondary': return '#5a6268';
        default: return '#106ebe';
      }
    }};
  }

  &:disabled {
    background: #404040;
    cursor: not-allowed;
  }
`;

const QueueStats = styled.div`
  display: flex;
  gap: 15px;
  font-size: 12px;
  color: #ccc;
`;

const Content = styled.div`
  flex: 1;
  overflow: hidden;
  display: flex;
  flex-direction: column;
`;

const TaskList = styled.div`
  flex: 1;
  overflow-y: auto;
  padding: 10px;
`;

const TaskItem = styled.div<{ status: string }>`
  padding: 12px;
  margin-bottom: 8px;
  background: #353535;
  border-radius: 6px;
  border-left: 4px solid ${props => {
    switch (props.status) {
      case 'completed': return '#28a745';
      case 'downloading': return '#0078d4';
      case 'failed': return '#dc3545';
      case 'paused': return '#ffc107';
      default: return '#6c757d';
    }
  }};
`;

const TaskHeader = styled.div`
  display: flex;
  justify-content: between;
  align-items: center;
  margin-bottom: 8px;
`;

const TaskName = styled.div`
  flex: 1;
  font-weight: 500;
  color: #ffffff;
  font-size: 13px;
  margin-right: 10px;
`;

const TaskActions = styled.div`
  display: flex;
  gap: 5px;
`;

const ActionButton = styled.button`
  padding: 4px 8px;
  background: #404040;
  color: white;
  border: none;
  border-radius: 3px;
  cursor: pointer;
  font-size: 11px;
  transition: background-color 0.2s;

  &:hover {
    background: #505050;
  }
`;

const TaskInfo = styled.div`
  font-size: 11px;
  color: #ccc;
  margin-bottom: 6px;
`;

const ProgressBar = styled.div`
  width: 100%;
  height: 6px;
  background: #404040;
  border-radius: 3px;
  overflow: hidden;
`;

const ProgressFill = styled.div<{ progress: number; status: string }>`
  height: 100%;
  width: ${props => props.progress}%;
  background: ${props => {
    switch (props.status) {
      case 'completed': return '#28a745';
      case 'downloading': return '#0078d4';
      case 'failed': return '#dc3545';
      case 'paused': return '#ffc107';
      default: return '#6c757d';
    }
  }};
  transition: width 0.3s ease;
`;

const StatusBar = styled.div`
  padding: 10px 15px;
  background: #2d2d2d;
  border-top: 1px solid #404040;
  font-size: 11px;
  color: #ccc;
`;

const EmptyState = styled.div`
  flex: 1;
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
  color: #888;
  font-size: 14px;
  text-align: center;
`;

const DownloadPanel: React.FC<DownloadPanelProps> = ({
  downloadQueue,
  onStartQueue,
  onStopQueue,
  onPauseTask,
  onResumeTask,
  onRemoveTask,
  onClearCompleted,
}) => {
  const formatFileSize = (bytes: number): string => {
    if (!bytes) return '0 B';
    const k = 1024;
    const sizes = ['B', 'KB', 'MB', 'GB'];
    const i = Math.floor(Math.log(bytes) / Math.log(k));
    return parseFloat((bytes / Math.pow(k, i)).toFixed(1)) + ' ' + sizes[i];
  };

  const formatSpeed = (bytesPerSecond: number): string => {
    if (!bytesPerSecond) return '0 KB/s';
    return formatFileSize(bytesPerSecond) + '/s';
  };

  const formatTime = (date: Date): string => {
    return date.toLocaleTimeString();
  };

  const getStatusText = (status: string): string => {
    switch (status) {
      case 'pending': return 'Pendiente';
      case 'downloading': return 'Descargando';
      case 'completed': return 'Completado';
      case 'failed': return 'Error';
      case 'paused': return 'Pausado';
      default: return status;
    }
  };

  return (
    <PanelContainer>
      <Header>
        <Title>Descargas</Title>
        <ButtonGroup>
          {!downloadQueue.isRunning ? (
            <Button onClick={onStartQueue} disabled={downloadQueue.tasks.length === 0}>
              ▶ Iniciar Cola
            </Button>
          ) : (
            <Button onClick={onStopQueue} variant="danger">
              ⏸ Detener Cola
            </Button>
          )}
          <Button onClick={onClearCompleted} variant="secondary">
            🗑 Limpiar Completados
          </Button>
        </ButtonGroup>
        <QueueStats>
          <span>Total: {downloadQueue.totalTasks}</span>
          <span>Completados: {downloadQueue.completedTasks}</span>
          <span>Fallidos: {downloadQueue.failedTasks}</span>
          {downloadQueue.isRunning && <span>🔄 Ejecutando</span>}
        </QueueStats>
      </Header>

      <Content>
        {downloadQueue.tasks.length === 0 ? (
          <EmptyState>
            <div>📥</div>
            <div>No hay descargas en la cola</div>
            <div style={{ fontSize: '12px', marginTop: '10px', color: '#666' }}>
              Selecciona archivos desde el panel M3U y haz clic derecho para descargar
            </div>
          </EmptyState>
        ) : (
          <TaskList>
            {downloadQueue.tasks.map(task => (
              <TaskItem key={task.id} status={task.status}>
                <TaskHeader>
                  <TaskName>{task.name}</TaskName>
                  <TaskActions>
                    {task.status === 'downloading' && (
                      <ActionButton onClick={() => onPauseTask(task.id)}>
                        ⏸
                      </ActionButton>
                    )}
                    {task.status === 'paused' && (
                      <ActionButton onClick={() => onResumeTask(task.id)}>
                        ▶
                      </ActionButton>
                    )}
                    <ActionButton onClick={() => onRemoveTask(task.id)}>
                      🗑
                    </ActionButton>
                  </TaskActions>
                </TaskHeader>

                <TaskInfo>
                  <div>📁 {task.remotePath}/{task.fileName}</div>
                  <div>
                    Estado: {getStatusText(task.status)}
                    {task.speed && ` • ${formatSpeed(task.speed)}`}
                    {task.startTime && ` • Iniciado: ${formatTime(task.startTime)}`}
                    {task.error && ` • Error: ${task.error}`}
                  </div>
                </TaskInfo>

                <ProgressBar>
                  <ProgressFill progress={task.progress} status={task.status} />
                </ProgressBar>
              </TaskItem>
            ))}
          </TaskList>
        )}
      </Content>

      <StatusBar>
        {downloadQueue.currentTask && (
          <span>
            Descargando: {downloadQueue.tasks.find(t => t.id === downloadQueue.currentTask)?.name}
          </span>
        )}
      </StatusBar>
    </PanelContainer>
  );
};

export default DownloadPanel;
