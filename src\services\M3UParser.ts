import { M3UEntry, M3UPlaylist, SeriesInfo, SeasonInfo, EpisodeInfo } from '../types';

export class M3UParser {
  private static generateId(): string {
    return Math.random().toString(36).substr(2, 9);
  }

  static parseM3U(content: string, name: string, filePath?: string): M3UPlaylist {
    const lines = content.split('\n').map(line => line.trim()).filter(line => line);
    const entries: M3UEntry[] = [];
    const groups = new Set<string>();

    let currentEntry: Partial<M3UEntry> | null = null;

    for (let i = 0; i < lines.length; i++) {
      const line = lines[i];

      if (line.startsWith('#EXTINF:')) {
        currentEntry = this.parseExtinf(line);
      } else if (line.startsWith('http://') || line.startsWith('https://')) {
        if (currentEntry) {
          currentEntry.url = line;
          currentEntry.id = this.generateId();
          
          // Detectar tipo de contenido basado en URL y metadatos
          currentEntry.type = this.detectContentType(currentEntry as M3UEntry);
          
          // Extraer información adicional de Xtream Codes
          this.extractXtreamInfo(currentEntry as M3UEntry);
          
          entries.push(currentEntry as M3UEntry);
          
          if (currentEntry.group) {
            groups.add(currentEntry.group);
          }
          
          currentEntry = null;
        }
      }
    }

    return {
      id: this.generateId(),
      name,
      filePath,
      entries,
      groups: Array.from(groups),
      lastUpdated: new Date(),
    };
  }

  private static parseExtinf(line: string): Partial<M3UEntry> {
    const entry: Partial<M3UEntry> = {};
    
    // Extraer duración
    const durationMatch = line.match(/#EXTINF:(-?\d+(?:\.\d+)?)/);
    entry.duration = durationMatch ? parseFloat(durationMatch[1]) : -1;

    // Extraer atributos
    const attributes = this.extractAttributes(line);
    
    entry.title = attributes['tvg-name'] || attributes['title'] || 'Unknown';
    entry.group = attributes['group-title'] || 'Uncategorized';
    entry.logo = attributes['tvg-logo'] || attributes['logo'];

    // Extraer metadatos adicionales
    entry.metadata = {
      year: attributes['year'] ? parseInt(attributes['year']) : undefined,
      genre: attributes['genre'],
      plot: attributes['plot'],
      cast: attributes['cast'],
      director: attributes['director'],
      rating: attributes['rating'] ? parseFloat(attributes['rating']) : undefined,
    };

    // Extraer título del final de la línea
    const titleMatch = line.match(/,(.+)$/);
    if (titleMatch) {
      entry.title = titleMatch[1].trim();
    }

    return entry;
  }

  private static extractAttributes(line: string): Record<string, string> {
    const attributes: Record<string, string> = {};
    const regex = /(\w+(?:-\w+)*)="([^"]*)"/g;
    let match;

    while ((match = regex.exec(line)) !== null) {
      attributes[match[1]] = match[2];
    }

    return attributes;
  }

  private static detectContentType(entry: M3UEntry): 'movie' | 'series' | 'live' | 'unknown' {
    const url = entry.url.toLowerCase();
    const title = entry.title.toLowerCase();
    const group = entry.group.toLowerCase();

    // Detectar series por patrones en URL o título
    if (this.isSeriesContent(url, title, group)) {
      return 'series';
    }

    // Detectar películas
    if (this.isMovieContent(url, title, group)) {
      return 'movie';
    }

    // Detectar contenido en vivo
    if (this.isLiveContent(url, title, group)) {
      return 'live';
    }

    return 'unknown';
  }

  private static isSeriesContent(url: string, title: string, group: string): boolean {
    const seriesPatterns = [
      /s\d+e\d+/i,           // S01E01
      /season\s*\d+/i,       // Season 1
      /episode\s*\d+/i,      // Episode 1
      /\d+x\d+/,             // 1x01
      /temporada/i,          // Temporada (Spanish)
      /capitulo/i,           // Capítulo (Spanish)
      /series/i,             // Series
      /tv.*show/i,           // TV Show
    ];

    const groupPatterns = [
      /series/i,
      /tv/i,
      /shows/i,
      /temporadas/i,
    ];

    return seriesPatterns.some(pattern => 
      pattern.test(url) || pattern.test(title)
    ) || groupPatterns.some(pattern => pattern.test(group));
  }

  private static isMovieContent(url: string, title: string, group: string): boolean {
    const moviePatterns = [
      /movies?/i,
      /films?/i,
      /cinema/i,
      /peliculas?/i,
      /\b\d{4}\b/,           // Year pattern
    ];

    const groupPatterns = [
      /movies?/i,
      /films?/i,
      /cinema/i,
      /peliculas?/i,
    ];

    return moviePatterns.some(pattern => 
      pattern.test(url) || pattern.test(title)
    ) || groupPatterns.some(pattern => pattern.test(group));
  }

  private static isLiveContent(url: string, title: string, group: string): boolean {
    const livePatterns = [
      /live/i,
      /tv/i,
      /channel/i,
      /canal/i,
      /directo/i,
      /en.*vivo/i,
    ];

    return livePatterns.some(pattern => 
      pattern.test(url) || pattern.test(title) || pattern.test(group)
    );
  }

  private static extractXtreamInfo(entry: M3UEntry): void {
    const url = entry.url;
    
    // Extraer información de URLs de Xtream Codes
    const xtreamMatch = url.match(/\/([^\/]+)\/([^\/]+)\/(\d+)/);
    if (xtreamMatch) {
      const [, username, password, streamId] = xtreamMatch;
      
      // Extraer información de series si es aplicable
      if (entry.type === 'series') {
        this.extractSeriesInfo(entry, url);
      }
    }
  }

  private static extractSeriesInfo(entry: M3UEntry, url: string): void {
    // Extraer temporada y episodio de la URL o título
    const seasonMatch = entry.title.match(/[Ss](\d+)[Ee](\d+)/);
    if (seasonMatch) {
      entry.metadata = {
        ...entry.metadata,
        season: parseInt(seasonMatch[1]),
        episode: parseInt(seasonMatch[2]),
      };
    }

    // Extraer ID de serie para agrupar episodios
    const seriesIdMatch = url.match(/series\/(\d+)/);
    if (seriesIdMatch) {
      entry.metadata = {
        ...entry.metadata,
        seriesId: seriesIdMatch[1],
      };
    }
  }

  static groupSeriesByShow(entries: M3UEntry[]): SeriesInfo[] {
    const seriesMap = new Map<string, SeriesInfo>();

    entries
      .filter(entry => entry.type === 'series' && entry.metadata?.seriesId)
      .forEach(entry => {
        const seriesId = entry.metadata!.seriesId!;
        const season = entry.metadata!.season || 1;
        const episode = entry.metadata!.episode || 1;

        if (!seriesMap.has(seriesId)) {
          seriesMap.set(seriesId, {
            id: seriesId,
            title: this.extractSeriesTitle(entry.title),
            seasons: [],
            totalEpisodes: 0,
            logo: entry.logo,
            plot: entry.metadata?.plot,
            year: entry.metadata?.year,
            genre: entry.metadata?.genre,
          });
        }

        const series = seriesMap.get(seriesId)!;
        let seasonInfo = series.seasons.find(s => s.number === season);

        if (!seasonInfo) {
          seasonInfo = { number: season, episodes: [] };
          series.seasons.push(seasonInfo);
        }

        seasonInfo.episodes.push({
          number: episode,
          title: entry.title,
          url: entry.url,
          duration: entry.duration,
          plot: entry.metadata?.plot,
        });

        series.totalEpisodes++;
      });

    // Ordenar temporadas y episodios
    seriesMap.forEach(series => {
      series.seasons.sort((a, b) => a.number - b.number);
      series.seasons.forEach(season => {
        season.episodes.sort((a, b) => a.number - b.number);
      });
    });

    return Array.from(seriesMap.values());
  }

  static extractSeriesTitle(episodeTitle: string): string {
    // Remover información de temporada/episodio para obtener el título de la serie
    return episodeTitle
      .replace(/[Ss]\d+[Ee]\d+.*$/, '')
      .replace(/\d+x\d+.*$/, '')
      .replace(/Season\s*\d+.*$/i, '')
      .replace(/Episode\s*\d+.*$/i, '')
      .trim();
  }
}
