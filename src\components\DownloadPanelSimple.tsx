import React from 'react';
import styled from 'styled-components';
import { DownloadQueue } from '../types';

const Container = styled.div`
  height: 100%;
  background: #2d2d2d;
  display: flex;
  flex-direction: column;
`;

const Header = styled.div`
  padding: 15px;
  background: #3d3d3d;
  border-bottom: 1px solid #404040;
  display: flex;
  justify-content: space-between;
  align-items: center;
`;

const Title = styled.h3`
  margin: 0;
  color: #ffffff;
  font-size: 16px;
`;

const Controls = styled.div`
  display: flex;
  gap: 10px;
`;

const Button = styled.button`
  background: #0078d4;
  color: white;
  border: none;
  padding: 8px 12px;
  border-radius: 4px;
  cursor: pointer;
  font-size: 12px;
  
  &:hover {
    background: #106ebe;
  }
  
  &:disabled {
    background: #555;
    cursor: not-allowed;
  }
`;

const Content = styled.div`
  flex: 1;
  padding: 15px;
  overflow-y: auto;
`;

const StatsContainer = styled.div`
  display: grid;
  grid-template-columns: repeat(auto-fit, minmax(120px, 1fr));
  gap: 10px;
  margin-bottom: 20px;
`;

const StatCard = styled.div`
  background: #404040;
  padding: 15px;
  border-radius: 8px;
  text-align: center;
`;

const StatValue = styled.div`
  font-size: 24px;
  font-weight: bold;
  color: #0078d4;
  margin-bottom: 5px;
`;

const StatLabel = styled.div`
  font-size: 12px;
  color: #ccc;
`;

const TaskItem = styled.div`
  background: #404040;
  padding: 15px;
  margin: 10px 0;
  border-radius: 8px;
  border-left: 4px solid #0078d4;
`;

const TaskHeader = styled.div`
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 10px;
`;

const TaskTitle = styled.div`
  color: white;
  font-weight: bold;
  font-size: 14px;
`;

const TaskStatus = styled.span<{ status: string }>`
  padding: 4px 8px;
  border-radius: 12px;
  font-size: 11px;
  font-weight: bold;
  background: ${props => {
    switch (props.status) {
      case 'completed': return '#28a745';
      case 'downloading': return '#007bff';
      case 'paused': return '#ffc107';
      case 'failed': return '#dc3545';
      default: return '#6c757d';
    }
  }};
  color: white;
`;

const ProgressBar = styled.div`
  width: 100%;
  height: 6px;
  background: #555;
  border-radius: 3px;
  overflow: hidden;
  margin: 10px 0;
`;

const ProgressFill = styled.div<{ progress: number }>`
  height: 100%;
  background: linear-gradient(90deg, #0078d4, #00bcf2);
  width: ${props => props.progress}%;
  transition: width 0.3s ease;
`;

const TaskDetails = styled.div`
  display: flex;
  justify-content: space-between;
  font-size: 12px;
  color: #ccc;
`;

interface Props {
  downloadQueue: DownloadQueue;
  onStartQueue: () => void;
  onStopQueue: () => void;
  onPauseTask: (taskId: string) => void;
  onResumeTask: (taskId: string) => void;
  onRemoveTask: (taskId: string) => void;
  onClearCompleted: () => void;
}

const DownloadPanelSimple: React.FC<Props> = ({
  downloadQueue,
  onStartQueue,
  onStopQueue,
  onPauseTask,
  onResumeTask,
  onRemoveTask,
  onClearCompleted
}) => {
  // Datos de ejemplo para la demo
  const mockTasks = [
    {
      id: '1',
      fileName: 'Película Ejemplo.mp4',
      status: 'completed' as const,
      progress: 100,
      speed: 0,
      size: '2.1 GB',
      timeRemaining: '0s'
    },
    {
      id: '2',
      fileName: 'Serie S01E01.mp4',
      status: 'downloading' as const,
      progress: 65,
      speed: 5.2,
      size: '850 MB',
      timeRemaining: '2m 15s'
    },
    {
      id: '3',
      fileName: 'Documental.mp4',
      status: 'paused' as const,
      progress: 30,
      speed: 0,
      size: '1.5 GB',
      timeRemaining: '∞'
    }
  ];

  const stats = {
    total: mockTasks.length,
    completed: mockTasks.filter(t => t.status === 'completed').length,
    downloading: mockTasks.filter(t => t.status === 'downloading').length,
    failed: 0 // No hay tareas fallidas en el mock
  };

  const formatSpeed = (speed: number) => {
    if (speed === 0) return '0 MB/s';
    return `${speed.toFixed(1)} MB/s`;
  };

  const getStatusText = (status: string) => {
    switch (status) {
      case 'completed': return 'Completado';
      case 'downloading': return 'Descargando';
      case 'paused': return 'Pausado';
      case 'failed': return 'Error';
      default: return 'Pendiente';
    }
  };

  return (
    <Container>
      <Header>
        <Title>📥 Panel de Descargas</Title>
        <Controls>
          <Button onClick={onStartQueue} disabled={downloadQueue.isRunning}>
            ▶️ Iniciar
          </Button>
          <Button onClick={onStopQueue} disabled={!downloadQueue.isRunning}>
            ⏹️ Detener
          </Button>
          <Button onClick={onClearCompleted}>
            🧹 Limpiar
          </Button>
        </Controls>
      </Header>
      
      <Content>
        <StatsContainer>
          <StatCard>
            <StatValue>{stats.total}</StatValue>
            <StatLabel>Total</StatLabel>
          </StatCard>
          <StatCard>
            <StatValue>{stats.downloading}</StatValue>
            <StatLabel>Descargando</StatLabel>
          </StatCard>
          <StatCard>
            <StatValue>{stats.completed}</StatValue>
            <StatLabel>Completadas</StatLabel>
          </StatCard>
          <StatCard>
            <StatValue>{stats.failed}</StatValue>
            <StatLabel>Fallidas</StatLabel>
          </StatCard>
        </StatsContainer>

        <div>
          <h4 style={{ color: 'white', marginBottom: '15px' }}>
            Cola de Descargas ({mockTasks.length})
          </h4>
          
          {mockTasks.map(task => (
            <TaskItem key={task.id}>
              <TaskHeader>
                <TaskTitle>{task.fileName}</TaskTitle>
                <TaskStatus status={task.status}>
                  {getStatusText(task.status)}
                </TaskStatus>
              </TaskHeader>
              
              <ProgressBar>
                <ProgressFill progress={task.progress} />
              </ProgressBar>
              
              <TaskDetails>
                <span>{task.progress}% • {task.size}</span>
                <span>{formatSpeed(task.speed)} • {task.timeRemaining}</span>
              </TaskDetails>
              
              <div style={{ marginTop: '10px', display: 'flex', gap: '5px' }}>
                {task.status === 'downloading' && (
                  <Button onClick={() => onPauseTask(task.id)}>⏸️</Button>
                )}
                {task.status === 'paused' && (
                  <Button onClick={() => onResumeTask(task.id)}>▶️</Button>
                )}
                <Button onClick={() => onRemoveTask(task.id)}>🗑️</Button>
              </div>
            </TaskItem>
          ))}
          
          {mockTasks.length === 0 && (
            <div style={{ 
              color: '#888', 
              textAlign: 'center', 
              marginTop: '50px',
              fontStyle: 'italic'
            }}>
              No hay descargas en la cola.<br />
              Selecciona contenido desde el panel M3U para comenzar.
            </div>
          )}
        </div>
      </Content>
    </Container>
  );
};

export default DownloadPanelSimple;
