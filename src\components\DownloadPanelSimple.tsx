import React, { useState, useEffect } from 'react';
import styled from 'styled-components';
import { DownloadQueue, DownloadTask } from '../types';
import { DownloadService } from '../services/DownloadService';

const Container = styled.div`
  height: 100%;
  background: #2d2d2d;
  display: flex;
  flex-direction: column;
`;

const Header = styled.div`
  padding: 15px;
  background: #3d3d3d;
  border-bottom: 1px solid #404040;
  display: flex;
  justify-content: space-between;
  align-items: center;
`;

const Title = styled.h3`
  margin: 0;
  color: #ffffff;
  font-size: 16px;
`;

const Controls = styled.div`
  display: flex;
  gap: 10px;
`;

const Button = styled.button`
  background: #0078d4;
  color: white;
  border: none;
  padding: 8px 12px;
  border-radius: 4px;
  cursor: pointer;
  font-size: 12px;
  
  &:hover {
    background: #106ebe;
  }
  
  &:disabled {
    background: #555;
    cursor: not-allowed;
  }
`;

const Content = styled.div`
  flex: 1;
  padding: 15px;
  overflow-y: auto;
`;

const StatsContainer = styled.div`
  display: grid;
  grid-template-columns: repeat(auto-fit, minmax(120px, 1fr));
  gap: 10px;
  margin-bottom: 20px;
`;

const StatCard = styled.div`
  background: #404040;
  padding: 15px;
  border-radius: 8px;
  text-align: center;
`;

const StatValue = styled.div`
  font-size: 24px;
  font-weight: bold;
  color: #0078d4;
  margin-bottom: 5px;
`;

const StatLabel = styled.div`
  font-size: 12px;
  color: #ccc;
`;

const TaskItem = styled.div`
  background: #404040;
  padding: 15px;
  margin: 10px 0;
  border-radius: 8px;
  border-left: 4px solid #0078d4;
`;

const TaskHeader = styled.div`
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 10px;
`;

const TaskTitle = styled.div`
  color: white;
  font-weight: bold;
  font-size: 14px;
`;

const TaskStatus = styled.span<{ status: string }>`
  padding: 4px 8px;
  border-radius: 12px;
  font-size: 11px;
  font-weight: bold;
  background: ${props => {
    switch (props.status) {
      case 'completed': return '#28a745';
      case 'downloading': return '#007bff';
      case 'paused': return '#ffc107';
      case 'failed': return '#dc3545';
      default: return '#6c757d';
    }
  }};
  color: white;
`;

const ProgressBar = styled.div`
  width: 100%;
  height: 6px;
  background: #555;
  border-radius: 3px;
  overflow: hidden;
  margin: 10px 0;
`;

const ProgressFill = styled.div<{ progress: number }>`
  height: 100%;
  background: linear-gradient(90deg, #0078d4, #00bcf2);
  width: ${props => props.progress}%;
  transition: width 0.3s ease;
`;

const TaskDetails = styled.div`
  display: flex;
  justify-content: space-between;
  font-size: 12px;
  color: #ccc;
`;

interface Props {
  // Props simplificadas - el servicio maneja la lógica internamente
}

const DownloadPanelSimple: React.FC<Props> = () => {
  const [downloadService] = useState(() => new DownloadService());
  const [downloadQueue, setDownloadQueue] = useState<DownloadQueue>(() => downloadService.getQueue());

  useEffect(() => {
    // Suscribirse a cambios en la cola de descargas
    const unsubscribe = downloadService.subscribe((queue) => {
      setDownloadQueue(queue);
    });

    return unsubscribe;
  }, [downloadService]);

  const handlePauseTask = (taskId: string) => {
    downloadService.pauseTask(taskId);
  };

  const handleResumeTask = (taskId: string) => {
    downloadService.resumeTask(taskId);
  };

  const handleCancelTask = (taskId: string) => {
    downloadService.cancelTask(taskId);
  };

  const handleRemoveTask = (taskId: string) => {
    downloadService.removeTask(taskId);
  };

  const handleClearCompleted = () => {
    downloadService.clearCompletedTasks();
  };

  const getTaskStatusIcon = (status: DownloadTask['status']): string => {
    switch (status) {
      case 'pending': return '⏳';
      case 'downloading': return '⬇️';
      case 'completed': return '✅';
      case 'failed': return '❌';
      case 'paused': return '⏸️';
      case 'cancelled': return '🚫';
      default: return '❓';
    }
  };

  const getTaskStatusColor = (status: DownloadTask['status']): string => {
    switch (status) {
      case 'pending': return '#ffa500';
      case 'downloading': return '#0078d4';
      case 'completed': return '#28a745';
      case 'failed': return '#dc3545';
      case 'paused': return '#6c757d';
      case 'cancelled': return '#6c757d';
      default: return '#ccc';
    }
  };

  const formatSpeed = (speed: number): string => {
    if (speed === 0) return '-';
    return `${speed.toFixed(1)} MB/s`;
  };

  const formatETA = (eta: number): string => {
    if (eta === 0) return '-';
    const seconds = Math.floor(eta / 1000);
    const minutes = Math.floor(seconds / 60);
    const hours = Math.floor(minutes / 60);

    if (hours > 0) return `${hours}h ${minutes % 60}m`;
    if (minutes > 0) return `${minutes}m ${seconds % 60}s`;
    return `${seconds}s`;
  };

  const stats = downloadService.getStats();

  const formatSpeed = (speed: number) => {
    if (speed === 0) return '0 MB/s';
    return `${speed.toFixed(1)} MB/s`;
  };

  const getStatusText = (status: string) => {
    switch (status) {
      case 'completed': return 'Completado';
      case 'downloading': return 'Descargando';
      case 'paused': return 'Pausado';
      case 'failed': return 'Error';
      default: return 'Pendiente';
    }
  };

  return (
    <Container>
      <Header>
        <Title>📥 Panel de Descargas</Title>
        <Controls>
          <Button onClick={handleClearCompleted} disabled={stats.completed === 0 && stats.failed === 0}>
            🧹 Limpiar Completadas
          </Button>
        </Controls>
      </Header>
      
      <Content>
        <StatsContainer>
          <StatCard>
            <StatValue>{stats.total}</StatValue>
            <StatLabel>Total</StatLabel>
          </StatCard>
          <StatCard>
            <StatValue>{stats.downloading}</StatValue>
            <StatLabel>Descargando</StatLabel>
          </StatCard>
          <StatCard>
            <StatValue>{stats.completed}</StatValue>
            <StatLabel>Completadas</StatLabel>
          </StatCard>
          <StatCard>
            <StatValue>{stats.failed}</StatValue>
            <StatLabel>Fallidas</StatLabel>
          </StatCard>
        </StatsContainer>

        <div>
          <h4 style={{ color: 'white', marginBottom: '15px' }}>
            Cola de Descargas ({downloadQueue.tasks.length})
          </h4>

          {downloadQueue.tasks.length === 0 ? (
            <div style={{
              color: '#888',
              textAlign: 'center',
              marginTop: '50px',
              fontStyle: 'italic'
            }}>
              No hay descargas en la cola.<br />
              Selecciona contenido desde el panel M3U para comenzar.
            </div>
          ) : (
            downloadQueue.tasks.map(task => (
              <TaskItem
                key={task.id}
                style={{ borderLeftColor: getTaskStatusColor(task.status) }}
              >
                <TaskHeader>
                  <TaskTitle>
                    {getTaskStatusIcon(task.status)} {task.entry.title}
                  </TaskTitle>
                  <TaskStatus status={task.status}>
                    {task.status.charAt(0).toUpperCase() + task.status.slice(1)}
                  </TaskStatus>
                </TaskHeader>

                <ProgressBar>
                  <ProgressFill progress={task.progress} />
                </ProgressBar>

                <TaskDetails>
                  <span>{task.progress}% • {task.destinationPath.split('/').pop()}</span>
                  <span>{formatSpeed(task.speed)} • {formatETA(task.eta)}</span>
                </TaskDetails>

                {task.error && (
                  <div style={{
                    color: '#dc3545',
                    fontSize: '12px',
                    marginTop: '5px',
                    background: '#2d1b1b',
                    padding: '5px',
                    borderRadius: '3px'
                  }}>
                    ❌ {task.error}
                  </div>
                )}

                <div style={{ marginTop: '10px', display: 'flex', gap: '5px' }}>
                  {task.status === 'downloading' && (
                    <Button onClick={() => handlePauseTask(task.id)}>⏸️ Pausar</Button>
                  )}
                  {task.status === 'paused' && (
                    <Button onClick={() => handleResumeTask(task.id)}>▶️ Reanudar</Button>
                  )}
                  {(task.status === 'pending' || task.status === 'downloading') && (
                    <Button onClick={() => handleCancelTask(task.id)}>🚫 Cancelar</Button>
                  )}
                  {(task.status === 'completed' || task.status === 'failed' || task.status === 'cancelled') && (
                    <Button onClick={() => handleRemoveTask(task.id)}>🗑️ Eliminar</Button>
                  )}
                </div>
              </TaskItem>
            ))
          )}
          
          {mockTasks.length === 0 && (
            <div style={{ 
              color: '#888', 
              textAlign: 'center', 
              marginTop: '50px',
              fontStyle: 'italic'
            }}>
              No hay descargas en la cola.<br />
              Selecciona contenido desde el panel M3U para comenzar.
            </div>
          )}
        </div>
      </Content>
    </Container>
  );
};

export default DownloadPanelSimple;
