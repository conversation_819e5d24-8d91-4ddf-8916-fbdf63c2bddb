/*! For license information please see bundle.js.LICENSE.txt */
(()=>{var e,t,n={221:(e,t,n)=>{"use strict";var r=n(540);function a(e){var t="https://react.dev/errors/"+e;if(1<arguments.length){t+="?args[]="+encodeURIComponent(arguments[1]);for(var n=2;n<arguments.length;n++)t+="&args[]="+encodeURIComponent(arguments[n])}return"Minified React error #"+e+"; visit "+t+" for the full message or use the non-minified dev environment for full errors and additional helpful warnings."}function l(){}var o={d:{f:l,r:function(){throw Error(a(522))},D:l,C:l,L:l,m:l,X:l,S:l,M:l},p:0,findDOMNode:null},i=Symbol.for("react.portal"),u=r.__CLIENT_INTERNALS_DO_NOT_USE_OR_WARN_USERS_THEY_CANNOT_UPGRADE;function s(e,t){return"font"===e?"":"string"==typeof t?"use-credentials"===t?t:"":void 0}t.__DOM_INTERNALS_DO_NOT_USE_OR_WARN_USERS_THEY_CANNOT_UPGRADE=o,t.createPortal=function(e,t){var n=2<arguments.length&&void 0!==arguments[2]?arguments[2]:null;if(!t||1!==t.nodeType&&9!==t.nodeType&&11!==t.nodeType)throw Error(a(299));return function(e,t,n){var r=3<arguments.length&&void 0!==arguments[3]?arguments[3]:null;return{$$typeof:i,key:null==r?null:""+r,children:e,containerInfo:t,implementation:n}}(e,t,null,n)},t.flushSync=function(e){var t=u.T,n=o.p;try{if(u.T=null,o.p=2,e)return e()}finally{u.T=t,o.p=n,o.d.f()}},t.preconnect=function(e,t){"string"==typeof e&&(t=t?"string"==typeof(t=t.crossOrigin)?"use-credentials"===t?t:"":void 0:null,o.d.C(e,t))},t.prefetchDNS=function(e){"string"==typeof e&&o.d.D(e)},t.preinit=function(e,t){if("string"==typeof e&&t&&"string"==typeof t.as){var n=t.as,r=s(n,t.crossOrigin),a="string"==typeof t.integrity?t.integrity:void 0,l="string"==typeof t.fetchPriority?t.fetchPriority:void 0;"style"===n?o.d.S(e,"string"==typeof t.precedence?t.precedence:void 0,{crossOrigin:r,integrity:a,fetchPriority:l}):"script"===n&&o.d.X(e,{crossOrigin:r,integrity:a,fetchPriority:l,nonce:"string"==typeof t.nonce?t.nonce:void 0})}},t.preinitModule=function(e,t){if("string"==typeof e)if("object"==typeof t&&null!==t){if(null==t.as||"script"===t.as){var n=s(t.as,t.crossOrigin);o.d.M(e,{crossOrigin:n,integrity:"string"==typeof t.integrity?t.integrity:void 0,nonce:"string"==typeof t.nonce?t.nonce:void 0})}}else null==t&&o.d.M(e)},t.preload=function(e,t){if("string"==typeof e&&"object"==typeof t&&null!==t&&"string"==typeof t.as){var n=t.as,r=s(n,t.crossOrigin);o.d.L(e,n,{crossOrigin:r,integrity:"string"==typeof t.integrity?t.integrity:void 0,nonce:"string"==typeof t.nonce?t.nonce:void 0,type:"string"==typeof t.type?t.type:void 0,fetchPriority:"string"==typeof t.fetchPriority?t.fetchPriority:void 0,referrerPolicy:"string"==typeof t.referrerPolicy?t.referrerPolicy:void 0,imageSrcSet:"string"==typeof t.imageSrcSet?t.imageSrcSet:void 0,imageSizes:"string"==typeof t.imageSizes?t.imageSizes:void 0,media:"string"==typeof t.media?t.media:void 0})}},t.preloadModule=function(e,t){if("string"==typeof e)if(t){var n=s(t.as,t.crossOrigin);o.d.m(e,{as:"string"==typeof t.as&&"script"!==t.as?t.as:void 0,crossOrigin:n,integrity:"string"==typeof t.integrity?t.integrity:void 0})}else o.d.m(e)},t.requestFormReset=function(e){o.d.r(e)},t.unstable_batchedUpdates=function(e,t){return e(t)},t.useFormState=function(e,t,n){return u.H.useFormState(e,t,n)},t.useFormStatus=function(){return u.H.useHostTransitionStatus()},t.version="19.1.0"},247:(e,t,n)=>{"use strict";var r=n(982),a=n(540),l=n(961);function o(e){var t="https://react.dev/errors/"+e;if(1<arguments.length){t+="?args[]="+encodeURIComponent(arguments[1]);for(var n=2;n<arguments.length;n++)t+="&args[]="+encodeURIComponent(arguments[n])}return"Minified React error #"+e+"; visit "+t+" for the full message or use the non-minified dev environment for full errors and additional helpful warnings."}function i(e){var t=e,n=e;if(e.alternate)for(;t.return;)t=t.return;else{e=t;do{!!(4098&(t=e).flags)&&(n=t.return),e=t.return}while(e)}return 3===t.tag?n:null}function u(e){if(13===e.tag){var t=e.memoizedState;if(null===t&&null!==(e=e.alternate)&&(t=e.memoizedState),null!==t)return t.dehydrated}return null}function s(e){if(i(e)!==e)throw Error(o(188))}function c(e){var t=e.tag;if(5===t||26===t||27===t||6===t)return e;for(e=e.child;null!==e;){if(null!==(t=c(e)))return t;e=e.sibling}return null}var f=Object.assign,d=Symbol.for("react.element"),p=Symbol.for("react.transitional.element"),h=Symbol.for("react.portal"),m=Symbol.for("react.fragment"),g=Symbol.for("react.strict_mode"),y=Symbol.for("react.profiler"),v=Symbol.for("react.provider"),b=Symbol.for("react.consumer"),S=Symbol.for("react.context"),k=Symbol.for("react.forward_ref"),w=Symbol.for("react.suspense"),x=Symbol.for("react.suspense_list"),E=Symbol.for("react.memo"),C=Symbol.for("react.lazy");Symbol.for("react.scope");var z=Symbol.for("react.activity");Symbol.for("react.legacy_hidden"),Symbol.for("react.tracing_marker");var P=Symbol.for("react.memo_cache_sentinel");Symbol.for("react.view_transition");var _=Symbol.iterator;function N(e){return null===e||"object"!=typeof e?null:"function"==typeof(e=_&&e[_]||e["@@iterator"])?e:null}var L=Symbol.for("react.client.reference");function T(e){if(null==e)return null;if("function"==typeof e)return e.$$typeof===L?null:e.displayName||e.name||null;if("string"==typeof e)return e;switch(e){case m:return"Fragment";case y:return"Profiler";case g:return"StrictMode";case w:return"Suspense";case x:return"SuspenseList";case z:return"Activity"}if("object"==typeof e)switch(e.$$typeof){case h:return"Portal";case S:return(e.displayName||"Context")+".Provider";case b:return(e._context.displayName||"Context")+".Consumer";case k:var t=e.render;return(e=e.displayName)||(e=""!==(e=t.displayName||t.name||"")?"ForwardRef("+e+")":"ForwardRef"),e;case E:return null!==(t=e.displayName||null)?t:T(e.type)||"Memo";case C:t=e._payload,e=e._init;try{return T(e(t))}catch(e){}}return null}var A=Array.isArray,R=a.__CLIENT_INTERNALS_DO_NOT_USE_OR_WARN_USERS_THEY_CANNOT_UPGRADE,O=l.__DOM_INTERNALS_DO_NOT_USE_OR_WARN_USERS_THEY_CANNOT_UPGRADE,I={pending:!1,data:null,method:null,action:null},D=[],M=-1;function F(e){return{current:e}}function j(e){0>M||(e.current=D[M],D[M]=null,M--)}function $(e,t){M++,D[M]=e.current,e.current=t}var U=F(null),H=F(null),B=F(null),V=F(null);function W(e,t){switch($(B,t),$(H,e),$(U,null),t.nodeType){case 9:case 11:e=(e=t.documentElement)&&(e=e.namespaceURI)?rf(e):0;break;default:if(e=t.tagName,t=t.namespaceURI)e=af(t=rf(t),e);else switch(e){case"svg":e=1;break;case"math":e=2;break;default:e=0}}j(U),$(U,e)}function Q(){j(U),j(H),j(B)}function q(e){null!==e.memoizedState&&$(V,e);var t=U.current,n=af(t,e.type);t!==n&&($(H,e),$(U,n))}function G(e){H.current===e&&(j(U),j(H)),V.current===e&&(j(V),Gf._currentValue=I)}var Y=Object.prototype.hasOwnProperty,K=r.unstable_scheduleCallback,X=r.unstable_cancelCallback,Z=r.unstable_shouldYield,J=r.unstable_requestPaint,ee=r.unstable_now,te=r.unstable_getCurrentPriorityLevel,ne=r.unstable_ImmediatePriority,re=r.unstable_UserBlockingPriority,ae=r.unstable_NormalPriority,le=r.unstable_LowPriority,oe=r.unstable_IdlePriority,ie=r.log,ue=r.unstable_setDisableYieldValue,se=null,ce=null;function fe(e){if("function"==typeof ie&&ue(e),ce&&"function"==typeof ce.setStrictMode)try{ce.setStrictMode(se,e)}catch(e){}}var de=Math.clz32?Math.clz32:function(e){return 0==(e>>>=0)?32:31-(pe(e)/he|0)|0},pe=Math.log,he=Math.LN2,me=256,ge=4194304;function ye(e){var t=42&e;if(0!==t)return t;switch(e&-e){case 1:return 1;case 2:return 2;case 4:return 4;case 8:return 8;case 16:return 16;case 32:return 32;case 64:return 64;case 128:return 128;case 256:case 512:case 1024:case 2048:case 4096:case 8192:case 16384:case 32768:case 65536:case 131072:case 262144:case 524288:case 1048576:case 2097152:return 4194048&e;case 4194304:case 8388608:case 16777216:case 33554432:return 62914560&e;case 67108864:return 67108864;case 134217728:return 134217728;case 268435456:return 268435456;case 536870912:return 536870912;case 1073741824:return 0;default:return e}}function ve(e,t,n){var r=e.pendingLanes;if(0===r)return 0;var a=0,l=e.suspendedLanes,o=e.pingedLanes;e=e.warmLanes;var i=134217727&r;return 0!==i?0!==(r=i&~l)?a=ye(r):0!==(o&=i)?a=ye(o):n||0!==(n=i&~e)&&(a=ye(n)):0!==(i=r&~l)?a=ye(i):0!==o?a=ye(o):n||0!==(n=r&~e)&&(a=ye(n)),0===a?0:0!==t&&t!==a&&0===(t&l)&&((l=a&-a)>=(n=t&-t)||32===l&&4194048&n)?t:a}function be(e,t){return 0===(e.pendingLanes&~(e.suspendedLanes&~e.pingedLanes)&t)}function Se(e,t){switch(e){case 1:case 2:case 4:case 8:case 64:return t+250;case 16:case 32:case 128:case 256:case 512:case 1024:case 2048:case 4096:case 8192:case 16384:case 32768:case 65536:case 131072:case 262144:case 524288:case 1048576:case 2097152:return t+5e3;default:return-1}}function ke(){var e=me;return!(4194048&(me<<=1))&&(me=256),e}function we(){var e=ge;return!(62914560&(ge<<=1))&&(ge=4194304),e}function xe(e){for(var t=[],n=0;31>n;n++)t.push(e);return t}function Ee(e,t){e.pendingLanes|=t,268435456!==t&&(e.suspendedLanes=0,e.pingedLanes=0,e.warmLanes=0)}function Ce(e,t,n){e.pendingLanes|=t,e.suspendedLanes&=~t;var r=31-de(t);e.entangledLanes|=t,e.entanglements[r]=1073741824|e.entanglements[r]|4194090&n}function ze(e,t){var n=e.entangledLanes|=t;for(e=e.entanglements;n;){var r=31-de(n),a=1<<r;a&t|e[r]&t&&(e[r]|=t),n&=~a}}function Pe(e){switch(e){case 2:e=1;break;case 8:e=4;break;case 32:e=16;break;case 256:case 512:case 1024:case 2048:case 4096:case 8192:case 16384:case 32768:case 65536:case 131072:case 262144:case 524288:case 1048576:case 2097152:case 4194304:case 8388608:case 16777216:case 33554432:e=128;break;case 268435456:e=134217728;break;default:e=0}return e}function _e(e){return 2<(e&=-e)?8<e?134217727&e?32:268435456:8:2}function Ne(){var e=O.p;return 0!==e?e:void 0===(e=window.event)?32:id(e.type)}var Le=Math.random().toString(36).slice(2),Te="__reactFiber$"+Le,Ae="__reactProps$"+Le,Re="__reactContainer$"+Le,Oe="__reactEvents$"+Le,Ie="__reactListeners$"+Le,De="__reactHandles$"+Le,Me="__reactResources$"+Le,Fe="__reactMarker$"+Le;function je(e){delete e[Te],delete e[Ae],delete e[Oe],delete e[Ie],delete e[De]}function $e(e){var t=e[Te];if(t)return t;for(var n=e.parentNode;n;){if(t=n[Re]||n[Te]){if(n=t.alternate,null!==t.child||null!==n&&null!==n.child)for(e=bf(e);null!==e;){if(n=e[Te])return n;e=bf(e)}return t}n=(e=n).parentNode}return null}function Ue(e){if(e=e[Te]||e[Re]){var t=e.tag;if(5===t||6===t||13===t||26===t||27===t||3===t)return e}return null}function He(e){var t=e.tag;if(5===t||26===t||27===t||6===t)return e.stateNode;throw Error(o(33))}function Be(e){var t=e[Me];return t||(t=e[Me]={hoistableStyles:new Map,hoistableScripts:new Map}),t}function Ve(e){e[Fe]=!0}var We=new Set,Qe={};function qe(e,t){Ge(e,t),Ge(e+"Capture",t)}function Ge(e,t){for(Qe[e]=t,e=0;e<t.length;e++)We.add(t[e])}var Ye,Ke,Xe=RegExp("^[:A-Z_a-z\\u00C0-\\u00D6\\u00D8-\\u00F6\\u00F8-\\u02FF\\u0370-\\u037D\\u037F-\\u1FFF\\u200C-\\u200D\\u2070-\\u218F\\u2C00-\\u2FEF\\u3001-\\uD7FF\\uF900-\\uFDCF\\uFDF0-\\uFFFD][:A-Z_a-z\\u00C0-\\u00D6\\u00D8-\\u00F6\\u00F8-\\u02FF\\u0370-\\u037D\\u037F-\\u1FFF\\u200C-\\u200D\\u2070-\\u218F\\u2C00-\\u2FEF\\u3001-\\uD7FF\\uF900-\\uFDCF\\uFDF0-\\uFFFD\\-.0-9\\u00B7\\u0300-\\u036F\\u203F-\\u2040]*$"),Ze={},Je={};function et(e,t,n){if(a=t,Y.call(Je,a)||!Y.call(Ze,a)&&(Xe.test(a)?Je[a]=!0:(Ze[a]=!0,0)))if(null===n)e.removeAttribute(t);else{switch(typeof n){case"undefined":case"function":case"symbol":return void e.removeAttribute(t);case"boolean":var r=t.toLowerCase().slice(0,5);if("data-"!==r&&"aria-"!==r)return void e.removeAttribute(t)}e.setAttribute(t,""+n)}var a}function tt(e,t,n){if(null===n)e.removeAttribute(t);else{switch(typeof n){case"undefined":case"function":case"symbol":case"boolean":return void e.removeAttribute(t)}e.setAttribute(t,""+n)}}function nt(e,t,n,r){if(null===r)e.removeAttribute(n);else{switch(typeof r){case"undefined":case"function":case"symbol":case"boolean":return void e.removeAttribute(n)}e.setAttributeNS(t,n,""+r)}}function rt(e){if(void 0===Ye)try{throw Error()}catch(e){var t=e.stack.trim().match(/\n( *(at )?)/);Ye=t&&t[1]||"",Ke=-1<e.stack.indexOf("\n    at")?" (<anonymous>)":-1<e.stack.indexOf("@")?"@unknown:0:0":""}return"\n"+Ye+e+Ke}var at=!1;function lt(e,t){if(!e||at)return"";at=!0;var n=Error.prepareStackTrace;Error.prepareStackTrace=void 0;try{var r={DetermineComponentFrameRoot:function(){try{if(t){var n=function(){throw Error()};if(Object.defineProperty(n.prototype,"props",{set:function(){throw Error()}}),"object"==typeof Reflect&&Reflect.construct){try{Reflect.construct(n,[])}catch(e){var r=e}Reflect.construct(e,[],n)}else{try{n.call()}catch(e){r=e}e.call(n.prototype)}}else{try{throw Error()}catch(e){r=e}(n=e())&&"function"==typeof n.catch&&n.catch(function(){})}}catch(e){if(e&&r&&"string"==typeof e.stack)return[e.stack,r.stack]}return[null,null]}};r.DetermineComponentFrameRoot.displayName="DetermineComponentFrameRoot";var a=Object.getOwnPropertyDescriptor(r.DetermineComponentFrameRoot,"name");a&&a.configurable&&Object.defineProperty(r.DetermineComponentFrameRoot,"name",{value:"DetermineComponentFrameRoot"});var l=r.DetermineComponentFrameRoot(),o=l[0],i=l[1];if(o&&i){var u=o.split("\n"),s=i.split("\n");for(a=r=0;r<u.length&&!u[r].includes("DetermineComponentFrameRoot");)r++;for(;a<s.length&&!s[a].includes("DetermineComponentFrameRoot");)a++;if(r===u.length||a===s.length)for(r=u.length-1,a=s.length-1;1<=r&&0<=a&&u[r]!==s[a];)a--;for(;1<=r&&0<=a;r--,a--)if(u[r]!==s[a]){if(1!==r||1!==a)do{if(r--,0>--a||u[r]!==s[a]){var c="\n"+u[r].replace(" at new "," at ");return e.displayName&&c.includes("<anonymous>")&&(c=c.replace("<anonymous>",e.displayName)),c}}while(1<=r&&0<=a);break}}}finally{at=!1,Error.prepareStackTrace=n}return(n=e?e.displayName||e.name:"")?rt(n):""}function ot(e){switch(e.tag){case 26:case 27:case 5:return rt(e.type);case 16:return rt("Lazy");case 13:return rt("Suspense");case 19:return rt("SuspenseList");case 0:case 15:return lt(e.type,!1);case 11:return lt(e.type.render,!1);case 1:return lt(e.type,!0);case 31:return rt("Activity");default:return""}}function it(e){try{var t="";do{t+=ot(e),e=e.return}while(e);return t}catch(e){return"\nError generating stack: "+e.message+"\n"+e.stack}}function ut(e){switch(typeof e){case"bigint":case"boolean":case"number":case"string":case"undefined":case"object":return e;default:return""}}function st(e){var t=e.type;return(e=e.nodeName)&&"input"===e.toLowerCase()&&("checkbox"===t||"radio"===t)}function ct(e){e._valueTracker||(e._valueTracker=function(e){var t=st(e)?"checked":"value",n=Object.getOwnPropertyDescriptor(e.constructor.prototype,t),r=""+e[t];if(!e.hasOwnProperty(t)&&void 0!==n&&"function"==typeof n.get&&"function"==typeof n.set){var a=n.get,l=n.set;return Object.defineProperty(e,t,{configurable:!0,get:function(){return a.call(this)},set:function(e){r=""+e,l.call(this,e)}}),Object.defineProperty(e,t,{enumerable:n.enumerable}),{getValue:function(){return r},setValue:function(e){r=""+e},stopTracking:function(){e._valueTracker=null,delete e[t]}}}}(e))}function ft(e){if(!e)return!1;var t=e._valueTracker;if(!t)return!0;var n=t.getValue(),r="";return e&&(r=st(e)?e.checked?"true":"false":e.value),(e=r)!==n&&(t.setValue(e),!0)}function dt(e){if(void 0===(e=e||("undefined"!=typeof document?document:void 0)))return null;try{return e.activeElement||e.body}catch(t){return e.body}}var pt=/[\n"\\]/g;function ht(e){return e.replace(pt,function(e){return"\\"+e.charCodeAt(0).toString(16)+" "})}function mt(e,t,n,r,a,l,o,i){e.name="",null!=o&&"function"!=typeof o&&"symbol"!=typeof o&&"boolean"!=typeof o?e.type=o:e.removeAttribute("type"),null!=t?"number"===o?(0===t&&""===e.value||e.value!=t)&&(e.value=""+ut(t)):e.value!==""+ut(t)&&(e.value=""+ut(t)):"submit"!==o&&"reset"!==o||e.removeAttribute("value"),null!=t?yt(e,o,ut(t)):null!=n?yt(e,o,ut(n)):null!=r&&e.removeAttribute("value"),null==a&&null!=l&&(e.defaultChecked=!!l),null!=a&&(e.checked=a&&"function"!=typeof a&&"symbol"!=typeof a),null!=i&&"function"!=typeof i&&"symbol"!=typeof i&&"boolean"!=typeof i?e.name=""+ut(i):e.removeAttribute("name")}function gt(e,t,n,r,a,l,o,i){if(null!=l&&"function"!=typeof l&&"symbol"!=typeof l&&"boolean"!=typeof l&&(e.type=l),null!=t||null!=n){if(("submit"===l||"reset"===l)&&null==t)return;n=null!=n?""+ut(n):"",t=null!=t?""+ut(t):n,i||t===e.value||(e.value=t),e.defaultValue=t}r="function"!=typeof(r=null!=r?r:a)&&"symbol"!=typeof r&&!!r,e.checked=i?e.checked:!!r,e.defaultChecked=!!r,null!=o&&"function"!=typeof o&&"symbol"!=typeof o&&"boolean"!=typeof o&&(e.name=o)}function yt(e,t,n){"number"===t&&dt(e.ownerDocument)===e||e.defaultValue===""+n||(e.defaultValue=""+n)}function vt(e,t,n,r){if(e=e.options,t){t={};for(var a=0;a<n.length;a++)t["$"+n[a]]=!0;for(n=0;n<e.length;n++)a=t.hasOwnProperty("$"+e[n].value),e[n].selected!==a&&(e[n].selected=a),a&&r&&(e[n].defaultSelected=!0)}else{for(n=""+ut(n),t=null,a=0;a<e.length;a++){if(e[a].value===n)return e[a].selected=!0,void(r&&(e[a].defaultSelected=!0));null!==t||e[a].disabled||(t=e[a])}null!==t&&(t.selected=!0)}}function bt(e,t,n){null==t||((t=""+ut(t))!==e.value&&(e.value=t),null!=n)?e.defaultValue=null!=n?""+ut(n):"":e.defaultValue!==t&&(e.defaultValue=t)}function St(e,t,n,r){if(null==t){if(null!=r){if(null!=n)throw Error(o(92));if(A(r)){if(1<r.length)throw Error(o(93));r=r[0]}n=r}null==n&&(n=""),t=n}n=ut(t),e.defaultValue=n,(r=e.textContent)===n&&""!==r&&null!==r&&(e.value=r)}function kt(e,t){if(t){var n=e.firstChild;if(n&&n===e.lastChild&&3===n.nodeType)return void(n.nodeValue=t)}e.textContent=t}var wt=new Set("animationIterationCount aspectRatio borderImageOutset borderImageSlice borderImageWidth boxFlex boxFlexGroup boxOrdinalGroup columnCount columns flex flexGrow flexPositive flexShrink flexNegative flexOrder gridArea gridRow gridRowEnd gridRowSpan gridRowStart gridColumn gridColumnEnd gridColumnSpan gridColumnStart fontWeight lineClamp lineHeight opacity order orphans scale tabSize widows zIndex zoom fillOpacity floodOpacity stopOpacity strokeDasharray strokeDashoffset strokeMiterlimit strokeOpacity strokeWidth MozAnimationIterationCount MozBoxFlex MozBoxFlexGroup MozLineClamp msAnimationIterationCount msFlex msZoom msFlexGrow msFlexNegative msFlexOrder msFlexPositive msFlexShrink msGridColumn msGridColumnSpan msGridRow msGridRowSpan WebkitAnimationIterationCount WebkitBoxFlex WebKitBoxFlexGroup WebkitBoxOrdinalGroup WebkitColumnCount WebkitColumns WebkitFlex WebkitFlexGrow WebkitFlexPositive WebkitFlexShrink WebkitLineClamp".split(" "));function xt(e,t,n){var r=0===t.indexOf("--");null==n||"boolean"==typeof n||""===n?r?e.setProperty(t,""):"float"===t?e.cssFloat="":e[t]="":r?e.setProperty(t,n):"number"!=typeof n||0===n||wt.has(t)?"float"===t?e.cssFloat=n:e[t]=(""+n).trim():e[t]=n+"px"}function Et(e,t,n){if(null!=t&&"object"!=typeof t)throw Error(o(62));if(e=e.style,null!=n){for(var r in n)!n.hasOwnProperty(r)||null!=t&&t.hasOwnProperty(r)||(0===r.indexOf("--")?e.setProperty(r,""):"float"===r?e.cssFloat="":e[r]="");for(var a in t)r=t[a],t.hasOwnProperty(a)&&n[a]!==r&&xt(e,a,r)}else for(var l in t)t.hasOwnProperty(l)&&xt(e,l,t[l])}function Ct(e){if(-1===e.indexOf("-"))return!1;switch(e){case"annotation-xml":case"color-profile":case"font-face":case"font-face-src":case"font-face-uri":case"font-face-format":case"font-face-name":case"missing-glyph":return!1;default:return!0}}var zt=new Map([["acceptCharset","accept-charset"],["htmlFor","for"],["httpEquiv","http-equiv"],["crossOrigin","crossorigin"],["accentHeight","accent-height"],["alignmentBaseline","alignment-baseline"],["arabicForm","arabic-form"],["baselineShift","baseline-shift"],["capHeight","cap-height"],["clipPath","clip-path"],["clipRule","clip-rule"],["colorInterpolation","color-interpolation"],["colorInterpolationFilters","color-interpolation-filters"],["colorProfile","color-profile"],["colorRendering","color-rendering"],["dominantBaseline","dominant-baseline"],["enableBackground","enable-background"],["fillOpacity","fill-opacity"],["fillRule","fill-rule"],["floodColor","flood-color"],["floodOpacity","flood-opacity"],["fontFamily","font-family"],["fontSize","font-size"],["fontSizeAdjust","font-size-adjust"],["fontStretch","font-stretch"],["fontStyle","font-style"],["fontVariant","font-variant"],["fontWeight","font-weight"],["glyphName","glyph-name"],["glyphOrientationHorizontal","glyph-orientation-horizontal"],["glyphOrientationVertical","glyph-orientation-vertical"],["horizAdvX","horiz-adv-x"],["horizOriginX","horiz-origin-x"],["imageRendering","image-rendering"],["letterSpacing","letter-spacing"],["lightingColor","lighting-color"],["markerEnd","marker-end"],["markerMid","marker-mid"],["markerStart","marker-start"],["overlinePosition","overline-position"],["overlineThickness","overline-thickness"],["paintOrder","paint-order"],["panose-1","panose-1"],["pointerEvents","pointer-events"],["renderingIntent","rendering-intent"],["shapeRendering","shape-rendering"],["stopColor","stop-color"],["stopOpacity","stop-opacity"],["strikethroughPosition","strikethrough-position"],["strikethroughThickness","strikethrough-thickness"],["strokeDasharray","stroke-dasharray"],["strokeDashoffset","stroke-dashoffset"],["strokeLinecap","stroke-linecap"],["strokeLinejoin","stroke-linejoin"],["strokeMiterlimit","stroke-miterlimit"],["strokeOpacity","stroke-opacity"],["strokeWidth","stroke-width"],["textAnchor","text-anchor"],["textDecoration","text-decoration"],["textRendering","text-rendering"],["transformOrigin","transform-origin"],["underlinePosition","underline-position"],["underlineThickness","underline-thickness"],["unicodeBidi","unicode-bidi"],["unicodeRange","unicode-range"],["unitsPerEm","units-per-em"],["vAlphabetic","v-alphabetic"],["vHanging","v-hanging"],["vIdeographic","v-ideographic"],["vMathematical","v-mathematical"],["vectorEffect","vector-effect"],["vertAdvY","vert-adv-y"],["vertOriginX","vert-origin-x"],["vertOriginY","vert-origin-y"],["wordSpacing","word-spacing"],["writingMode","writing-mode"],["xmlnsXlink","xmlns:xlink"],["xHeight","x-height"]]),Pt=/^[\u0000-\u001F ]*j[\r\n\t]*a[\r\n\t]*v[\r\n\t]*a[\r\n\t]*s[\r\n\t]*c[\r\n\t]*r[\r\n\t]*i[\r\n\t]*p[\r\n\t]*t[\r\n\t]*:/i;function _t(e){return Pt.test(""+e)?"javascript:throw new Error('React has blocked a javascript: URL as a security precaution.')":e}var Nt=null;function Lt(e){return(e=e.target||e.srcElement||window).correspondingUseElement&&(e=e.correspondingUseElement),3===e.nodeType?e.parentNode:e}var Tt=null,At=null;function Rt(e){var t=Ue(e);if(t&&(e=t.stateNode)){var n=e[Ae]||null;e:switch(e=t.stateNode,t.type){case"input":if(mt(e,n.value,n.defaultValue,n.defaultValue,n.checked,n.defaultChecked,n.type,n.name),t=n.name,"radio"===n.type&&null!=t){for(n=e;n.parentNode;)n=n.parentNode;for(n=n.querySelectorAll('input[name="'+ht(""+t)+'"][type="radio"]'),t=0;t<n.length;t++){var r=n[t];if(r!==e&&r.form===e.form){var a=r[Ae]||null;if(!a)throw Error(o(90));mt(r,a.value,a.defaultValue,a.defaultValue,a.checked,a.defaultChecked,a.type,a.name)}}for(t=0;t<n.length;t++)(r=n[t]).form===e.form&&ft(r)}break e;case"textarea":bt(e,n.value,n.defaultValue);break e;case"select":null!=(t=n.value)&&vt(e,!!n.multiple,t,!1)}}}var Ot=!1;function It(e,t,n){if(Ot)return e(t,n);Ot=!0;try{return e(t)}finally{if(Ot=!1,(null!==Tt||null!==At)&&($s(),Tt&&(t=Tt,e=At,At=Tt=null,Rt(t),e)))for(t=0;t<e.length;t++)Rt(e[t])}}function Dt(e,t){var n=e.stateNode;if(null===n)return null;var r=n[Ae]||null;if(null===r)return null;n=r[t];e:switch(t){case"onClick":case"onClickCapture":case"onDoubleClick":case"onDoubleClickCapture":case"onMouseDown":case"onMouseDownCapture":case"onMouseMove":case"onMouseMoveCapture":case"onMouseUp":case"onMouseUpCapture":case"onMouseEnter":(r=!r.disabled)||(r=!("button"===(e=e.type)||"input"===e||"select"===e||"textarea"===e)),e=!r;break e;default:e=!1}if(e)return null;if(n&&"function"!=typeof n)throw Error(o(231,t,typeof n));return n}var Mt=!("undefined"==typeof window||void 0===window.document||void 0===window.document.createElement),Ft=!1;if(Mt)try{var jt={};Object.defineProperty(jt,"passive",{get:function(){Ft=!0}}),window.addEventListener("test",jt,jt),window.removeEventListener("test",jt,jt)}catch(e){Ft=!1}var $t=null,Ut=null,Ht=null;function Bt(){if(Ht)return Ht;var e,t,n=Ut,r=n.length,a="value"in $t?$t.value:$t.textContent,l=a.length;for(e=0;e<r&&n[e]===a[e];e++);var o=r-e;for(t=1;t<=o&&n[r-t]===a[l-t];t++);return Ht=a.slice(e,1<t?1-t:void 0)}function Vt(e){var t=e.keyCode;return"charCode"in e?0===(e=e.charCode)&&13===t&&(e=13):e=t,10===e&&(e=13),32<=e||13===e?e:0}function Wt(){return!0}function Qt(){return!1}function qt(e){function t(t,n,r,a,l){for(var o in this._reactName=t,this._targetInst=r,this.type=n,this.nativeEvent=a,this.target=l,this.currentTarget=null,e)e.hasOwnProperty(o)&&(t=e[o],this[o]=t?t(a):a[o]);return this.isDefaultPrevented=(null!=a.defaultPrevented?a.defaultPrevented:!1===a.returnValue)?Wt:Qt,this.isPropagationStopped=Qt,this}return f(t.prototype,{preventDefault:function(){this.defaultPrevented=!0;var e=this.nativeEvent;e&&(e.preventDefault?e.preventDefault():"unknown"!=typeof e.returnValue&&(e.returnValue=!1),this.isDefaultPrevented=Wt)},stopPropagation:function(){var e=this.nativeEvent;e&&(e.stopPropagation?e.stopPropagation():"unknown"!=typeof e.cancelBubble&&(e.cancelBubble=!0),this.isPropagationStopped=Wt)},persist:function(){},isPersistent:Wt}),t}var Gt,Yt,Kt,Xt={eventPhase:0,bubbles:0,cancelable:0,timeStamp:function(e){return e.timeStamp||Date.now()},defaultPrevented:0,isTrusted:0},Zt=qt(Xt),Jt=f({},Xt,{view:0,detail:0}),en=qt(Jt),tn=f({},Jt,{screenX:0,screenY:0,clientX:0,clientY:0,pageX:0,pageY:0,ctrlKey:0,shiftKey:0,altKey:0,metaKey:0,getModifierState:pn,button:0,buttons:0,relatedTarget:function(e){return void 0===e.relatedTarget?e.fromElement===e.srcElement?e.toElement:e.fromElement:e.relatedTarget},movementX:function(e){return"movementX"in e?e.movementX:(e!==Kt&&(Kt&&"mousemove"===e.type?(Gt=e.screenX-Kt.screenX,Yt=e.screenY-Kt.screenY):Yt=Gt=0,Kt=e),Gt)},movementY:function(e){return"movementY"in e?e.movementY:Yt}}),nn=qt(tn),rn=qt(f({},tn,{dataTransfer:0})),an=qt(f({},Jt,{relatedTarget:0})),ln=qt(f({},Xt,{animationName:0,elapsedTime:0,pseudoElement:0})),on=qt(f({},Xt,{clipboardData:function(e){return"clipboardData"in e?e.clipboardData:window.clipboardData}})),un=qt(f({},Xt,{data:0})),sn={Esc:"Escape",Spacebar:" ",Left:"ArrowLeft",Up:"ArrowUp",Right:"ArrowRight",Down:"ArrowDown",Del:"Delete",Win:"OS",Menu:"ContextMenu",Apps:"ContextMenu",Scroll:"ScrollLock",MozPrintableKey:"Unidentified"},cn={8:"Backspace",9:"Tab",12:"Clear",13:"Enter",16:"Shift",17:"Control",18:"Alt",19:"Pause",20:"CapsLock",27:"Escape",32:" ",33:"PageUp",34:"PageDown",35:"End",36:"Home",37:"ArrowLeft",38:"ArrowUp",39:"ArrowRight",40:"ArrowDown",45:"Insert",46:"Delete",112:"F1",113:"F2",114:"F3",115:"F4",116:"F5",117:"F6",118:"F7",119:"F8",120:"F9",121:"F10",122:"F11",123:"F12",144:"NumLock",145:"ScrollLock",224:"Meta"},fn={Alt:"altKey",Control:"ctrlKey",Meta:"metaKey",Shift:"shiftKey"};function dn(e){var t=this.nativeEvent;return t.getModifierState?t.getModifierState(e):!!(e=fn[e])&&!!t[e]}function pn(){return dn}var hn=qt(f({},Jt,{key:function(e){if(e.key){var t=sn[e.key]||e.key;if("Unidentified"!==t)return t}return"keypress"===e.type?13===(e=Vt(e))?"Enter":String.fromCharCode(e):"keydown"===e.type||"keyup"===e.type?cn[e.keyCode]||"Unidentified":""},code:0,location:0,ctrlKey:0,shiftKey:0,altKey:0,metaKey:0,repeat:0,locale:0,getModifierState:pn,charCode:function(e){return"keypress"===e.type?Vt(e):0},keyCode:function(e){return"keydown"===e.type||"keyup"===e.type?e.keyCode:0},which:function(e){return"keypress"===e.type?Vt(e):"keydown"===e.type||"keyup"===e.type?e.keyCode:0}})),mn=qt(f({},tn,{pointerId:0,width:0,height:0,pressure:0,tangentialPressure:0,tiltX:0,tiltY:0,twist:0,pointerType:0,isPrimary:0})),gn=qt(f({},Jt,{touches:0,targetTouches:0,changedTouches:0,altKey:0,metaKey:0,ctrlKey:0,shiftKey:0,getModifierState:pn})),yn=qt(f({},Xt,{propertyName:0,elapsedTime:0,pseudoElement:0})),vn=qt(f({},tn,{deltaX:function(e){return"deltaX"in e?e.deltaX:"wheelDeltaX"in e?-e.wheelDeltaX:0},deltaY:function(e){return"deltaY"in e?e.deltaY:"wheelDeltaY"in e?-e.wheelDeltaY:"wheelDelta"in e?-e.wheelDelta:0},deltaZ:0,deltaMode:0})),bn=qt(f({},Xt,{newState:0,oldState:0})),Sn=[9,13,27,32],kn=Mt&&"CompositionEvent"in window,wn=null;Mt&&"documentMode"in document&&(wn=document.documentMode);var xn=Mt&&"TextEvent"in window&&!wn,En=Mt&&(!kn||wn&&8<wn&&11>=wn),Cn=String.fromCharCode(32),zn=!1;function Pn(e,t){switch(e){case"keyup":return-1!==Sn.indexOf(t.keyCode);case"keydown":return 229!==t.keyCode;case"keypress":case"mousedown":case"focusout":return!0;default:return!1}}function _n(e){return"object"==typeof(e=e.detail)&&"data"in e?e.data:null}var Nn=!1,Ln={color:!0,date:!0,datetime:!0,"datetime-local":!0,email:!0,month:!0,number:!0,password:!0,range:!0,search:!0,tel:!0,text:!0,time:!0,url:!0,week:!0};function Tn(e){var t=e&&e.nodeName&&e.nodeName.toLowerCase();return"input"===t?!!Ln[e.type]:"textarea"===t}function An(e,t,n,r){Tt?At?At.push(r):At=[r]:Tt=r,0<(t=Bc(t,"onChange")).length&&(n=new Zt("onChange","change",null,n,r),e.push({event:n,listeners:t}))}var Rn=null,On=null;function In(e){Ic(e,0)}function Dn(e){if(ft(He(e)))return e}function Mn(e,t){if("change"===e)return t}var Fn=!1;if(Mt){var jn;if(Mt){var $n="oninput"in document;if(!$n){var Un=document.createElement("div");Un.setAttribute("oninput","return;"),$n="function"==typeof Un.oninput}jn=$n}else jn=!1;Fn=jn&&(!document.documentMode||9<document.documentMode)}function Hn(){Rn&&(Rn.detachEvent("onpropertychange",Bn),On=Rn=null)}function Bn(e){if("value"===e.propertyName&&Dn(On)){var t=[];An(t,On,e,Lt(e)),It(In,t)}}function Vn(e,t,n){"focusin"===e?(Hn(),On=n,(Rn=t).attachEvent("onpropertychange",Bn)):"focusout"===e&&Hn()}function Wn(e){if("selectionchange"===e||"keyup"===e||"keydown"===e)return Dn(On)}function Qn(e,t){if("click"===e)return Dn(t)}function qn(e,t){if("input"===e||"change"===e)return Dn(t)}var Gn="function"==typeof Object.is?Object.is:function(e,t){return e===t&&(0!==e||1/e==1/t)||e!=e&&t!=t};function Yn(e,t){if(Gn(e,t))return!0;if("object"!=typeof e||null===e||"object"!=typeof t||null===t)return!1;var n=Object.keys(e),r=Object.keys(t);if(n.length!==r.length)return!1;for(r=0;r<n.length;r++){var a=n[r];if(!Y.call(t,a)||!Gn(e[a],t[a]))return!1}return!0}function Kn(e){for(;e&&e.firstChild;)e=e.firstChild;return e}function Xn(e,t){var n,r=Kn(e);for(e=0;r;){if(3===r.nodeType){if(n=e+r.textContent.length,e<=t&&n>=t)return{node:r,offset:t-e};e=n}e:{for(;r;){if(r.nextSibling){r=r.nextSibling;break e}r=r.parentNode}r=void 0}r=Kn(r)}}function Zn(e,t){return!(!e||!t)&&(e===t||(!e||3!==e.nodeType)&&(t&&3===t.nodeType?Zn(e,t.parentNode):"contains"in e?e.contains(t):!!e.compareDocumentPosition&&!!(16&e.compareDocumentPosition(t))))}function Jn(e){for(var t=dt((e=null!=e&&null!=e.ownerDocument&&null!=e.ownerDocument.defaultView?e.ownerDocument.defaultView:window).document);t instanceof e.HTMLIFrameElement;){try{var n="string"==typeof t.contentWindow.location.href}catch(e){n=!1}if(!n)break;t=dt((e=t.contentWindow).document)}return t}function er(e){var t=e&&e.nodeName&&e.nodeName.toLowerCase();return t&&("input"===t&&("text"===e.type||"search"===e.type||"tel"===e.type||"url"===e.type||"password"===e.type)||"textarea"===t||"true"===e.contentEditable)}var tr=Mt&&"documentMode"in document&&11>=document.documentMode,nr=null,rr=null,ar=null,lr=!1;function or(e,t,n){var r=n.window===n?n.document:9===n.nodeType?n:n.ownerDocument;lr||null==nr||nr!==dt(r)||(r="selectionStart"in(r=nr)&&er(r)?{start:r.selectionStart,end:r.selectionEnd}:{anchorNode:(r=(r.ownerDocument&&r.ownerDocument.defaultView||window).getSelection()).anchorNode,anchorOffset:r.anchorOffset,focusNode:r.focusNode,focusOffset:r.focusOffset},ar&&Yn(ar,r)||(ar=r,0<(r=Bc(rr,"onSelect")).length&&(t=new Zt("onSelect","select",null,t,n),e.push({event:t,listeners:r}),t.target=nr)))}function ir(e,t){var n={};return n[e.toLowerCase()]=t.toLowerCase(),n["Webkit"+e]="webkit"+t,n["Moz"+e]="moz"+t,n}var ur={animationend:ir("Animation","AnimationEnd"),animationiteration:ir("Animation","AnimationIteration"),animationstart:ir("Animation","AnimationStart"),transitionrun:ir("Transition","TransitionRun"),transitionstart:ir("Transition","TransitionStart"),transitioncancel:ir("Transition","TransitionCancel"),transitionend:ir("Transition","TransitionEnd")},sr={},cr={};function fr(e){if(sr[e])return sr[e];if(!ur[e])return e;var t,n=ur[e];for(t in n)if(n.hasOwnProperty(t)&&t in cr)return sr[e]=n[t];return e}Mt&&(cr=document.createElement("div").style,"AnimationEvent"in window||(delete ur.animationend.animation,delete ur.animationiteration.animation,delete ur.animationstart.animation),"TransitionEvent"in window||delete ur.transitionend.transition);var dr=fr("animationend"),pr=fr("animationiteration"),hr=fr("animationstart"),mr=fr("transitionrun"),gr=fr("transitionstart"),yr=fr("transitioncancel"),vr=fr("transitionend"),br=new Map,Sr="abort auxClick beforeToggle cancel canPlay canPlayThrough click close contextMenu copy cut drag dragEnd dragEnter dragExit dragLeave dragOver dragStart drop durationChange emptied encrypted ended error gotPointerCapture input invalid keyDown keyPress keyUp load loadedData loadedMetadata loadStart lostPointerCapture mouseDown mouseMove mouseOut mouseOver mouseUp paste pause play playing pointerCancel pointerDown pointerMove pointerOut pointerOver pointerUp progress rateChange reset resize seeked seeking stalled submit suspend timeUpdate touchCancel touchEnd touchStart volumeChange scroll toggle touchMove waiting wheel".split(" ");function kr(e,t){br.set(e,t),qe(t,[e])}Sr.push("scrollEnd");var wr=new WeakMap;function xr(e,t){if("object"==typeof e&&null!==e){var n=wr.get(e);return void 0!==n?n:(t={value:e,source:t,stack:it(t)},wr.set(e,t),t)}return{value:e,source:t,stack:it(t)}}var Er=[],Cr=0,zr=0;function Pr(){for(var e=Cr,t=zr=Cr=0;t<e;){var n=Er[t];Er[t++]=null;var r=Er[t];Er[t++]=null;var a=Er[t];Er[t++]=null;var l=Er[t];if(Er[t++]=null,null!==r&&null!==a){var o=r.pending;null===o?a.next=a:(a.next=o.next,o.next=a),r.pending=a}0!==l&&Tr(n,a,l)}}function _r(e,t,n,r){Er[Cr++]=e,Er[Cr++]=t,Er[Cr++]=n,Er[Cr++]=r,zr|=r,e.lanes|=r,null!==(e=e.alternate)&&(e.lanes|=r)}function Nr(e,t,n,r){return _r(e,t,n,r),Ar(e)}function Lr(e,t){return _r(e,null,null,t),Ar(e)}function Tr(e,t,n){e.lanes|=n;var r=e.alternate;null!==r&&(r.lanes|=n);for(var a=!1,l=e.return;null!==l;)l.childLanes|=n,null!==(r=l.alternate)&&(r.childLanes|=n),22===l.tag&&(null===(e=l.stateNode)||1&e._visibility||(a=!0)),e=l,l=l.return;return 3===e.tag?(l=e.stateNode,a&&null!==t&&(a=31-de(n),null===(r=(e=l.hiddenUpdates)[a])?e[a]=[t]:r.push(t),t.lane=536870912|n),l):null}function Ar(e){if(50<Ts)throw Ts=0,As=null,Error(o(185));for(var t=e.return;null!==t;)t=(e=t).return;return 3===e.tag?e.stateNode:null}var Rr={};function Or(e,t,n,r){this.tag=e,this.key=n,this.sibling=this.child=this.return=this.stateNode=this.type=this.elementType=null,this.index=0,this.refCleanup=this.ref=null,this.pendingProps=t,this.dependencies=this.memoizedState=this.updateQueue=this.memoizedProps=null,this.mode=r,this.subtreeFlags=this.flags=0,this.deletions=null,this.childLanes=this.lanes=0,this.alternate=null}function Ir(e,t,n,r){return new Or(e,t,n,r)}function Dr(e){return!(!(e=e.prototype)||!e.isReactComponent)}function Mr(e,t){var n=e.alternate;return null===n?((n=Ir(e.tag,t,e.key,e.mode)).elementType=e.elementType,n.type=e.type,n.stateNode=e.stateNode,n.alternate=e,e.alternate=n):(n.pendingProps=t,n.type=e.type,n.flags=0,n.subtreeFlags=0,n.deletions=null),n.flags=65011712&e.flags,n.childLanes=e.childLanes,n.lanes=e.lanes,n.child=e.child,n.memoizedProps=e.memoizedProps,n.memoizedState=e.memoizedState,n.updateQueue=e.updateQueue,t=e.dependencies,n.dependencies=null===t?null:{lanes:t.lanes,firstContext:t.firstContext},n.sibling=e.sibling,n.index=e.index,n.ref=e.ref,n.refCleanup=e.refCleanup,n}function Fr(e,t){e.flags&=65011714;var n=e.alternate;return null===n?(e.childLanes=0,e.lanes=t,e.child=null,e.subtreeFlags=0,e.memoizedProps=null,e.memoizedState=null,e.updateQueue=null,e.dependencies=null,e.stateNode=null):(e.childLanes=n.childLanes,e.lanes=n.lanes,e.child=n.child,e.subtreeFlags=0,e.deletions=null,e.memoizedProps=n.memoizedProps,e.memoizedState=n.memoizedState,e.updateQueue=n.updateQueue,e.type=n.type,t=n.dependencies,e.dependencies=null===t?null:{lanes:t.lanes,firstContext:t.firstContext}),e}function jr(e,t,n,r,a,l){var i=0;if(r=e,"function"==typeof e)Dr(e)&&(i=1);else if("string"==typeof e)i=function(e,t,n){if(1===n||null!=t.itemProp)return!1;switch(e){case"meta":case"title":return!0;case"style":if("string"!=typeof t.precedence||"string"!=typeof t.href||""===t.href)break;return!0;case"link":if("string"!=typeof t.rel||"string"!=typeof t.href||""===t.href||t.onLoad||t.onError)break;return"stylesheet"!==t.rel||(e=t.disabled,"string"==typeof t.precedence&&null==e);case"script":if(t.async&&"function"!=typeof t.async&&"symbol"!=typeof t.async&&!t.onLoad&&!t.onError&&t.src&&"string"==typeof t.src)return!0}return!1}(e,n,U.current)?26:"html"===e||"head"===e||"body"===e?27:5;else e:switch(e){case z:return(e=Ir(31,n,t,a)).elementType=z,e.lanes=l,e;case m:return $r(n.children,a,l,t);case g:i=8,a|=24;break;case y:return(e=Ir(12,n,t,2|a)).elementType=y,e.lanes=l,e;case w:return(e=Ir(13,n,t,a)).elementType=w,e.lanes=l,e;case x:return(e=Ir(19,n,t,a)).elementType=x,e.lanes=l,e;default:if("object"==typeof e&&null!==e)switch(e.$$typeof){case v:case S:i=10;break e;case b:i=9;break e;case k:i=11;break e;case E:i=14;break e;case C:i=16,r=null;break e}i=29,n=Error(o(130,null===e?"null":typeof e,"")),r=null}return(t=Ir(i,n,t,a)).elementType=e,t.type=r,t.lanes=l,t}function $r(e,t,n,r){return(e=Ir(7,e,r,t)).lanes=n,e}function Ur(e,t,n){return(e=Ir(6,e,null,t)).lanes=n,e}function Hr(e,t,n){return(t=Ir(4,null!==e.children?e.children:[],e.key,t)).lanes=n,t.stateNode={containerInfo:e.containerInfo,pendingChildren:null,implementation:e.implementation},t}var Br=[],Vr=0,Wr=null,Qr=0,qr=[],Gr=0,Yr=null,Kr=1,Xr="";function Zr(e,t){Br[Vr++]=Qr,Br[Vr++]=Wr,Wr=e,Qr=t}function Jr(e,t,n){qr[Gr++]=Kr,qr[Gr++]=Xr,qr[Gr++]=Yr,Yr=e;var r=Kr;e=Xr;var a=32-de(r)-1;r&=~(1<<a),n+=1;var l=32-de(t)+a;if(30<l){var o=a-a%5;l=(r&(1<<o)-1).toString(32),r>>=o,a-=o,Kr=1<<32-de(t)+a|n<<a|r,Xr=l+e}else Kr=1<<l|n<<a|r,Xr=e}function ea(e){null!==e.return&&(Zr(e,1),Jr(e,1,0))}function ta(e){for(;e===Wr;)Wr=Br[--Vr],Br[Vr]=null,Qr=Br[--Vr],Br[Vr]=null;for(;e===Yr;)Yr=qr[--Gr],qr[Gr]=null,Xr=qr[--Gr],qr[Gr]=null,Kr=qr[--Gr],qr[Gr]=null}var na=null,ra=null,aa=!1,la=null,oa=!1,ia=Error(o(519));function ua(e){throw ha(xr(Error(o(418,"")),e)),ia}function sa(e){var t=e.stateNode,n=e.type,r=e.memoizedProps;switch(t[Te]=e,t[Ae]=r,n){case"dialog":Dc("cancel",t),Dc("close",t);break;case"iframe":case"object":case"embed":Dc("load",t);break;case"video":case"audio":for(n=0;n<Rc.length;n++)Dc(Rc[n],t);break;case"source":Dc("error",t);break;case"img":case"image":case"link":Dc("error",t),Dc("load",t);break;case"details":Dc("toggle",t);break;case"input":Dc("invalid",t),gt(t,r.value,r.defaultValue,r.checked,r.defaultChecked,r.type,r.name,!0),ct(t);break;case"select":Dc("invalid",t);break;case"textarea":Dc("invalid",t),St(t,r.value,r.defaultValue,r.children),ct(t)}"string"!=typeof(n=r.children)&&"number"!=typeof n&&"bigint"!=typeof n||t.textContent===""+n||!0===r.suppressHydrationWarning||Yc(t.textContent,n)?(null!=r.popover&&(Dc("beforetoggle",t),Dc("toggle",t)),null!=r.onScroll&&Dc("scroll",t),null!=r.onScrollEnd&&Dc("scrollend",t),null!=r.onClick&&(t.onclick=Kc),t=!0):t=!1,t||ua(e)}function ca(e){for(na=e.return;na;)switch(na.tag){case 5:case 13:return void(oa=!1);case 27:case 3:return void(oa=!0);default:na=na.return}}function fa(e){if(e!==na)return!1;if(!aa)return ca(e),aa=!0,!1;var t,n=e.tag;if((t=3!==n&&27!==n)&&((t=5===n)&&(t=!("form"!==(t=e.type)&&"button"!==t)||lf(e.type,e.memoizedProps)),t=!t),t&&ra&&ua(e),ca(e),13===n){if(!(e=null!==(e=e.memoizedState)?e.dehydrated:null))throw Error(o(317));e:{for(e=e.nextSibling,n=0;e;){if(8===e.nodeType)if("/$"===(t=e.data)){if(0===n){ra=yf(e.nextSibling);break e}n--}else"$"!==t&&"$!"!==t&&"$?"!==t||n++;e=e.nextSibling}ra=null}}else 27===n?(n=ra,pf(e.type)?(e=vf,vf=null,ra=e):ra=n):ra=na?yf(e.stateNode.nextSibling):null;return!0}function da(){ra=na=null,aa=!1}function pa(){var e=la;return null!==e&&(null===vs?vs=e:vs.push.apply(vs,e),la=null),e}function ha(e){null===la?la=[e]:la.push(e)}var ma=F(null),ga=null,ya=null;function va(e,t,n){$(ma,t._currentValue),t._currentValue=n}function ba(e){e._currentValue=ma.current,j(ma)}function Sa(e,t,n){for(;null!==e;){var r=e.alternate;if((e.childLanes&t)!==t?(e.childLanes|=t,null!==r&&(r.childLanes|=t)):null!==r&&(r.childLanes&t)!==t&&(r.childLanes|=t),e===n)break;e=e.return}}function ka(e,t,n,r){var a=e.child;for(null!==a&&(a.return=e);null!==a;){var l=a.dependencies;if(null!==l){var i=a.child;l=l.firstContext;e:for(;null!==l;){var u=l;l=a;for(var s=0;s<t.length;s++)if(u.context===t[s]){l.lanes|=n,null!==(u=l.alternate)&&(u.lanes|=n),Sa(l.return,n,e),r||(i=null);break e}l=u.next}}else if(18===a.tag){if(null===(i=a.return))throw Error(o(341));i.lanes|=n,null!==(l=i.alternate)&&(l.lanes|=n),Sa(i,n,e),i=null}else i=a.child;if(null!==i)i.return=a;else for(i=a;null!==i;){if(i===e){i=null;break}if(null!==(a=i.sibling)){a.return=i.return,i=a;break}i=i.return}a=i}}function wa(e,t,n,r){e=null;for(var a=t,l=!1;null!==a;){if(!l)if(524288&a.flags)l=!0;else if(262144&a.flags)break;if(10===a.tag){var i=a.alternate;if(null===i)throw Error(o(387));if(null!==(i=i.memoizedProps)){var u=a.type;Gn(a.pendingProps.value,i.value)||(null!==e?e.push(u):e=[u])}}else if(a===V.current){if(null===(i=a.alternate))throw Error(o(387));i.memoizedState.memoizedState!==a.memoizedState.memoizedState&&(null!==e?e.push(Gf):e=[Gf])}a=a.return}null!==e&&ka(t,e,n,r),t.flags|=262144}function xa(e){for(e=e.firstContext;null!==e;){if(!Gn(e.context._currentValue,e.memoizedValue))return!0;e=e.next}return!1}function Ea(e){ga=e,ya=null,null!==(e=e.dependencies)&&(e.firstContext=null)}function Ca(e){return Pa(ga,e)}function za(e,t){return null===ga&&Ea(e),Pa(e,t)}function Pa(e,t){var n=t._currentValue;if(t={context:t,memoizedValue:n,next:null},null===ya){if(null===e)throw Error(o(308));ya=t,e.dependencies={lanes:0,firstContext:t},e.flags|=524288}else ya=ya.next=t;return n}var _a="undefined"!=typeof AbortController?AbortController:function(){var e=[],t=this.signal={aborted:!1,addEventListener:function(t,n){e.push(n)}};this.abort=function(){t.aborted=!0,e.forEach(function(e){return e()})}},Na=r.unstable_scheduleCallback,La=r.unstable_NormalPriority,Ta={$$typeof:S,Consumer:null,Provider:null,_currentValue:null,_currentValue2:null,_threadCount:0};function Aa(){return{controller:new _a,data:new Map,refCount:0}}function Ra(e){e.refCount--,0===e.refCount&&Na(La,function(){e.controller.abort()})}var Oa=null,Ia=0,Da=0,Ma=null;function Fa(){if(0===--Ia&&null!==Oa){null!==Ma&&(Ma.status="fulfilled");var e=Oa;Oa=null,Da=0,Ma=null;for(var t=0;t<e.length;t++)(0,e[t])()}}var ja=R.S;R.S=function(e,t){"object"==typeof t&&null!==t&&"function"==typeof t.then&&function(e,t){if(null===Oa){var n=Oa=[];Ia=0,Da=_c(),Ma={status:"pending",value:void 0,then:function(e){n.push(e)}}}Ia++,t.then(Fa,Fa)}(0,t),null!==ja&&ja(e,t)};var $a=F(null);function Ua(){var e=$a.current;return null!==e?e:ns.pooledCache}function Ha(e,t){$($a,null===t?$a.current:t.pool)}function Ba(){var e=Ua();return null===e?null:{parent:Ta._currentValue,pool:e}}var Va=Error(o(460)),Wa=Error(o(474)),Qa=Error(o(542)),qa={then:function(){}};function Ga(e){return"fulfilled"===(e=e.status)||"rejected"===e}function Ya(){}function Ka(e,t,n){switch(void 0===(n=e[n])?e.push(t):n!==t&&(t.then(Ya,Ya),t=n),t.status){case"fulfilled":return t.value;case"rejected":throw Ja(e=t.reason),e;default:if("string"==typeof t.status)t.then(Ya,Ya);else{if(null!==(e=ns)&&100<e.shellSuspendCounter)throw Error(o(482));(e=t).status="pending",e.then(function(e){if("pending"===t.status){var n=t;n.status="fulfilled",n.value=e}},function(e){if("pending"===t.status){var n=t;n.status="rejected",n.reason=e}})}switch(t.status){case"fulfilled":return t.value;case"rejected":throw Ja(e=t.reason),e}throw Xa=t,Va}}var Xa=null;function Za(){if(null===Xa)throw Error(o(459));var e=Xa;return Xa=null,e}function Ja(e){if(e===Va||e===Qa)throw Error(o(483))}var el=!1;function tl(e){e.updateQueue={baseState:e.memoizedState,firstBaseUpdate:null,lastBaseUpdate:null,shared:{pending:null,lanes:0,hiddenCallbacks:null},callbacks:null}}function nl(e,t){e=e.updateQueue,t.updateQueue===e&&(t.updateQueue={baseState:e.baseState,firstBaseUpdate:e.firstBaseUpdate,lastBaseUpdate:e.lastBaseUpdate,shared:e.shared,callbacks:null})}function rl(e){return{lane:e,tag:0,payload:null,callback:null,next:null}}function al(e,t,n){var r=e.updateQueue;if(null===r)return null;if(r=r.shared,2&ts){var a=r.pending;return null===a?t.next=t:(t.next=a.next,a.next=t),r.pending=t,t=Ar(e),Tr(e,null,n),t}return _r(e,r,t,n),Ar(e)}function ll(e,t,n){if(null!==(t=t.updateQueue)&&(t=t.shared,4194048&n)){var r=t.lanes;n|=r&=e.pendingLanes,t.lanes=n,ze(e,n)}}function ol(e,t){var n=e.updateQueue,r=e.alternate;if(null!==r&&n===(r=r.updateQueue)){var a=null,l=null;if(null!==(n=n.firstBaseUpdate)){do{var o={lane:n.lane,tag:n.tag,payload:n.payload,callback:null,next:null};null===l?a=l=o:l=l.next=o,n=n.next}while(null!==n);null===l?a=l=t:l=l.next=t}else a=l=t;return n={baseState:r.baseState,firstBaseUpdate:a,lastBaseUpdate:l,shared:r.shared,callbacks:r.callbacks},void(e.updateQueue=n)}null===(e=n.lastBaseUpdate)?n.firstBaseUpdate=t:e.next=t,n.lastBaseUpdate=t}var il=!1;function ul(){if(il&&null!==Ma)throw Ma}function sl(e,t,n,r){il=!1;var a=e.updateQueue;el=!1;var l=a.firstBaseUpdate,o=a.lastBaseUpdate,i=a.shared.pending;if(null!==i){a.shared.pending=null;var u=i,s=u.next;u.next=null,null===o?l=s:o.next=s,o=u;var c=e.alternate;null!==c&&(i=(c=c.updateQueue).lastBaseUpdate)!==o&&(null===i?c.firstBaseUpdate=s:i.next=s,c.lastBaseUpdate=u)}if(null!==l){var d=a.baseState;for(o=0,c=s=u=null,i=l;;){var p=-536870913&i.lane,h=p!==i.lane;if(h?(as&p)===p:(r&p)===p){0!==p&&p===Da&&(il=!0),null!==c&&(c=c.next={lane:0,tag:i.tag,payload:i.payload,callback:null,next:null});e:{var m=e,g=i;p=t;var y=n;switch(g.tag){case 1:if("function"==typeof(m=g.payload)){d=m.call(y,d,p);break e}d=m;break e;case 3:m.flags=-65537&m.flags|128;case 0:if(null==(p="function"==typeof(m=g.payload)?m.call(y,d,p):m))break e;d=f({},d,p);break e;case 2:el=!0}}null!==(p=i.callback)&&(e.flags|=64,h&&(e.flags|=8192),null===(h=a.callbacks)?a.callbacks=[p]:h.push(p))}else h={lane:p,tag:i.tag,payload:i.payload,callback:i.callback,next:null},null===c?(s=c=h,u=d):c=c.next=h,o|=p;if(null===(i=i.next)){if(null===(i=a.shared.pending))break;i=(h=i).next,h.next=null,a.lastBaseUpdate=h,a.shared.pending=null}}null===c&&(u=d),a.baseState=u,a.firstBaseUpdate=s,a.lastBaseUpdate=c,null===l&&(a.shared.lanes=0),ds|=o,e.lanes=o,e.memoizedState=d}}function cl(e,t){if("function"!=typeof e)throw Error(o(191,e));e.call(t)}function fl(e,t){var n=e.callbacks;if(null!==n)for(e.callbacks=null,e=0;e<n.length;e++)cl(n[e],t)}var dl=F(null),pl=F(0);function hl(e,t){$(pl,e=cs),$(dl,t),cs=e|t.baseLanes}function ml(){$(pl,cs),$(dl,dl.current)}function gl(){cs=pl.current,j(dl),j(pl)}var yl=0,vl=null,bl=null,Sl=null,kl=!1,wl=!1,xl=!1,El=0,Cl=0,zl=null,Pl=0;function _l(){throw Error(o(321))}function Nl(e,t){if(null===t)return!1;for(var n=0;n<t.length&&n<e.length;n++)if(!Gn(e[n],t[n]))return!1;return!0}function Ll(e,t,n,r,a,l){return yl=l,vl=t,t.memoizedState=null,t.updateQueue=null,t.lanes=0,R.H=null===e||null===e.memoizedState?Wo:Qo,xl=!1,l=n(r,a),xl=!1,wl&&(l=Al(t,n,r,a)),Tl(e),l}function Tl(e){R.H=Vo;var t=null!==bl&&null!==bl.next;if(yl=0,Sl=bl=vl=null,kl=!1,Cl=0,zl=null,t)throw Error(o(300));null===e||zi||null!==(e=e.dependencies)&&xa(e)&&(zi=!0)}function Al(e,t,n,r){vl=e;var a=0;do{if(wl&&(zl=null),Cl=0,wl=!1,25<=a)throw Error(o(301));if(a+=1,Sl=bl=null,null!=e.updateQueue){var l=e.updateQueue;l.lastEffect=null,l.events=null,l.stores=null,null!=l.memoCache&&(l.memoCache.index=0)}R.H=qo,l=t(n,r)}while(wl);return l}function Rl(){var e=R.H,t=e.useState()[0];return t="function"==typeof t.then?jl(t):t,e=e.useState()[0],(null!==bl?bl.memoizedState:null)!==e&&(vl.flags|=1024),t}function Ol(){var e=0!==El;return El=0,e}function Il(e,t,n){t.updateQueue=e.updateQueue,t.flags&=-2053,e.lanes&=~n}function Dl(e){if(kl){for(e=e.memoizedState;null!==e;){var t=e.queue;null!==t&&(t.pending=null),e=e.next}kl=!1}yl=0,Sl=bl=vl=null,wl=!1,Cl=El=0,zl=null}function Ml(){var e={memoizedState:null,baseState:null,baseQueue:null,queue:null,next:null};return null===Sl?vl.memoizedState=Sl=e:Sl=Sl.next=e,Sl}function Fl(){if(null===bl){var e=vl.alternate;e=null!==e?e.memoizedState:null}else e=bl.next;var t=null===Sl?vl.memoizedState:Sl.next;if(null!==t)Sl=t,bl=e;else{if(null===e){if(null===vl.alternate)throw Error(o(467));throw Error(o(310))}e={memoizedState:(bl=e).memoizedState,baseState:bl.baseState,baseQueue:bl.baseQueue,queue:bl.queue,next:null},null===Sl?vl.memoizedState=Sl=e:Sl=Sl.next=e}return Sl}function jl(e){var t=Cl;return Cl+=1,null===zl&&(zl=[]),e=Ka(zl,e,t),t=vl,null===(null===Sl?t.memoizedState:Sl.next)&&(t=t.alternate,R.H=null===t||null===t.memoizedState?Wo:Qo),e}function $l(e){if(null!==e&&"object"==typeof e){if("function"==typeof e.then)return jl(e);if(e.$$typeof===S)return Ca(e)}throw Error(o(438,String(e)))}function Ul(e){var t=null,n=vl.updateQueue;if(null!==n&&(t=n.memoCache),null==t){var r=vl.alternate;null!==r&&null!==(r=r.updateQueue)&&null!=(r=r.memoCache)&&(t={data:r.data.map(function(e){return e.slice()}),index:0})}if(null==t&&(t={data:[],index:0}),null===n&&(n={lastEffect:null,events:null,stores:null,memoCache:null},vl.updateQueue=n),n.memoCache=t,void 0===(n=t.data[t.index]))for(n=t.data[t.index]=Array(e),r=0;r<e;r++)n[r]=P;return t.index++,n}function Hl(e,t){return"function"==typeof t?t(e):t}function Bl(e){return Vl(Fl(),bl,e)}function Vl(e,t,n){var r=e.queue;if(null===r)throw Error(o(311));r.lastRenderedReducer=n;var a=e.baseQueue,l=r.pending;if(null!==l){if(null!==a){var i=a.next;a.next=l.next,l.next=i}t.baseQueue=a=l,r.pending=null}if(l=e.baseState,null===a)e.memoizedState=l;else{var u=i=null,s=null,c=t=a.next,f=!1;do{var d=-536870913&c.lane;if(d!==c.lane?(as&d)===d:(yl&d)===d){var p=c.revertLane;if(0===p)null!==s&&(s=s.next={lane:0,revertLane:0,action:c.action,hasEagerState:c.hasEagerState,eagerState:c.eagerState,next:null}),d===Da&&(f=!0);else{if((yl&p)===p){c=c.next,p===Da&&(f=!0);continue}d={lane:0,revertLane:c.revertLane,action:c.action,hasEagerState:c.hasEagerState,eagerState:c.eagerState,next:null},null===s?(u=s=d,i=l):s=s.next=d,vl.lanes|=p,ds|=p}d=c.action,xl&&n(l,d),l=c.hasEagerState?c.eagerState:n(l,d)}else p={lane:d,revertLane:c.revertLane,action:c.action,hasEagerState:c.hasEagerState,eagerState:c.eagerState,next:null},null===s?(u=s=p,i=l):s=s.next=p,vl.lanes|=d,ds|=d;c=c.next}while(null!==c&&c!==t);if(null===s?i=l:s.next=u,!Gn(l,e.memoizedState)&&(zi=!0,f&&null!==(n=Ma)))throw n;e.memoizedState=l,e.baseState=i,e.baseQueue=s,r.lastRenderedState=l}return null===a&&(r.lanes=0),[e.memoizedState,r.dispatch]}function Wl(e){var t=Fl(),n=t.queue;if(null===n)throw Error(o(311));n.lastRenderedReducer=e;var r=n.dispatch,a=n.pending,l=t.memoizedState;if(null!==a){n.pending=null;var i=a=a.next;do{l=e(l,i.action),i=i.next}while(i!==a);Gn(l,t.memoizedState)||(zi=!0),t.memoizedState=l,null===t.baseQueue&&(t.baseState=l),n.lastRenderedState=l}return[l,r]}function Ql(e,t,n){var r=vl,a=Fl(),l=aa;if(l){if(void 0===n)throw Error(o(407));n=n()}else n=t();var i=!Gn((bl||a).memoizedState,n);if(i&&(a.memoizedState=n,zi=!0),a=a.queue,go(2048,8,Yl.bind(null,r,a,e),[e]),a.getSnapshot!==t||i||null!==Sl&&1&Sl.memoizedState.tag){if(r.flags|=2048,po(9,{destroy:void 0,resource:void 0},Gl.bind(null,r,a,n,t),null),null===ns)throw Error(o(349));l||124&yl||ql(r,t,n)}return n}function ql(e,t,n){e.flags|=16384,e={getSnapshot:t,value:n},null===(t=vl.updateQueue)?(t={lastEffect:null,events:null,stores:null,memoCache:null},vl.updateQueue=t,t.stores=[e]):null===(n=t.stores)?t.stores=[e]:n.push(e)}function Gl(e,t,n,r){t.value=n,t.getSnapshot=r,Kl(t)&&Xl(e)}function Yl(e,t,n){return n(function(){Kl(t)&&Xl(e)})}function Kl(e){var t=e.getSnapshot;e=e.value;try{var n=t();return!Gn(e,n)}catch(e){return!0}}function Xl(e){var t=Lr(e,2);null!==t&&Is(t,0,2)}function Zl(e){var t=Ml();if("function"==typeof e){var n=e;if(e=n(),xl){fe(!0);try{n()}finally{fe(!1)}}}return t.memoizedState=t.baseState=e,t.queue={pending:null,lanes:0,dispatch:null,lastRenderedReducer:Hl,lastRenderedState:e},t}function Jl(e,t,n,r){return e.baseState=n,Vl(e,bl,"function"==typeof r?r:Hl)}function eo(e,t,n,r,a){if(Uo(e))throw Error(o(485));if(null!==(e=t.action)){var l={payload:a,action:e,next:null,isTransition:!0,status:"pending",value:null,reason:null,listeners:[],then:function(e){l.listeners.push(e)}};null!==R.T?n(!0):l.isTransition=!1,r(l),null===(n=t.pending)?(l.next=t.pending=l,to(t,l)):(l.next=n.next,t.pending=n.next=l)}}function to(e,t){var n=t.action,r=t.payload,a=e.state;if(t.isTransition){var l=R.T,o={};R.T=o;try{var i=n(a,r),u=R.S;null!==u&&u(o,i),no(e,t,i)}catch(n){ao(e,t,n)}finally{R.T=l}}else try{no(e,t,l=n(a,r))}catch(n){ao(e,t,n)}}function no(e,t,n){null!==n&&"object"==typeof n&&"function"==typeof n.then?n.then(function(n){ro(e,t,n)},function(n){return ao(e,t,n)}):ro(e,t,n)}function ro(e,t,n){t.status="fulfilled",t.value=n,lo(t),e.state=n,null!==(t=e.pending)&&((n=t.next)===t?e.pending=null:(n=n.next,t.next=n,to(e,n)))}function ao(e,t,n){var r=e.pending;if(e.pending=null,null!==r){r=r.next;do{t.status="rejected",t.reason=n,lo(t),t=t.next}while(t!==r)}e.action=null}function lo(e){e=e.listeners;for(var t=0;t<e.length;t++)(0,e[t])()}function oo(e,t){return t}function io(e,t){if(aa){var n=ns.formState;if(null!==n){e:{var r=vl;if(aa){if(ra){t:{for(var a=ra,l=oa;8!==a.nodeType;){if(!l){a=null;break t}if(null===(a=yf(a.nextSibling))){a=null;break t}}a="F!"===(l=a.data)||"F"===l?a:null}if(a){ra=yf(a.nextSibling),r="F!"===a.data;break e}}ua(r)}r=!1}r&&(t=n[0])}}return(n=Ml()).memoizedState=n.baseState=t,r={pending:null,lanes:0,dispatch:null,lastRenderedReducer:oo,lastRenderedState:t},n.queue=r,n=Fo.bind(null,vl,r),r.dispatch=n,r=Zl(!1),l=$o.bind(null,vl,!1,r.queue),a={state:t,dispatch:null,action:e,pending:null},(r=Ml()).queue=a,n=eo.bind(null,vl,a,l,n),a.dispatch=n,r.memoizedState=e,[t,n,!1]}function uo(e){return so(Fl(),bl,e)}function so(e,t,n){if(t=Vl(e,t,oo)[0],e=Bl(Hl)[0],"object"==typeof t&&null!==t&&"function"==typeof t.then)try{var r=jl(t)}catch(e){if(e===Va)throw Qa;throw e}else r=t;var a=(t=Fl()).queue,l=a.dispatch;return n!==t.memoizedState&&(vl.flags|=2048,po(9,{destroy:void 0,resource:void 0},co.bind(null,a,n),null)),[r,l,e]}function co(e,t){e.action=t}function fo(e){var t=Fl(),n=bl;if(null!==n)return so(t,n,e);Fl(),t=t.memoizedState;var r=(n=Fl()).queue.dispatch;return n.memoizedState=e,[t,r,!1]}function po(e,t,n,r){return e={tag:e,create:n,deps:r,inst:t,next:null},null===(t=vl.updateQueue)&&(t={lastEffect:null,events:null,stores:null,memoCache:null},vl.updateQueue=t),null===(n=t.lastEffect)?t.lastEffect=e.next=e:(r=n.next,n.next=e,e.next=r,t.lastEffect=e),e}function ho(){return Fl().memoizedState}function mo(e,t,n,r){var a=Ml();r=void 0===r?null:r,vl.flags|=e,a.memoizedState=po(1|t,{destroy:void 0,resource:void 0},n,r)}function go(e,t,n,r){var a=Fl();r=void 0===r?null:r;var l=a.memoizedState.inst;null!==bl&&null!==r&&Nl(r,bl.memoizedState.deps)?a.memoizedState=po(t,l,n,r):(vl.flags|=e,a.memoizedState=po(1|t,l,n,r))}function yo(e,t){mo(8390656,8,e,t)}function vo(e,t){go(2048,8,e,t)}function bo(e,t){return go(4,2,e,t)}function So(e,t){return go(4,4,e,t)}function ko(e,t){if("function"==typeof t){e=e();var n=t(e);return function(){"function"==typeof n?n():t(null)}}if(null!=t)return e=e(),t.current=e,function(){t.current=null}}function wo(e,t,n){n=null!=n?n.concat([e]):null,go(4,4,ko.bind(null,t,e),n)}function xo(){}function Eo(e,t){var n=Fl();t=void 0===t?null:t;var r=n.memoizedState;return null!==t&&Nl(t,r[1])?r[0]:(n.memoizedState=[e,t],e)}function Co(e,t){var n=Fl();t=void 0===t?null:t;var r=n.memoizedState;if(null!==t&&Nl(t,r[1]))return r[0];if(r=e(),xl){fe(!0);try{e()}finally{fe(!1)}}return n.memoizedState=[r,t],r}function zo(e,t,n){return void 0===n||1073741824&yl?e.memoizedState=t:(e.memoizedState=n,e=Os(),vl.lanes|=e,ds|=e,n)}function Po(e,t,n,r){return Gn(n,t)?n:null!==dl.current?(e=zo(e,n,r),Gn(e,t)||(zi=!0),e):42&yl?(e=Os(),vl.lanes|=e,ds|=e,t):(zi=!0,e.memoizedState=n)}function _o(e,t,n,r,a){var l=O.p;O.p=0!==l&&8>l?l:8;var o,i,u,s=R.T,c={};R.T=c,$o(e,!1,t,n);try{var f=a(),d=R.S;null!==d&&d(c,f),null!==f&&"object"==typeof f&&"function"==typeof f.then?jo(e,t,(o=r,i=[],u={status:"pending",value:null,reason:null,then:function(e){i.push(e)}},f.then(function(){u.status="fulfilled",u.value=o;for(var e=0;e<i.length;e++)(0,i[e])(o)},function(e){for(u.status="rejected",u.reason=e,e=0;e<i.length;e++)(0,i[e])(void 0)}),u),Rs()):jo(e,t,r,Rs())}catch(n){jo(e,t,{then:function(){},status:"rejected",reason:n},Rs())}finally{O.p=l,R.T=s}}function No(){}function Lo(e,t,n,r){if(5!==e.tag)throw Error(o(476));var a=To(e).queue;_o(e,a,t,I,null===n?No:function(){return Ao(e),n(r)})}function To(e){var t=e.memoizedState;if(null!==t)return t;var n={};return(t={memoizedState:I,baseState:I,baseQueue:null,queue:{pending:null,lanes:0,dispatch:null,lastRenderedReducer:Hl,lastRenderedState:I},next:null}).next={memoizedState:n,baseState:n,baseQueue:null,queue:{pending:null,lanes:0,dispatch:null,lastRenderedReducer:Hl,lastRenderedState:n},next:null},e.memoizedState=t,null!==(e=e.alternate)&&(e.memoizedState=t),t}function Ao(e){jo(e,To(e).next.queue,{},Rs())}function Ro(){return Ca(Gf)}function Oo(){return Fl().memoizedState}function Io(){return Fl().memoizedState}function Do(e){for(var t=e.return;null!==t;){switch(t.tag){case 24:case 3:var n=Rs(),r=al(t,e=rl(n),n);return null!==r&&(Is(r,0,n),ll(r,t,n)),t={cache:Aa()},void(e.payload=t)}t=t.return}}function Mo(e,t,n){var r=Rs();n={lane:r,revertLane:0,action:n,hasEagerState:!1,eagerState:null,next:null},Uo(e)?Ho(t,n):null!==(n=Nr(e,t,n,r))&&(Is(n,0,r),Bo(n,t,r))}function Fo(e,t,n){jo(e,t,n,Rs())}function jo(e,t,n,r){var a={lane:r,revertLane:0,action:n,hasEagerState:!1,eagerState:null,next:null};if(Uo(e))Ho(t,a);else{var l=e.alternate;if(0===e.lanes&&(null===l||0===l.lanes)&&null!==(l=t.lastRenderedReducer))try{var o=t.lastRenderedState,i=l(o,n);if(a.hasEagerState=!0,a.eagerState=i,Gn(i,o))return _r(e,t,a,0),null===ns&&Pr(),!1}catch(e){}if(null!==(n=Nr(e,t,a,r)))return Is(n,0,r),Bo(n,t,r),!0}return!1}function $o(e,t,n,r){if(r={lane:2,revertLane:_c(),action:r,hasEagerState:!1,eagerState:null,next:null},Uo(e)){if(t)throw Error(o(479))}else null!==(t=Nr(e,n,r,2))&&Is(t,0,2)}function Uo(e){var t=e.alternate;return e===vl||null!==t&&t===vl}function Ho(e,t){wl=kl=!0;var n=e.pending;null===n?t.next=t:(t.next=n.next,n.next=t),e.pending=t}function Bo(e,t,n){if(4194048&n){var r=t.lanes;n|=r&=e.pendingLanes,t.lanes=n,ze(e,n)}}var Vo={readContext:Ca,use:$l,useCallback:_l,useContext:_l,useEffect:_l,useImperativeHandle:_l,useLayoutEffect:_l,useInsertionEffect:_l,useMemo:_l,useReducer:_l,useRef:_l,useState:_l,useDebugValue:_l,useDeferredValue:_l,useTransition:_l,useSyncExternalStore:_l,useId:_l,useHostTransitionStatus:_l,useFormState:_l,useActionState:_l,useOptimistic:_l,useMemoCache:_l,useCacheRefresh:_l},Wo={readContext:Ca,use:$l,useCallback:function(e,t){return Ml().memoizedState=[e,void 0===t?null:t],e},useContext:Ca,useEffect:yo,useImperativeHandle:function(e,t,n){n=null!=n?n.concat([e]):null,mo(4194308,4,ko.bind(null,t,e),n)},useLayoutEffect:function(e,t){return mo(4194308,4,e,t)},useInsertionEffect:function(e,t){mo(4,2,e,t)},useMemo:function(e,t){var n=Ml();t=void 0===t?null:t;var r=e();if(xl){fe(!0);try{e()}finally{fe(!1)}}return n.memoizedState=[r,t],r},useReducer:function(e,t,n){var r=Ml();if(void 0!==n){var a=n(t);if(xl){fe(!0);try{n(t)}finally{fe(!1)}}}else a=t;return r.memoizedState=r.baseState=a,e={pending:null,lanes:0,dispatch:null,lastRenderedReducer:e,lastRenderedState:a},r.queue=e,e=e.dispatch=Mo.bind(null,vl,e),[r.memoizedState,e]},useRef:function(e){return e={current:e},Ml().memoizedState=e},useState:function(e){var t=(e=Zl(e)).queue,n=Fo.bind(null,vl,t);return t.dispatch=n,[e.memoizedState,n]},useDebugValue:xo,useDeferredValue:function(e,t){return zo(Ml(),e,t)},useTransition:function(){var e=Zl(!1);return e=_o.bind(null,vl,e.queue,!0,!1),Ml().memoizedState=e,[!1,e]},useSyncExternalStore:function(e,t,n){var r=vl,a=Ml();if(aa){if(void 0===n)throw Error(o(407));n=n()}else{if(n=t(),null===ns)throw Error(o(349));124&as||ql(r,t,n)}a.memoizedState=n;var l={value:n,getSnapshot:t};return a.queue=l,yo(Yl.bind(null,r,l,e),[e]),r.flags|=2048,po(9,{destroy:void 0,resource:void 0},Gl.bind(null,r,l,n,t),null),n},useId:function(){var e=Ml(),t=ns.identifierPrefix;if(aa){var n=Xr;t="«"+t+"R"+(n=(Kr&~(1<<32-de(Kr)-1)).toString(32)+n),0<(n=El++)&&(t+="H"+n.toString(32)),t+="»"}else t="«"+t+"r"+(n=Pl++).toString(32)+"»";return e.memoizedState=t},useHostTransitionStatus:Ro,useFormState:io,useActionState:io,useOptimistic:function(e){var t=Ml();t.memoizedState=t.baseState=e;var n={pending:null,lanes:0,dispatch:null,lastRenderedReducer:null,lastRenderedState:null};return t.queue=n,t=$o.bind(null,vl,!0,n),n.dispatch=t,[e,t]},useMemoCache:Ul,useCacheRefresh:function(){return Ml().memoizedState=Do.bind(null,vl)}},Qo={readContext:Ca,use:$l,useCallback:Eo,useContext:Ca,useEffect:vo,useImperativeHandle:wo,useInsertionEffect:bo,useLayoutEffect:So,useMemo:Co,useReducer:Bl,useRef:ho,useState:function(){return Bl(Hl)},useDebugValue:xo,useDeferredValue:function(e,t){return Po(Fl(),bl.memoizedState,e,t)},useTransition:function(){var e=Bl(Hl)[0],t=Fl().memoizedState;return["boolean"==typeof e?e:jl(e),t]},useSyncExternalStore:Ql,useId:Oo,useHostTransitionStatus:Ro,useFormState:uo,useActionState:uo,useOptimistic:function(e,t){return Jl(Fl(),0,e,t)},useMemoCache:Ul,useCacheRefresh:Io},qo={readContext:Ca,use:$l,useCallback:Eo,useContext:Ca,useEffect:vo,useImperativeHandle:wo,useInsertionEffect:bo,useLayoutEffect:So,useMemo:Co,useReducer:Wl,useRef:ho,useState:function(){return Wl(Hl)},useDebugValue:xo,useDeferredValue:function(e,t){var n=Fl();return null===bl?zo(n,e,t):Po(n,bl.memoizedState,e,t)},useTransition:function(){var e=Wl(Hl)[0],t=Fl().memoizedState;return["boolean"==typeof e?e:jl(e),t]},useSyncExternalStore:Ql,useId:Oo,useHostTransitionStatus:Ro,useFormState:fo,useActionState:fo,useOptimistic:function(e,t){var n=Fl();return null!==bl?Jl(n,0,e,t):(n.baseState=e,[e,n.queue.dispatch])},useMemoCache:Ul,useCacheRefresh:Io},Go=null,Yo=0;function Ko(e){var t=Yo;return Yo+=1,null===Go&&(Go=[]),Ka(Go,e,t)}function Xo(e,t){t=t.props.ref,e.ref=void 0!==t?t:null}function Zo(e,t){if(t.$$typeof===d)throw Error(o(525));throw e=Object.prototype.toString.call(t),Error(o(31,"[object Object]"===e?"object with keys {"+Object.keys(t).join(", ")+"}":e))}function Jo(e){return(0,e._init)(e._payload)}function ei(e){function t(t,n){if(e){var r=t.deletions;null===r?(t.deletions=[n],t.flags|=16):r.push(n)}}function n(n,r){if(!e)return null;for(;null!==r;)t(n,r),r=r.sibling;return null}function r(e){for(var t=new Map;null!==e;)null!==e.key?t.set(e.key,e):t.set(e.index,e),e=e.sibling;return t}function a(e,t){return(e=Mr(e,t)).index=0,e.sibling=null,e}function l(t,n,r){return t.index=r,e?null!==(r=t.alternate)?(r=r.index)<n?(t.flags|=67108866,n):r:(t.flags|=67108866,n):(t.flags|=1048576,n)}function i(t){return e&&null===t.alternate&&(t.flags|=67108866),t}function u(e,t,n,r){return null===t||6!==t.tag?((t=Ur(n,e.mode,r)).return=e,t):((t=a(t,n)).return=e,t)}function s(e,t,n,r){var l=n.type;return l===m?f(e,t,n.props.children,r,n.key):null!==t&&(t.elementType===l||"object"==typeof l&&null!==l&&l.$$typeof===C&&Jo(l)===t.type)?(Xo(t=a(t,n.props),n),t.return=e,t):(Xo(t=jr(n.type,n.key,n.props,null,e.mode,r),n),t.return=e,t)}function c(e,t,n,r){return null===t||4!==t.tag||t.stateNode.containerInfo!==n.containerInfo||t.stateNode.implementation!==n.implementation?((t=Hr(n,e.mode,r)).return=e,t):((t=a(t,n.children||[])).return=e,t)}function f(e,t,n,r,l){return null===t||7!==t.tag?((t=$r(n,e.mode,r,l)).return=e,t):((t=a(t,n)).return=e,t)}function d(e,t,n){if("string"==typeof t&&""!==t||"number"==typeof t||"bigint"==typeof t)return(t=Ur(""+t,e.mode,n)).return=e,t;if("object"==typeof t&&null!==t){switch(t.$$typeof){case p:return Xo(n=jr(t.type,t.key,t.props,null,e.mode,n),t),n.return=e,n;case h:return(t=Hr(t,e.mode,n)).return=e,t;case C:return d(e,t=(0,t._init)(t._payload),n)}if(A(t)||N(t))return(t=$r(t,e.mode,n,null)).return=e,t;if("function"==typeof t.then)return d(e,Ko(t),n);if(t.$$typeof===S)return d(e,za(e,t),n);Zo(e,t)}return null}function g(e,t,n,r){var a=null!==t?t.key:null;if("string"==typeof n&&""!==n||"number"==typeof n||"bigint"==typeof n)return null!==a?null:u(e,t,""+n,r);if("object"==typeof n&&null!==n){switch(n.$$typeof){case p:return n.key===a?s(e,t,n,r):null;case h:return n.key===a?c(e,t,n,r):null;case C:return g(e,t,n=(a=n._init)(n._payload),r)}if(A(n)||N(n))return null!==a?null:f(e,t,n,r,null);if("function"==typeof n.then)return g(e,t,Ko(n),r);if(n.$$typeof===S)return g(e,t,za(e,n),r);Zo(e,n)}return null}function y(e,t,n,r,a){if("string"==typeof r&&""!==r||"number"==typeof r||"bigint"==typeof r)return u(t,e=e.get(n)||null,""+r,a);if("object"==typeof r&&null!==r){switch(r.$$typeof){case p:return s(t,e=e.get(null===r.key?n:r.key)||null,r,a);case h:return c(t,e=e.get(null===r.key?n:r.key)||null,r,a);case C:return y(e,t,n,r=(0,r._init)(r._payload),a)}if(A(r)||N(r))return f(t,e=e.get(n)||null,r,a,null);if("function"==typeof r.then)return y(e,t,n,Ko(r),a);if(r.$$typeof===S)return y(e,t,n,za(t,r),a);Zo(t,r)}return null}function v(u,s,c,f){if("object"==typeof c&&null!==c&&c.type===m&&null===c.key&&(c=c.props.children),"object"==typeof c&&null!==c){switch(c.$$typeof){case p:e:{for(var b=c.key;null!==s;){if(s.key===b){if((b=c.type)===m){if(7===s.tag){n(u,s.sibling),(f=a(s,c.props.children)).return=u,u=f;break e}}else if(s.elementType===b||"object"==typeof b&&null!==b&&b.$$typeof===C&&Jo(b)===s.type){n(u,s.sibling),Xo(f=a(s,c.props),c),f.return=u,u=f;break e}n(u,s);break}t(u,s),s=s.sibling}c.type===m?((f=$r(c.props.children,u.mode,f,c.key)).return=u,u=f):(Xo(f=jr(c.type,c.key,c.props,null,u.mode,f),c),f.return=u,u=f)}return i(u);case h:e:{for(b=c.key;null!==s;){if(s.key===b){if(4===s.tag&&s.stateNode.containerInfo===c.containerInfo&&s.stateNode.implementation===c.implementation){n(u,s.sibling),(f=a(s,c.children||[])).return=u,u=f;break e}n(u,s);break}t(u,s),s=s.sibling}(f=Hr(c,u.mode,f)).return=u,u=f}return i(u);case C:return v(u,s,c=(b=c._init)(c._payload),f)}if(A(c))return function(a,o,i,u){for(var s=null,c=null,f=o,p=o=0,h=null;null!==f&&p<i.length;p++){f.index>p?(h=f,f=null):h=f.sibling;var m=g(a,f,i[p],u);if(null===m){null===f&&(f=h);break}e&&f&&null===m.alternate&&t(a,f),o=l(m,o,p),null===c?s=m:c.sibling=m,c=m,f=h}if(p===i.length)return n(a,f),aa&&Zr(a,p),s;if(null===f){for(;p<i.length;p++)null!==(f=d(a,i[p],u))&&(o=l(f,o,p),null===c?s=f:c.sibling=f,c=f);return aa&&Zr(a,p),s}for(f=r(f);p<i.length;p++)null!==(h=y(f,a,p,i[p],u))&&(e&&null!==h.alternate&&f.delete(null===h.key?p:h.key),o=l(h,o,p),null===c?s=h:c.sibling=h,c=h);return e&&f.forEach(function(e){return t(a,e)}),aa&&Zr(a,p),s}(u,s,c,f);if(N(c)){if("function"!=typeof(b=N(c)))throw Error(o(150));return function(a,i,u,s){if(null==u)throw Error(o(151));for(var c=null,f=null,p=i,h=i=0,m=null,v=u.next();null!==p&&!v.done;h++,v=u.next()){p.index>h?(m=p,p=null):m=p.sibling;var b=g(a,p,v.value,s);if(null===b){null===p&&(p=m);break}e&&p&&null===b.alternate&&t(a,p),i=l(b,i,h),null===f?c=b:f.sibling=b,f=b,p=m}if(v.done)return n(a,p),aa&&Zr(a,h),c;if(null===p){for(;!v.done;h++,v=u.next())null!==(v=d(a,v.value,s))&&(i=l(v,i,h),null===f?c=v:f.sibling=v,f=v);return aa&&Zr(a,h),c}for(p=r(p);!v.done;h++,v=u.next())null!==(v=y(p,a,h,v.value,s))&&(e&&null!==v.alternate&&p.delete(null===v.key?h:v.key),i=l(v,i,h),null===f?c=v:f.sibling=v,f=v);return e&&p.forEach(function(e){return t(a,e)}),aa&&Zr(a,h),c}(u,s,c=b.call(c),f)}if("function"==typeof c.then)return v(u,s,Ko(c),f);if(c.$$typeof===S)return v(u,s,za(u,c),f);Zo(u,c)}return"string"==typeof c&&""!==c||"number"==typeof c||"bigint"==typeof c?(c=""+c,null!==s&&6===s.tag?(n(u,s.sibling),(f=a(s,c)).return=u,u=f):(n(u,s),(f=Ur(c,u.mode,f)).return=u,u=f),i(u)):n(u,s)}return function(e,t,n,r){try{Yo=0;var a=v(e,t,n,r);return Go=null,a}catch(t){if(t===Va||t===Qa)throw t;var l=Ir(29,t,null,e.mode);return l.lanes=r,l.return=e,l}}}var ti=ei(!0),ni=ei(!1),ri=F(null),ai=null;function li(e){var t=e.alternate;$(si,1&si.current),$(ri,e),null===ai&&(null===t||null!==dl.current||null!==t.memoizedState)&&(ai=e)}function oi(e){if(22===e.tag){if($(si,si.current),$(ri,e),null===ai){var t=e.alternate;null!==t&&null!==t.memoizedState&&(ai=e)}}else ii()}function ii(){$(si,si.current),$(ri,ri.current)}function ui(e){j(ri),ai===e&&(ai=null),j(si)}var si=F(0);function ci(e){for(var t=e;null!==t;){if(13===t.tag){var n=t.memoizedState;if(null!==n&&(null===(n=n.dehydrated)||"$?"===n.data||gf(n)))return t}else if(19===t.tag&&void 0!==t.memoizedProps.revealOrder){if(128&t.flags)return t}else if(null!==t.child){t.child.return=t,t=t.child;continue}if(t===e)break;for(;null===t.sibling;){if(null===t.return||t.return===e)return null;t=t.return}t.sibling.return=t.return,t=t.sibling}return null}function fi(e,t,n,r){n=null==(n=n(r,t=e.memoizedState))?t:f({},t,n),e.memoizedState=n,0===e.lanes&&(e.updateQueue.baseState=n)}var di={enqueueSetState:function(e,t,n){e=e._reactInternals;var r=Rs(),a=rl(r);a.payload=t,null!=n&&(a.callback=n),null!==(t=al(e,a,r))&&(Is(t,0,r),ll(t,e,r))},enqueueReplaceState:function(e,t,n){e=e._reactInternals;var r=Rs(),a=rl(r);a.tag=1,a.payload=t,null!=n&&(a.callback=n),null!==(t=al(e,a,r))&&(Is(t,0,r),ll(t,e,r))},enqueueForceUpdate:function(e,t){e=e._reactInternals;var n=Rs(),r=rl(n);r.tag=2,null!=t&&(r.callback=t),null!==(t=al(e,r,n))&&(Is(t,0,n),ll(t,e,n))}};function pi(e,t,n,r,a,l,o){return"function"==typeof(e=e.stateNode).shouldComponentUpdate?e.shouldComponentUpdate(r,l,o):!(t.prototype&&t.prototype.isPureReactComponent&&Yn(n,r)&&Yn(a,l))}function hi(e,t,n,r){e=t.state,"function"==typeof t.componentWillReceiveProps&&t.componentWillReceiveProps(n,r),"function"==typeof t.UNSAFE_componentWillReceiveProps&&t.UNSAFE_componentWillReceiveProps(n,r),t.state!==e&&di.enqueueReplaceState(t,t.state,null)}function mi(e,t){var n=t;if("ref"in t)for(var r in n={},t)"ref"!==r&&(n[r]=t[r]);if(e=e.defaultProps)for(var a in n===t&&(n=f({},n)),e)void 0===n[a]&&(n[a]=e[a]);return n}var gi="function"==typeof reportError?reportError:function(e){if("object"==typeof window&&"function"==typeof window.ErrorEvent){var t=new window.ErrorEvent("error",{bubbles:!0,cancelable:!0,message:"object"==typeof e&&null!==e&&"string"==typeof e.message?String(e.message):String(e),error:e});if(!window.dispatchEvent(t))return}else if("object"==typeof process&&"function"==typeof process.emit)return void process.emit("uncaughtException",e);console.error(e)};function yi(e){gi(e)}function vi(e){console.error(e)}function bi(e){gi(e)}function Si(e,t){try{(0,e.onUncaughtError)(t.value,{componentStack:t.stack})}catch(e){setTimeout(function(){throw e})}}function ki(e,t,n){try{(0,e.onCaughtError)(n.value,{componentStack:n.stack,errorBoundary:1===t.tag?t.stateNode:null})}catch(e){setTimeout(function(){throw e})}}function wi(e,t,n){return(n=rl(n)).tag=3,n.payload={element:null},n.callback=function(){Si(e,t)},n}function xi(e){return(e=rl(e)).tag=3,e}function Ei(e,t,n,r){var a=n.type.getDerivedStateFromError;if("function"==typeof a){var l=r.value;e.payload=function(){return a(l)},e.callback=function(){ki(t,n,r)}}var o=n.stateNode;null!==o&&"function"==typeof o.componentDidCatch&&(e.callback=function(){ki(t,n,r),"function"!=typeof a&&(null===xs?xs=new Set([this]):xs.add(this));var e=r.stack;this.componentDidCatch(r.value,{componentStack:null!==e?e:""})})}var Ci=Error(o(461)),zi=!1;function Pi(e,t,n,r){t.child=null===e?ni(t,null,n,r):ti(t,e.child,n,r)}function _i(e,t,n,r,a){n=n.render;var l=t.ref;if("ref"in r){var o={};for(var i in r)"ref"!==i&&(o[i]=r[i])}else o=r;return Ea(t),r=Ll(e,t,n,o,l,a),i=Ol(),null===e||zi?(aa&&i&&ea(t),t.flags|=1,Pi(e,t,r,a),t.child):(Il(e,t,a),Gi(e,t,a))}function Ni(e,t,n,r,a){if(null===e){var l=n.type;return"function"!=typeof l||Dr(l)||void 0!==l.defaultProps||null!==n.compare?((e=jr(n.type,null,r,t,t.mode,a)).ref=t.ref,e.return=t,t.child=e):(t.tag=15,t.type=l,Li(e,t,l,r,a))}if(l=e.child,!Yi(e,a)){var o=l.memoizedProps;if((n=null!==(n=n.compare)?n:Yn)(o,r)&&e.ref===t.ref)return Gi(e,t,a)}return t.flags|=1,(e=Mr(l,r)).ref=t.ref,e.return=t,t.child=e}function Li(e,t,n,r,a){if(null!==e){var l=e.memoizedProps;if(Yn(l,r)&&e.ref===t.ref){if(zi=!1,t.pendingProps=r=l,!Yi(e,a))return t.lanes=e.lanes,Gi(e,t,a);131072&e.flags&&(zi=!0)}}return Oi(e,t,n,r,a)}function Ti(e,t,n){var r=t.pendingProps,a=r.children,l=null!==e?e.memoizedState:null;if("hidden"===r.mode){if(128&t.flags){if(r=null!==l?l.baseLanes|n:n,null!==e){for(a=t.child=e.child,l=0;null!==a;)l=l|a.lanes|a.childLanes,a=a.sibling;t.childLanes=l&~r}else t.childLanes=0,t.child=null;return Ai(e,t,r,n)}if(!(536870912&n))return t.lanes=t.childLanes=536870912,Ai(e,t,null!==l?l.baseLanes|n:n,n);t.memoizedState={baseLanes:0,cachePool:null},null!==e&&Ha(0,null!==l?l.cachePool:null),null!==l?hl(t,l):ml(),oi(t)}else null!==l?(Ha(0,l.cachePool),hl(t,l),ii(),t.memoizedState=null):(null!==e&&Ha(0,null),ml(),ii());return Pi(e,t,a,n),t.child}function Ai(e,t,n,r){var a=Ua();return a=null===a?null:{parent:Ta._currentValue,pool:a},t.memoizedState={baseLanes:n,cachePool:a},null!==e&&Ha(0,null),ml(),oi(t),null!==e&&wa(e,t,r,!0),null}function Ri(e,t){var n=t.ref;if(null===n)null!==e&&null!==e.ref&&(t.flags|=4194816);else{if("function"!=typeof n&&"object"!=typeof n)throw Error(o(284));null!==e&&e.ref===n||(t.flags|=4194816)}}function Oi(e,t,n,r,a){return Ea(t),n=Ll(e,t,n,r,void 0,a),r=Ol(),null===e||zi?(aa&&r&&ea(t),t.flags|=1,Pi(e,t,n,a),t.child):(Il(e,t,a),Gi(e,t,a))}function Ii(e,t,n,r,a,l){return Ea(t),t.updateQueue=null,n=Al(t,r,n,a),Tl(e),r=Ol(),null===e||zi?(aa&&r&&ea(t),t.flags|=1,Pi(e,t,n,l),t.child):(Il(e,t,l),Gi(e,t,l))}function Di(e,t,n,r,a){if(Ea(t),null===t.stateNode){var l=Rr,o=n.contextType;"object"==typeof o&&null!==o&&(l=Ca(o)),l=new n(r,l),t.memoizedState=null!==l.state&&void 0!==l.state?l.state:null,l.updater=di,t.stateNode=l,l._reactInternals=t,(l=t.stateNode).props=r,l.state=t.memoizedState,l.refs={},tl(t),o=n.contextType,l.context="object"==typeof o&&null!==o?Ca(o):Rr,l.state=t.memoizedState,"function"==typeof(o=n.getDerivedStateFromProps)&&(fi(t,n,o,r),l.state=t.memoizedState),"function"==typeof n.getDerivedStateFromProps||"function"==typeof l.getSnapshotBeforeUpdate||"function"!=typeof l.UNSAFE_componentWillMount&&"function"!=typeof l.componentWillMount||(o=l.state,"function"==typeof l.componentWillMount&&l.componentWillMount(),"function"==typeof l.UNSAFE_componentWillMount&&l.UNSAFE_componentWillMount(),o!==l.state&&di.enqueueReplaceState(l,l.state,null),sl(t,r,l,a),ul(),l.state=t.memoizedState),"function"==typeof l.componentDidMount&&(t.flags|=4194308),r=!0}else if(null===e){l=t.stateNode;var i=t.memoizedProps,u=mi(n,i);l.props=u;var s=l.context,c=n.contextType;o=Rr,"object"==typeof c&&null!==c&&(o=Ca(c));var f=n.getDerivedStateFromProps;c="function"==typeof f||"function"==typeof l.getSnapshotBeforeUpdate,i=t.pendingProps!==i,c||"function"!=typeof l.UNSAFE_componentWillReceiveProps&&"function"!=typeof l.componentWillReceiveProps||(i||s!==o)&&hi(t,l,r,o),el=!1;var d=t.memoizedState;l.state=d,sl(t,r,l,a),ul(),s=t.memoizedState,i||d!==s||el?("function"==typeof f&&(fi(t,n,f,r),s=t.memoizedState),(u=el||pi(t,n,u,r,d,s,o))?(c||"function"!=typeof l.UNSAFE_componentWillMount&&"function"!=typeof l.componentWillMount||("function"==typeof l.componentWillMount&&l.componentWillMount(),"function"==typeof l.UNSAFE_componentWillMount&&l.UNSAFE_componentWillMount()),"function"==typeof l.componentDidMount&&(t.flags|=4194308)):("function"==typeof l.componentDidMount&&(t.flags|=4194308),t.memoizedProps=r,t.memoizedState=s),l.props=r,l.state=s,l.context=o,r=u):("function"==typeof l.componentDidMount&&(t.flags|=4194308),r=!1)}else{l=t.stateNode,nl(e,t),c=mi(n,o=t.memoizedProps),l.props=c,f=t.pendingProps,d=l.context,s=n.contextType,u=Rr,"object"==typeof s&&null!==s&&(u=Ca(s)),(s="function"==typeof(i=n.getDerivedStateFromProps)||"function"==typeof l.getSnapshotBeforeUpdate)||"function"!=typeof l.UNSAFE_componentWillReceiveProps&&"function"!=typeof l.componentWillReceiveProps||(o!==f||d!==u)&&hi(t,l,r,u),el=!1,d=t.memoizedState,l.state=d,sl(t,r,l,a),ul();var p=t.memoizedState;o!==f||d!==p||el||null!==e&&null!==e.dependencies&&xa(e.dependencies)?("function"==typeof i&&(fi(t,n,i,r),p=t.memoizedState),(c=el||pi(t,n,c,r,d,p,u)||null!==e&&null!==e.dependencies&&xa(e.dependencies))?(s||"function"!=typeof l.UNSAFE_componentWillUpdate&&"function"!=typeof l.componentWillUpdate||("function"==typeof l.componentWillUpdate&&l.componentWillUpdate(r,p,u),"function"==typeof l.UNSAFE_componentWillUpdate&&l.UNSAFE_componentWillUpdate(r,p,u)),"function"==typeof l.componentDidUpdate&&(t.flags|=4),"function"==typeof l.getSnapshotBeforeUpdate&&(t.flags|=1024)):("function"!=typeof l.componentDidUpdate||o===e.memoizedProps&&d===e.memoizedState||(t.flags|=4),"function"!=typeof l.getSnapshotBeforeUpdate||o===e.memoizedProps&&d===e.memoizedState||(t.flags|=1024),t.memoizedProps=r,t.memoizedState=p),l.props=r,l.state=p,l.context=u,r=c):("function"!=typeof l.componentDidUpdate||o===e.memoizedProps&&d===e.memoizedState||(t.flags|=4),"function"!=typeof l.getSnapshotBeforeUpdate||o===e.memoizedProps&&d===e.memoizedState||(t.flags|=1024),r=!1)}return l=r,Ri(e,t),r=!!(128&t.flags),l||r?(l=t.stateNode,n=r&&"function"!=typeof n.getDerivedStateFromError?null:l.render(),t.flags|=1,null!==e&&r?(t.child=ti(t,e.child,null,a),t.child=ti(t,null,n,a)):Pi(e,t,n,a),t.memoizedState=l.state,e=t.child):e=Gi(e,t,a),e}function Mi(e,t,n,r){return da(),t.flags|=256,Pi(e,t,n,r),t.child}var Fi={dehydrated:null,treeContext:null,retryLane:0,hydrationErrors:null};function ji(e){return{baseLanes:e,cachePool:Ba()}}function $i(e,t,n){return e=null!==e?e.childLanes&~n:0,t&&(e|=ms),e}function Ui(e,t,n){var r,a=t.pendingProps,l=!1,i=!!(128&t.flags);if((r=i)||(r=(null===e||null!==e.memoizedState)&&!!(2&si.current)),r&&(l=!0,t.flags&=-129),r=!!(32&t.flags),t.flags&=-33,null===e){if(aa){if(l?li(t):ii(),aa){var u,s=ra;if(u=s){e:{for(u=s,s=oa;8!==u.nodeType;){if(!s){s=null;break e}if(null===(u=yf(u.nextSibling))){s=null;break e}}s=u}null!==s?(t.memoizedState={dehydrated:s,treeContext:null!==Yr?{id:Kr,overflow:Xr}:null,retryLane:536870912,hydrationErrors:null},(u=Ir(18,null,null,0)).stateNode=s,u.return=t,t.child=u,na=t,ra=null,u=!0):u=!1}u||ua(t)}if(null!==(s=t.memoizedState)&&null!==(s=s.dehydrated))return gf(s)?t.lanes=32:t.lanes=536870912,null;ui(t)}return s=a.children,a=a.fallback,l?(ii(),s=Bi({mode:"hidden",children:s},l=t.mode),a=$r(a,l,n,null),s.return=t,a.return=t,s.sibling=a,t.child=s,(l=t.child).memoizedState=ji(n),l.childLanes=$i(e,r,n),t.memoizedState=Fi,a):(li(t),Hi(t,s))}if(null!==(u=e.memoizedState)&&null!==(s=u.dehydrated)){if(i)256&t.flags?(li(t),t.flags&=-257,t=Vi(e,t,n)):null!==t.memoizedState?(ii(),t.child=e.child,t.flags|=128,t=null):(ii(),l=a.fallback,s=t.mode,a=Bi({mode:"visible",children:a.children},s),(l=$r(l,s,n,null)).flags|=2,a.return=t,l.return=t,a.sibling=l,t.child=a,ti(t,e.child,null,n),(a=t.child).memoizedState=ji(n),a.childLanes=$i(e,r,n),t.memoizedState=Fi,t=l);else if(li(t),gf(s)){if(r=s.nextSibling&&s.nextSibling.dataset)var c=r.dgst;r=c,(a=Error(o(419))).stack="",a.digest=r,ha({value:a,source:null,stack:null}),t=Vi(e,t,n)}else if(zi||wa(e,t,n,!1),r=0!==(n&e.childLanes),zi||r){if(null!==(r=ns)&&0!==(a=0!==((a=42&(a=n&-n)?1:Pe(a))&(r.suspendedLanes|n))?0:a)&&a!==u.retryLane)throw u.retryLane=a,Lr(e,a),Is(r,0,a),Ci;"$?"===s.data||Qs(),t=Vi(e,t,n)}else"$?"===s.data?(t.flags|=192,t.child=e.child,t=null):(e=u.treeContext,ra=yf(s.nextSibling),na=t,aa=!0,la=null,oa=!1,null!==e&&(qr[Gr++]=Kr,qr[Gr++]=Xr,qr[Gr++]=Yr,Kr=e.id,Xr=e.overflow,Yr=t),(t=Hi(t,a.children)).flags|=4096);return t}return l?(ii(),l=a.fallback,s=t.mode,c=(u=e.child).sibling,(a=Mr(u,{mode:"hidden",children:a.children})).subtreeFlags=65011712&u.subtreeFlags,null!==c?l=Mr(c,l):(l=$r(l,s,n,null)).flags|=2,l.return=t,a.return=t,a.sibling=l,t.child=a,a=l,l=t.child,null===(s=e.child.memoizedState)?s=ji(n):(null!==(u=s.cachePool)?(c=Ta._currentValue,u=u.parent!==c?{parent:c,pool:c}:u):u=Ba(),s={baseLanes:s.baseLanes|n,cachePool:u}),l.memoizedState=s,l.childLanes=$i(e,r,n),t.memoizedState=Fi,a):(li(t),e=(n=e.child).sibling,(n=Mr(n,{mode:"visible",children:a.children})).return=t,n.sibling=null,null!==e&&(null===(r=t.deletions)?(t.deletions=[e],t.flags|=16):r.push(e)),t.child=n,t.memoizedState=null,n)}function Hi(e,t){return(t=Bi({mode:"visible",children:t},e.mode)).return=e,e.child=t}function Bi(e,t){return(e=Ir(22,e,null,t)).lanes=0,e.stateNode={_visibility:1,_pendingMarkers:null,_retryCache:null,_transitions:null},e}function Vi(e,t,n){return ti(t,e.child,null,n),(e=Hi(t,t.pendingProps.children)).flags|=2,t.memoizedState=null,e}function Wi(e,t,n){e.lanes|=t;var r=e.alternate;null!==r&&(r.lanes|=t),Sa(e.return,t,n)}function Qi(e,t,n,r,a){var l=e.memoizedState;null===l?e.memoizedState={isBackwards:t,rendering:null,renderingStartTime:0,last:r,tail:n,tailMode:a}:(l.isBackwards=t,l.rendering=null,l.renderingStartTime=0,l.last=r,l.tail=n,l.tailMode=a)}function qi(e,t,n){var r=t.pendingProps,a=r.revealOrder,l=r.tail;if(Pi(e,t,r.children,n),2&(r=si.current))r=1&r|2,t.flags|=128;else{if(null!==e&&128&e.flags)e:for(e=t.child;null!==e;){if(13===e.tag)null!==e.memoizedState&&Wi(e,n,t);else if(19===e.tag)Wi(e,n,t);else if(null!==e.child){e.child.return=e,e=e.child;continue}if(e===t)break e;for(;null===e.sibling;){if(null===e.return||e.return===t)break e;e=e.return}e.sibling.return=e.return,e=e.sibling}r&=1}switch($(si,r),a){case"forwards":for(n=t.child,a=null;null!==n;)null!==(e=n.alternate)&&null===ci(e)&&(a=n),n=n.sibling;null===(n=a)?(a=t.child,t.child=null):(a=n.sibling,n.sibling=null),Qi(t,!1,a,n,l);break;case"backwards":for(n=null,a=t.child,t.child=null;null!==a;){if(null!==(e=a.alternate)&&null===ci(e)){t.child=a;break}e=a.sibling,a.sibling=n,n=a,a=e}Qi(t,!0,n,null,l);break;case"together":Qi(t,!1,null,null,void 0);break;default:t.memoizedState=null}return t.child}function Gi(e,t,n){if(null!==e&&(t.dependencies=e.dependencies),ds|=t.lanes,0===(n&t.childLanes)){if(null===e)return null;if(wa(e,t,n,!1),0===(n&t.childLanes))return null}if(null!==e&&t.child!==e.child)throw Error(o(153));if(null!==t.child){for(n=Mr(e=t.child,e.pendingProps),t.child=n,n.return=t;null!==e.sibling;)e=e.sibling,(n=n.sibling=Mr(e,e.pendingProps)).return=t;n.sibling=null}return t.child}function Yi(e,t){return 0!==(e.lanes&t)||!(null===(e=e.dependencies)||!xa(e))}function Ki(e,t,n){if(null!==e)if(e.memoizedProps!==t.pendingProps)zi=!0;else{if(!(Yi(e,n)||128&t.flags))return zi=!1,function(e,t,n){switch(t.tag){case 3:W(t,t.stateNode.containerInfo),va(0,Ta,e.memoizedState.cache),da();break;case 27:case 5:q(t);break;case 4:W(t,t.stateNode.containerInfo);break;case 10:va(0,t.type,t.memoizedProps.value);break;case 13:var r=t.memoizedState;if(null!==r)return null!==r.dehydrated?(li(t),t.flags|=128,null):0!==(n&t.child.childLanes)?Ui(e,t,n):(li(t),null!==(e=Gi(e,t,n))?e.sibling:null);li(t);break;case 19:var a=!!(128&e.flags);if((r=0!==(n&t.childLanes))||(wa(e,t,n,!1),r=0!==(n&t.childLanes)),a){if(r)return qi(e,t,n);t.flags|=128}if(null!==(a=t.memoizedState)&&(a.rendering=null,a.tail=null,a.lastEffect=null),$(si,si.current),r)break;return null;case 22:case 23:return t.lanes=0,Ti(e,t,n);case 24:va(0,Ta,e.memoizedState.cache)}return Gi(e,t,n)}(e,t,n);zi=!!(131072&e.flags)}else zi=!1,aa&&1048576&t.flags&&Jr(t,Qr,t.index);switch(t.lanes=0,t.tag){case 16:e:{e=t.pendingProps;var r=t.elementType,a=r._init;if(r=a(r._payload),t.type=r,"function"!=typeof r){if(null!=r){if((a=r.$$typeof)===k){t.tag=11,t=_i(null,t,r,e,n);break e}if(a===E){t.tag=14,t=Ni(null,t,r,e,n);break e}}throw t=T(r)||r,Error(o(306,t,""))}Dr(r)?(e=mi(r,e),t.tag=1,t=Di(null,t,r,e,n)):(t.tag=0,t=Oi(null,t,r,e,n))}return t;case 0:return Oi(e,t,t.type,t.pendingProps,n);case 1:return Di(e,t,r=t.type,a=mi(r,t.pendingProps),n);case 3:e:{if(W(t,t.stateNode.containerInfo),null===e)throw Error(o(387));r=t.pendingProps;var l=t.memoizedState;a=l.element,nl(e,t),sl(t,r,null,n);var i=t.memoizedState;if(r=i.cache,va(0,Ta,r),r!==l.cache&&ka(t,[Ta],n,!0),ul(),r=i.element,l.isDehydrated){if(l={element:r,isDehydrated:!1,cache:i.cache},t.updateQueue.baseState=l,t.memoizedState=l,256&t.flags){t=Mi(e,t,r,n);break e}if(r!==a){ha(a=xr(Error(o(424)),t)),t=Mi(e,t,r,n);break e}for(e=9===(e=t.stateNode.containerInfo).nodeType?e.body:"HTML"===e.nodeName?e.ownerDocument.body:e,ra=yf(e.firstChild),na=t,aa=!0,la=null,oa=!0,n=ni(t,null,r,n),t.child=n;n;)n.flags=-3&n.flags|4096,n=n.sibling}else{if(da(),r===a){t=Gi(e,t,n);break e}Pi(e,t,r,n)}t=t.child}return t;case 26:return Ri(e,t),null===e?(n=_f(t.type,null,t.pendingProps,null))?t.memoizedState=n:aa||(n=t.type,e=t.pendingProps,(r=nf(B.current).createElement(n))[Te]=t,r[Ae]=e,Jc(r,n,e),Ve(r),t.stateNode=r):t.memoizedState=_f(t.type,e.memoizedProps,t.pendingProps,e.memoizedState),null;case 27:return q(t),null===e&&aa&&(r=t.stateNode=Sf(t.type,t.pendingProps,B.current),na=t,oa=!0,a=ra,pf(t.type)?(vf=a,ra=yf(r.firstChild)):ra=a),Pi(e,t,t.pendingProps.children,n),Ri(e,t),null===e&&(t.flags|=4194304),t.child;case 5:return null===e&&aa&&((a=r=ra)&&(null!==(r=function(e,t,n,r){for(;1===e.nodeType;){var a=n;if(e.nodeName.toLowerCase()!==t.toLowerCase()){if(!r&&("INPUT"!==e.nodeName||"hidden"!==e.type))break}else if(r){if(!e[Fe])switch(t){case"meta":if(!e.hasAttribute("itemprop"))break;return e;case"link":if("stylesheet"===(l=e.getAttribute("rel"))&&e.hasAttribute("data-precedence"))break;if(l!==a.rel||e.getAttribute("href")!==(null==a.href||""===a.href?null:a.href)||e.getAttribute("crossorigin")!==(null==a.crossOrigin?null:a.crossOrigin)||e.getAttribute("title")!==(null==a.title?null:a.title))break;return e;case"style":if(e.hasAttribute("data-precedence"))break;return e;case"script":if(((l=e.getAttribute("src"))!==(null==a.src?null:a.src)||e.getAttribute("type")!==(null==a.type?null:a.type)||e.getAttribute("crossorigin")!==(null==a.crossOrigin?null:a.crossOrigin))&&l&&e.hasAttribute("async")&&!e.hasAttribute("itemprop"))break;return e;default:return e}}else{if("input"!==t||"hidden"!==e.type)return e;var l=null==a.name?null:""+a.name;if("hidden"===a.type&&e.getAttribute("name")===l)return e}if(null===(e=yf(e.nextSibling)))break}return null}(r,t.type,t.pendingProps,oa))?(t.stateNode=r,na=t,ra=yf(r.firstChild),oa=!1,a=!0):a=!1),a||ua(t)),q(t),a=t.type,l=t.pendingProps,i=null!==e?e.memoizedProps:null,r=l.children,lf(a,l)?r=null:null!==i&&lf(a,i)&&(t.flags|=32),null!==t.memoizedState&&(a=Ll(e,t,Rl,null,null,n),Gf._currentValue=a),Ri(e,t),Pi(e,t,r,n),t.child;case 6:return null===e&&aa&&((e=n=ra)&&(null!==(n=function(e,t,n){if(""===t)return null;for(;3!==e.nodeType;){if((1!==e.nodeType||"INPUT"!==e.nodeName||"hidden"!==e.type)&&!n)return null;if(null===(e=yf(e.nextSibling)))return null}return e}(n,t.pendingProps,oa))?(t.stateNode=n,na=t,ra=null,e=!0):e=!1),e||ua(t)),null;case 13:return Ui(e,t,n);case 4:return W(t,t.stateNode.containerInfo),r=t.pendingProps,null===e?t.child=ti(t,null,r,n):Pi(e,t,r,n),t.child;case 11:return _i(e,t,t.type,t.pendingProps,n);case 7:return Pi(e,t,t.pendingProps,n),t.child;case 8:case 12:return Pi(e,t,t.pendingProps.children,n),t.child;case 10:return r=t.pendingProps,va(0,t.type,r.value),Pi(e,t,r.children,n),t.child;case 9:return a=t.type._context,r=t.pendingProps.children,Ea(t),r=r(a=Ca(a)),t.flags|=1,Pi(e,t,r,n),t.child;case 14:return Ni(e,t,t.type,t.pendingProps,n);case 15:return Li(e,t,t.type,t.pendingProps,n);case 19:return qi(e,t,n);case 31:return r=t.pendingProps,n=t.mode,r={mode:r.mode,children:r.children},null===e?((n=Bi(r,n)).ref=t.ref,t.child=n,n.return=t,t=n):((n=Mr(e.child,r)).ref=t.ref,t.child=n,n.return=t,t=n),t;case 22:return Ti(e,t,n);case 24:return Ea(t),r=Ca(Ta),null===e?(null===(a=Ua())&&(a=ns,l=Aa(),a.pooledCache=l,l.refCount++,null!==l&&(a.pooledCacheLanes|=n),a=l),t.memoizedState={parent:r,cache:a},tl(t),va(0,Ta,a)):(0!==(e.lanes&n)&&(nl(e,t),sl(t,null,null,n),ul()),a=e.memoizedState,l=t.memoizedState,a.parent!==r?(a={parent:r,cache:r},t.memoizedState=a,0===t.lanes&&(t.memoizedState=t.updateQueue.baseState=a),va(0,Ta,r)):(r=l.cache,va(0,Ta,r),r!==a.cache&&ka(t,[Ta],n,!0))),Pi(e,t,t.pendingProps.children,n),t.child;case 29:throw t.pendingProps}throw Error(o(156,t.tag))}function Xi(e){e.flags|=4}function Zi(e,t){if("stylesheet"!==t.type||4&t.state.loading)e.flags&=-16777217;else if(e.flags|=16777216,!Uf(t)){if(null!==(t=ri.current)&&((4194048&as)===as?null!==ai:(62914560&as)!==as&&!(536870912&as)||t!==ai))throw Xa=qa,Wa;e.flags|=8192}}function Ji(e,t){null!==t&&(e.flags|=4),16384&e.flags&&(t=22!==e.tag?we():536870912,e.lanes|=t,gs|=t)}function eu(e,t){if(!aa)switch(e.tailMode){case"hidden":t=e.tail;for(var n=null;null!==t;)null!==t.alternate&&(n=t),t=t.sibling;null===n?e.tail=null:n.sibling=null;break;case"collapsed":n=e.tail;for(var r=null;null!==n;)null!==n.alternate&&(r=n),n=n.sibling;null===r?t||null===e.tail?e.tail=null:e.tail.sibling=null:r.sibling=null}}function tu(e){var t=null!==e.alternate&&e.alternate.child===e.child,n=0,r=0;if(t)for(var a=e.child;null!==a;)n|=a.lanes|a.childLanes,r|=65011712&a.subtreeFlags,r|=65011712&a.flags,a.return=e,a=a.sibling;else for(a=e.child;null!==a;)n|=a.lanes|a.childLanes,r|=a.subtreeFlags,r|=a.flags,a.return=e,a=a.sibling;return e.subtreeFlags|=r,e.childLanes=n,t}function nu(e,t,n){var r=t.pendingProps;switch(ta(t),t.tag){case 31:case 16:case 15:case 0:case 11:case 7:case 8:case 12:case 9:case 14:case 1:return tu(t),null;case 3:return n=t.stateNode,r=null,null!==e&&(r=e.memoizedState.cache),t.memoizedState.cache!==r&&(t.flags|=2048),ba(Ta),Q(),n.pendingContext&&(n.context=n.pendingContext,n.pendingContext=null),null!==e&&null!==e.child||(fa(t)?Xi(t):null===e||e.memoizedState.isDehydrated&&!(256&t.flags)||(t.flags|=1024,pa())),tu(t),null;case 26:return n=t.memoizedState,null===e?(Xi(t),null!==n?(tu(t),Zi(t,n)):(tu(t),t.flags&=-16777217)):n?n!==e.memoizedState?(Xi(t),tu(t),Zi(t,n)):(tu(t),t.flags&=-16777217):(e.memoizedProps!==r&&Xi(t),tu(t),t.flags&=-16777217),null;case 27:G(t),n=B.current;var a=t.type;if(null!==e&&null!=t.stateNode)e.memoizedProps!==r&&Xi(t);else{if(!r){if(null===t.stateNode)throw Error(o(166));return tu(t),null}e=U.current,fa(t)?sa(t):(e=Sf(a,r,n),t.stateNode=e,Xi(t))}return tu(t),null;case 5:if(G(t),n=t.type,null!==e&&null!=t.stateNode)e.memoizedProps!==r&&Xi(t);else{if(!r){if(null===t.stateNode)throw Error(o(166));return tu(t),null}if(e=U.current,fa(t))sa(t);else{switch(a=nf(B.current),e){case 1:e=a.createElementNS("http://www.w3.org/2000/svg",n);break;case 2:e=a.createElementNS("http://www.w3.org/1998/Math/MathML",n);break;default:switch(n){case"svg":e=a.createElementNS("http://www.w3.org/2000/svg",n);break;case"math":e=a.createElementNS("http://www.w3.org/1998/Math/MathML",n);break;case"script":(e=a.createElement("div")).innerHTML="<script><\/script>",e=e.removeChild(e.firstChild);break;case"select":e="string"==typeof r.is?a.createElement("select",{is:r.is}):a.createElement("select"),r.multiple?e.multiple=!0:r.size&&(e.size=r.size);break;default:e="string"==typeof r.is?a.createElement(n,{is:r.is}):a.createElement(n)}}e[Te]=t,e[Ae]=r;e:for(a=t.child;null!==a;){if(5===a.tag||6===a.tag)e.appendChild(a.stateNode);else if(4!==a.tag&&27!==a.tag&&null!==a.child){a.child.return=a,a=a.child;continue}if(a===t)break e;for(;null===a.sibling;){if(null===a.return||a.return===t)break e;a=a.return}a.sibling.return=a.return,a=a.sibling}t.stateNode=e;e:switch(Jc(e,n,r),n){case"button":case"input":case"select":case"textarea":e=!!r.autoFocus;break e;case"img":e=!0;break e;default:e=!1}e&&Xi(t)}}return tu(t),t.flags&=-16777217,null;case 6:if(e&&null!=t.stateNode)e.memoizedProps!==r&&Xi(t);else{if("string"!=typeof r&&null===t.stateNode)throw Error(o(166));if(e=B.current,fa(t)){if(e=t.stateNode,n=t.memoizedProps,r=null,null!==(a=na))switch(a.tag){case 27:case 5:r=a.memoizedProps}e[Te]=t,(e=!!(e.nodeValue===n||null!==r&&!0===r.suppressHydrationWarning||Yc(e.nodeValue,n)))||ua(t)}else(e=nf(e).createTextNode(r))[Te]=t,t.stateNode=e}return tu(t),null;case 13:if(r=t.memoizedState,null===e||null!==e.memoizedState&&null!==e.memoizedState.dehydrated){if(a=fa(t),null!==r&&null!==r.dehydrated){if(null===e){if(!a)throw Error(o(318));if(!(a=null!==(a=t.memoizedState)?a.dehydrated:null))throw Error(o(317));a[Te]=t}else da(),!(128&t.flags)&&(t.memoizedState=null),t.flags|=4;tu(t),a=!1}else a=pa(),null!==e&&null!==e.memoizedState&&(e.memoizedState.hydrationErrors=a),a=!0;if(!a)return 256&t.flags?(ui(t),t):(ui(t),null)}if(ui(t),128&t.flags)return t.lanes=n,t;if(n=null!==r,e=null!==e&&null!==e.memoizedState,n){a=null,null!==(r=t.child).alternate&&null!==r.alternate.memoizedState&&null!==r.alternate.memoizedState.cachePool&&(a=r.alternate.memoizedState.cachePool.pool);var l=null;null!==r.memoizedState&&null!==r.memoizedState.cachePool&&(l=r.memoizedState.cachePool.pool),l!==a&&(r.flags|=2048)}return n!==e&&n&&(t.child.flags|=8192),Ji(t,t.updateQueue),tu(t),null;case 4:return Q(),null===e&&jc(t.stateNode.containerInfo),tu(t),null;case 10:return ba(t.type),tu(t),null;case 19:if(j(si),null===(a=t.memoizedState))return tu(t),null;if(r=!!(128&t.flags),null===(l=a.rendering))if(r)eu(a,!1);else{if(0!==fs||null!==e&&128&e.flags)for(e=t.child;null!==e;){if(null!==(l=ci(e))){for(t.flags|=128,eu(a,!1),e=l.updateQueue,t.updateQueue=e,Ji(t,e),t.subtreeFlags=0,e=n,n=t.child;null!==n;)Fr(n,e),n=n.sibling;return $(si,1&si.current|2),t.child}e=e.sibling}null!==a.tail&&ee()>ks&&(t.flags|=128,r=!0,eu(a,!1),t.lanes=4194304)}else{if(!r)if(null!==(e=ci(l))){if(t.flags|=128,r=!0,e=e.updateQueue,t.updateQueue=e,Ji(t,e),eu(a,!0),null===a.tail&&"hidden"===a.tailMode&&!l.alternate&&!aa)return tu(t),null}else 2*ee()-a.renderingStartTime>ks&&536870912!==n&&(t.flags|=128,r=!0,eu(a,!1),t.lanes=4194304);a.isBackwards?(l.sibling=t.child,t.child=l):(null!==(e=a.last)?e.sibling=l:t.child=l,a.last=l)}return null!==a.tail?(t=a.tail,a.rendering=t,a.tail=t.sibling,a.renderingStartTime=ee(),t.sibling=null,e=si.current,$(si,r?1&e|2:1&e),t):(tu(t),null);case 22:case 23:return ui(t),gl(),r=null!==t.memoizedState,null!==e?null!==e.memoizedState!==r&&(t.flags|=8192):r&&(t.flags|=8192),r?!!(536870912&n)&&!(128&t.flags)&&(tu(t),6&t.subtreeFlags&&(t.flags|=8192)):tu(t),null!==(n=t.updateQueue)&&Ji(t,n.retryQueue),n=null,null!==e&&null!==e.memoizedState&&null!==e.memoizedState.cachePool&&(n=e.memoizedState.cachePool.pool),r=null,null!==t.memoizedState&&null!==t.memoizedState.cachePool&&(r=t.memoizedState.cachePool.pool),r!==n&&(t.flags|=2048),null!==e&&j($a),null;case 24:return n=null,null!==e&&(n=e.memoizedState.cache),t.memoizedState.cache!==n&&(t.flags|=2048),ba(Ta),tu(t),null;case 25:case 30:return null}throw Error(o(156,t.tag))}function ru(e,t){switch(ta(t),t.tag){case 1:return 65536&(e=t.flags)?(t.flags=-65537&e|128,t):null;case 3:return ba(Ta),Q(),65536&(e=t.flags)&&!(128&e)?(t.flags=-65537&e|128,t):null;case 26:case 27:case 5:return G(t),null;case 13:if(ui(t),null!==(e=t.memoizedState)&&null!==e.dehydrated){if(null===t.alternate)throw Error(o(340));da()}return 65536&(e=t.flags)?(t.flags=-65537&e|128,t):null;case 19:return j(si),null;case 4:return Q(),null;case 10:return ba(t.type),null;case 22:case 23:return ui(t),gl(),null!==e&&j($a),65536&(e=t.flags)?(t.flags=-65537&e|128,t):null;case 24:return ba(Ta),null;default:return null}}function au(e,t){switch(ta(t),t.tag){case 3:ba(Ta),Q();break;case 26:case 27:case 5:G(t);break;case 4:Q();break;case 13:ui(t);break;case 19:j(si);break;case 10:ba(t.type);break;case 22:case 23:ui(t),gl(),null!==e&&j($a);break;case 24:ba(Ta)}}function lu(e,t){try{var n=t.updateQueue,r=null!==n?n.lastEffect:null;if(null!==r){var a=r.next;n=a;do{if((n.tag&e)===e){r=void 0;var l=n.create,o=n.inst;r=l(),o.destroy=r}n=n.next}while(n!==a)}}catch(e){sc(t,t.return,e)}}function ou(e,t,n){try{var r=t.updateQueue,a=null!==r?r.lastEffect:null;if(null!==a){var l=a.next;r=l;do{if((r.tag&e)===e){var o=r.inst,i=o.destroy;if(void 0!==i){o.destroy=void 0,a=t;var u=n,s=i;try{s()}catch(e){sc(a,u,e)}}}r=r.next}while(r!==l)}}catch(e){sc(t,t.return,e)}}function iu(e){var t=e.updateQueue;if(null!==t){var n=e.stateNode;try{fl(t,n)}catch(t){sc(e,e.return,t)}}}function uu(e,t,n){n.props=mi(e.type,e.memoizedProps),n.state=e.memoizedState;try{n.componentWillUnmount()}catch(n){sc(e,t,n)}}function su(e,t){try{var n=e.ref;if(null!==n){switch(e.tag){case 26:case 27:case 5:var r=e.stateNode;break;default:r=e.stateNode}"function"==typeof n?e.refCleanup=n(r):n.current=r}}catch(n){sc(e,t,n)}}function cu(e,t){var n=e.ref,r=e.refCleanup;if(null!==n)if("function"==typeof r)try{r()}catch(n){sc(e,t,n)}finally{e.refCleanup=null,null!=(e=e.alternate)&&(e.refCleanup=null)}else if("function"==typeof n)try{n(null)}catch(n){sc(e,t,n)}else n.current=null}function fu(e){var t=e.type,n=e.memoizedProps,r=e.stateNode;try{e:switch(t){case"button":case"input":case"select":case"textarea":n.autoFocus&&r.focus();break e;case"img":n.src?r.src=n.src:n.srcSet&&(r.srcset=n.srcSet)}}catch(t){sc(e,e.return,t)}}function du(e,t,n){try{var r=e.stateNode;!function(e,t,n,r){switch(t){case"div":case"span":case"svg":case"path":case"a":case"g":case"p":case"li":break;case"input":var a=null,l=null,i=null,u=null,s=null,c=null,f=null;for(h in n){var d=n[h];if(n.hasOwnProperty(h)&&null!=d)switch(h){case"checked":case"value":break;case"defaultValue":s=d;default:r.hasOwnProperty(h)||Xc(e,t,h,null,r,d)}}for(var p in r){var h=r[p];if(d=n[p],r.hasOwnProperty(p)&&(null!=h||null!=d))switch(p){case"type":l=h;break;case"name":a=h;break;case"checked":c=h;break;case"defaultChecked":f=h;break;case"value":i=h;break;case"defaultValue":u=h;break;case"children":case"dangerouslySetInnerHTML":if(null!=h)throw Error(o(137,t));break;default:h!==d&&Xc(e,t,p,h,r,d)}}return void mt(e,i,u,s,c,f,l,a);case"select":for(l in h=i=u=p=null,n)if(s=n[l],n.hasOwnProperty(l)&&null!=s)switch(l){case"value":break;case"multiple":h=s;default:r.hasOwnProperty(l)||Xc(e,t,l,null,r,s)}for(a in r)if(l=r[a],s=n[a],r.hasOwnProperty(a)&&(null!=l||null!=s))switch(a){case"value":p=l;break;case"defaultValue":u=l;break;case"multiple":i=l;default:l!==s&&Xc(e,t,a,l,r,s)}return t=u,n=i,r=h,void(null!=p?vt(e,!!n,p,!1):!!r!=!!n&&(null!=t?vt(e,!!n,t,!0):vt(e,!!n,n?[]:"",!1)));case"textarea":for(u in h=p=null,n)if(a=n[u],n.hasOwnProperty(u)&&null!=a&&!r.hasOwnProperty(u))switch(u){case"value":case"children":break;default:Xc(e,t,u,null,r,a)}for(i in r)if(a=r[i],l=n[i],r.hasOwnProperty(i)&&(null!=a||null!=l))switch(i){case"value":p=a;break;case"defaultValue":h=a;break;case"children":break;case"dangerouslySetInnerHTML":if(null!=a)throw Error(o(91));break;default:a!==l&&Xc(e,t,i,a,r,l)}return void bt(e,p,h);case"option":for(var m in n)p=n[m],n.hasOwnProperty(m)&&null!=p&&!r.hasOwnProperty(m)&&("selected"===m?e.selected=!1:Xc(e,t,m,null,r,p));for(s in r)p=r[s],h=n[s],!r.hasOwnProperty(s)||p===h||null==p&&null==h||("selected"===s?e.selected=p&&"function"!=typeof p&&"symbol"!=typeof p:Xc(e,t,s,p,r,h));return;case"img":case"link":case"area":case"base":case"br":case"col":case"embed":case"hr":case"keygen":case"meta":case"param":case"source":case"track":case"wbr":case"menuitem":for(var g in n)p=n[g],n.hasOwnProperty(g)&&null!=p&&!r.hasOwnProperty(g)&&Xc(e,t,g,null,r,p);for(c in r)if(p=r[c],h=n[c],r.hasOwnProperty(c)&&p!==h&&(null!=p||null!=h))switch(c){case"children":case"dangerouslySetInnerHTML":if(null!=p)throw Error(o(137,t));break;default:Xc(e,t,c,p,r,h)}return;default:if(Ct(t)){for(var y in n)p=n[y],n.hasOwnProperty(y)&&void 0!==p&&!r.hasOwnProperty(y)&&Zc(e,t,y,void 0,r,p);for(f in r)p=r[f],h=n[f],!r.hasOwnProperty(f)||p===h||void 0===p&&void 0===h||Zc(e,t,f,p,r,h);return}}for(var v in n)p=n[v],n.hasOwnProperty(v)&&null!=p&&!r.hasOwnProperty(v)&&Xc(e,t,v,null,r,p);for(d in r)p=r[d],h=n[d],!r.hasOwnProperty(d)||p===h||null==p&&null==h||Xc(e,t,d,p,r,h)}(r,e.type,n,t),r[Ae]=t}catch(t){sc(e,e.return,t)}}function pu(e){return 5===e.tag||3===e.tag||26===e.tag||27===e.tag&&pf(e.type)||4===e.tag}function hu(e){e:for(;;){for(;null===e.sibling;){if(null===e.return||pu(e.return))return null;e=e.return}for(e.sibling.return=e.return,e=e.sibling;5!==e.tag&&6!==e.tag&&18!==e.tag;){if(27===e.tag&&pf(e.type))continue e;if(2&e.flags)continue e;if(null===e.child||4===e.tag)continue e;e.child.return=e,e=e.child}if(!(2&e.flags))return e.stateNode}}function mu(e,t,n){var r=e.tag;if(5===r||6===r)e=e.stateNode,t?(9===n.nodeType?n.body:"HTML"===n.nodeName?n.ownerDocument.body:n).insertBefore(e,t):((t=9===n.nodeType?n.body:"HTML"===n.nodeName?n.ownerDocument.body:n).appendChild(e),null!=(n=n._reactRootContainer)||null!==t.onclick||(t.onclick=Kc));else if(4!==r&&(27===r&&pf(e.type)&&(n=e.stateNode,t=null),null!==(e=e.child)))for(mu(e,t,n),e=e.sibling;null!==e;)mu(e,t,n),e=e.sibling}function gu(e,t,n){var r=e.tag;if(5===r||6===r)e=e.stateNode,t?n.insertBefore(e,t):n.appendChild(e);else if(4!==r&&(27===r&&pf(e.type)&&(n=e.stateNode),null!==(e=e.child)))for(gu(e,t,n),e=e.sibling;null!==e;)gu(e,t,n),e=e.sibling}function yu(e){var t=e.stateNode,n=e.memoizedProps;try{for(var r=e.type,a=t.attributes;a.length;)t.removeAttributeNode(a[0]);Jc(t,r,n),t[Te]=e,t[Ae]=n}catch(t){sc(e,e.return,t)}}var vu=!1,bu=!1,Su=!1,ku="function"==typeof WeakSet?WeakSet:Set,wu=null;function xu(e,t,n){var r=n.flags;switch(n.tag){case 0:case 11:case 15:Du(e,n),4&r&&lu(5,n);break;case 1:if(Du(e,n),4&r)if(e=n.stateNode,null===t)try{e.componentDidMount()}catch(e){sc(n,n.return,e)}else{var a=mi(n.type,t.memoizedProps);t=t.memoizedState;try{e.componentDidUpdate(a,t,e.__reactInternalSnapshotBeforeUpdate)}catch(e){sc(n,n.return,e)}}64&r&&iu(n),512&r&&su(n,n.return);break;case 3:if(Du(e,n),64&r&&null!==(e=n.updateQueue)){if(t=null,null!==n.child)switch(n.child.tag){case 27:case 5:case 1:t=n.child.stateNode}try{fl(e,t)}catch(e){sc(n,n.return,e)}}break;case 27:null===t&&4&r&&yu(n);case 26:case 5:Du(e,n),null===t&&4&r&&fu(n),512&r&&su(n,n.return);break;case 12:Du(e,n);break;case 13:Du(e,n),4&r&&Nu(e,n),64&r&&null!==(e=n.memoizedState)&&null!==(e=e.dehydrated)&&function(e,t){var n=e.ownerDocument;if("$?"!==e.data||"complete"===n.readyState)t();else{var r=function(){t(),n.removeEventListener("DOMContentLoaded",r)};n.addEventListener("DOMContentLoaded",r),e._reactRetry=r}}(e,n=pc.bind(null,n));break;case 22:if(!(r=null!==n.memoizedState||vu)){t=null!==t&&null!==t.memoizedState||bu,a=vu;var l=bu;vu=r,(bu=t)&&!l?Fu(e,n,!!(8772&n.subtreeFlags)):Du(e,n),vu=a,bu=l}break;case 30:break;default:Du(e,n)}}function Eu(e){var t=e.alternate;null!==t&&(e.alternate=null,Eu(t)),e.child=null,e.deletions=null,e.sibling=null,5===e.tag&&null!==(t=e.stateNode)&&je(t),e.stateNode=null,e.return=null,e.dependencies=null,e.memoizedProps=null,e.memoizedState=null,e.pendingProps=null,e.stateNode=null,e.updateQueue=null}var Cu=null,zu=!1;function Pu(e,t,n){for(n=n.child;null!==n;)_u(e,t,n),n=n.sibling}function _u(e,t,n){if(ce&&"function"==typeof ce.onCommitFiberUnmount)try{ce.onCommitFiberUnmount(se,n)}catch(e){}switch(n.tag){case 26:bu||cu(n,t),Pu(e,t,n),n.memoizedState?n.memoizedState.count--:n.stateNode&&(n=n.stateNode).parentNode.removeChild(n);break;case 27:bu||cu(n,t);var r=Cu,a=zu;pf(n.type)&&(Cu=n.stateNode,zu=!1),Pu(e,t,n),kf(n.stateNode),Cu=r,zu=a;break;case 5:bu||cu(n,t);case 6:if(r=Cu,a=zu,Cu=null,Pu(e,t,n),zu=a,null!==(Cu=r))if(zu)try{(9===Cu.nodeType?Cu.body:"HTML"===Cu.nodeName?Cu.ownerDocument.body:Cu).removeChild(n.stateNode)}catch(e){sc(n,t,e)}else try{Cu.removeChild(n.stateNode)}catch(e){sc(n,t,e)}break;case 18:null!==Cu&&(zu?(hf(9===(e=Cu).nodeType?e.body:"HTML"===e.nodeName?e.ownerDocument.body:e,n.stateNode),Cd(e)):hf(Cu,n.stateNode));break;case 4:r=Cu,a=zu,Cu=n.stateNode.containerInfo,zu=!0,Pu(e,t,n),Cu=r,zu=a;break;case 0:case 11:case 14:case 15:bu||ou(2,n,t),bu||ou(4,n,t),Pu(e,t,n);break;case 1:bu||(cu(n,t),"function"==typeof(r=n.stateNode).componentWillUnmount&&uu(n,t,r)),Pu(e,t,n);break;case 21:Pu(e,t,n);break;case 22:bu=(r=bu)||null!==n.memoizedState,Pu(e,t,n),bu=r;break;default:Pu(e,t,n)}}function Nu(e,t){if(null===t.memoizedState&&null!==(e=t.alternate)&&null!==(e=e.memoizedState)&&null!==(e=e.dehydrated))try{Cd(e)}catch(e){sc(t,t.return,e)}}function Lu(e,t){var n=function(e){switch(e.tag){case 13:case 19:var t=e.stateNode;return null===t&&(t=e.stateNode=new ku),t;case 22:return null===(t=(e=e.stateNode)._retryCache)&&(t=e._retryCache=new ku),t;default:throw Error(o(435,e.tag))}}(e);t.forEach(function(t){var r=hc.bind(null,e,t);n.has(t)||(n.add(t),t.then(r,r))})}function Tu(e,t){var n=t.deletions;if(null!==n)for(var r=0;r<n.length;r++){var a=n[r],l=e,i=t,u=i;e:for(;null!==u;){switch(u.tag){case 27:if(pf(u.type)){Cu=u.stateNode,zu=!1;break e}break;case 5:Cu=u.stateNode,zu=!1;break e;case 3:case 4:Cu=u.stateNode.containerInfo,zu=!0;break e}u=u.return}if(null===Cu)throw Error(o(160));_u(l,i,a),Cu=null,zu=!1,null!==(l=a.alternate)&&(l.return=null),a.return=null}if(13878&t.subtreeFlags)for(t=t.child;null!==t;)Ru(t,e),t=t.sibling}var Au=null;function Ru(e,t){var n=e.alternate,r=e.flags;switch(e.tag){case 0:case 11:case 14:case 15:Tu(t,e),Ou(e),4&r&&(ou(3,e,e.return),lu(3,e),ou(5,e,e.return));break;case 1:Tu(t,e),Ou(e),512&r&&(bu||null===n||cu(n,n.return)),64&r&&vu&&null!==(e=e.updateQueue)&&null!==(r=e.callbacks)&&(n=e.shared.hiddenCallbacks,e.shared.hiddenCallbacks=null===n?r:n.concat(r));break;case 26:var a=Au;if(Tu(t,e),Ou(e),512&r&&(bu||null===n||cu(n,n.return)),4&r){var l=null!==n?n.memoizedState:null;if(r=e.memoizedState,null===n)if(null===r)if(null===e.stateNode){e:{r=e.type,n=e.memoizedProps,a=a.ownerDocument||a;t:switch(r){case"title":(!(l=a.getElementsByTagName("title")[0])||l[Fe]||l[Te]||"http://www.w3.org/2000/svg"===l.namespaceURI||l.hasAttribute("itemprop"))&&(l=a.createElement(r),a.head.insertBefore(l,a.querySelector("head > title"))),Jc(l,r,n),l[Te]=e,Ve(l),r=l;break e;case"link":var i=jf("link","href",a).get(r+(n.href||""));if(i)for(var u=0;u<i.length;u++)if((l=i[u]).getAttribute("href")===(null==n.href||""===n.href?null:n.href)&&l.getAttribute("rel")===(null==n.rel?null:n.rel)&&l.getAttribute("title")===(null==n.title?null:n.title)&&l.getAttribute("crossorigin")===(null==n.crossOrigin?null:n.crossOrigin)){i.splice(u,1);break t}Jc(l=a.createElement(r),r,n),a.head.appendChild(l);break;case"meta":if(i=jf("meta","content",a).get(r+(n.content||"")))for(u=0;u<i.length;u++)if((l=i[u]).getAttribute("content")===(null==n.content?null:""+n.content)&&l.getAttribute("name")===(null==n.name?null:n.name)&&l.getAttribute("property")===(null==n.property?null:n.property)&&l.getAttribute("http-equiv")===(null==n.httpEquiv?null:n.httpEquiv)&&l.getAttribute("charset")===(null==n.charSet?null:n.charSet)){i.splice(u,1);break t}Jc(l=a.createElement(r),r,n),a.head.appendChild(l);break;default:throw Error(o(468,r))}l[Te]=e,Ve(l),r=l}e.stateNode=r}else $f(a,e.type,e.stateNode);else e.stateNode=Of(a,r,e.memoizedProps);else l!==r?(null===l?null!==n.stateNode&&(n=n.stateNode).parentNode.removeChild(n):l.count--,null===r?$f(a,e.type,e.stateNode):Of(a,r,e.memoizedProps)):null===r&&null!==e.stateNode&&du(e,e.memoizedProps,n.memoizedProps)}break;case 27:Tu(t,e),Ou(e),512&r&&(bu||null===n||cu(n,n.return)),null!==n&&4&r&&du(e,e.memoizedProps,n.memoizedProps);break;case 5:if(Tu(t,e),Ou(e),512&r&&(bu||null===n||cu(n,n.return)),32&e.flags){a=e.stateNode;try{kt(a,"")}catch(t){sc(e,e.return,t)}}4&r&&null!=e.stateNode&&du(e,a=e.memoizedProps,null!==n?n.memoizedProps:a),1024&r&&(Su=!0);break;case 6:if(Tu(t,e),Ou(e),4&r){if(null===e.stateNode)throw Error(o(162));r=e.memoizedProps,n=e.stateNode;try{n.nodeValue=r}catch(t){sc(e,e.return,t)}}break;case 3:if(Ff=null,a=Au,Au=Ef(t.containerInfo),Tu(t,e),Au=a,Ou(e),4&r&&null!==n&&n.memoizedState.isDehydrated)try{Cd(t.containerInfo)}catch(t){sc(e,e.return,t)}Su&&(Su=!1,Iu(e));break;case 4:r=Au,Au=Ef(e.stateNode.containerInfo),Tu(t,e),Ou(e),Au=r;break;case 12:default:Tu(t,e),Ou(e);break;case 13:Tu(t,e),Ou(e),8192&e.child.flags&&null!==e.memoizedState!=(null!==n&&null!==n.memoizedState)&&(Ss=ee()),4&r&&null!==(r=e.updateQueue)&&(e.updateQueue=null,Lu(e,r));break;case 22:a=null!==e.memoizedState;var s=null!==n&&null!==n.memoizedState,c=vu,f=bu;if(vu=c||a,bu=f||s,Tu(t,e),bu=f,vu=c,Ou(e),8192&r)e:for(t=e.stateNode,t._visibility=a?-2&t._visibility:1|t._visibility,a&&(null===n||s||vu||bu||Mu(e)),n=null,t=e;;){if(5===t.tag||26===t.tag){if(null===n){s=n=t;try{if(l=s.stateNode,a)"function"==typeof(i=l.style).setProperty?i.setProperty("display","none","important"):i.display="none";else{u=s.stateNode;var d=s.memoizedProps.style,p=null!=d&&d.hasOwnProperty("display")?d.display:null;u.style.display=null==p||"boolean"==typeof p?"":(""+p).trim()}}catch(e){sc(s,s.return,e)}}}else if(6===t.tag){if(null===n){s=t;try{s.stateNode.nodeValue=a?"":s.memoizedProps}catch(e){sc(s,s.return,e)}}}else if((22!==t.tag&&23!==t.tag||null===t.memoizedState||t===e)&&null!==t.child){t.child.return=t,t=t.child;continue}if(t===e)break e;for(;null===t.sibling;){if(null===t.return||t.return===e)break e;n===t&&(n=null),t=t.return}n===t&&(n=null),t.sibling.return=t.return,t=t.sibling}4&r&&null!==(r=e.updateQueue)&&null!==(n=r.retryQueue)&&(r.retryQueue=null,Lu(e,n));break;case 19:Tu(t,e),Ou(e),4&r&&null!==(r=e.updateQueue)&&(e.updateQueue=null,Lu(e,r));case 30:case 21:}}function Ou(e){var t=e.flags;if(2&t){try{for(var n,r=e.return;null!==r;){if(pu(r)){n=r;break}r=r.return}if(null==n)throw Error(o(160));switch(n.tag){case 27:var a=n.stateNode;gu(e,hu(e),a);break;case 5:var l=n.stateNode;32&n.flags&&(kt(l,""),n.flags&=-33),gu(e,hu(e),l);break;case 3:case 4:var i=n.stateNode.containerInfo;mu(e,hu(e),i);break;default:throw Error(o(161))}}catch(t){sc(e,e.return,t)}e.flags&=-3}4096&t&&(e.flags&=-4097)}function Iu(e){if(1024&e.subtreeFlags)for(e=e.child;null!==e;){var t=e;Iu(t),5===t.tag&&1024&t.flags&&t.stateNode.reset(),e=e.sibling}}function Du(e,t){if(8772&t.subtreeFlags)for(t=t.child;null!==t;)xu(e,t.alternate,t),t=t.sibling}function Mu(e){for(e=e.child;null!==e;){var t=e;switch(t.tag){case 0:case 11:case 14:case 15:ou(4,t,t.return),Mu(t);break;case 1:cu(t,t.return);var n=t.stateNode;"function"==typeof n.componentWillUnmount&&uu(t,t.return,n),Mu(t);break;case 27:kf(t.stateNode);case 26:case 5:cu(t,t.return),Mu(t);break;case 22:null===t.memoizedState&&Mu(t);break;default:Mu(t)}e=e.sibling}}function Fu(e,t,n){for(n=n&&!!(8772&t.subtreeFlags),t=t.child;null!==t;){var r=t.alternate,a=e,l=t,o=l.flags;switch(l.tag){case 0:case 11:case 15:Fu(a,l,n),lu(4,l);break;case 1:if(Fu(a,l,n),"function"==typeof(a=(r=l).stateNode).componentDidMount)try{a.componentDidMount()}catch(e){sc(r,r.return,e)}if(null!==(a=(r=l).updateQueue)){var i=r.stateNode;try{var u=a.shared.hiddenCallbacks;if(null!==u)for(a.shared.hiddenCallbacks=null,a=0;a<u.length;a++)cl(u[a],i)}catch(e){sc(r,r.return,e)}}n&&64&o&&iu(l),su(l,l.return);break;case 27:yu(l);case 26:case 5:Fu(a,l,n),n&&null===r&&4&o&&fu(l),su(l,l.return);break;case 12:Fu(a,l,n);break;case 13:Fu(a,l,n),n&&4&o&&Nu(a,l);break;case 22:null===l.memoizedState&&Fu(a,l,n),su(l,l.return);break;case 30:break;default:Fu(a,l,n)}t=t.sibling}}function ju(e,t){var n=null;null!==e&&null!==e.memoizedState&&null!==e.memoizedState.cachePool&&(n=e.memoizedState.cachePool.pool),e=null,null!==t.memoizedState&&null!==t.memoizedState.cachePool&&(e=t.memoizedState.cachePool.pool),e!==n&&(null!=e&&e.refCount++,null!=n&&Ra(n))}function $u(e,t){e=null,null!==t.alternate&&(e=t.alternate.memoizedState.cache),(t=t.memoizedState.cache)!==e&&(t.refCount++,null!=e&&Ra(e))}function Uu(e,t,n,r){if(10256&t.subtreeFlags)for(t=t.child;null!==t;)Hu(e,t,n,r),t=t.sibling}function Hu(e,t,n,r){var a=t.flags;switch(t.tag){case 0:case 11:case 15:Uu(e,t,n,r),2048&a&&lu(9,t);break;case 1:case 13:default:Uu(e,t,n,r);break;case 3:Uu(e,t,n,r),2048&a&&(e=null,null!==t.alternate&&(e=t.alternate.memoizedState.cache),(t=t.memoizedState.cache)!==e&&(t.refCount++,null!=e&&Ra(e)));break;case 12:if(2048&a){Uu(e,t,n,r),e=t.stateNode;try{var l=t.memoizedProps,o=l.id,i=l.onPostCommit;"function"==typeof i&&i(o,null===t.alternate?"mount":"update",e.passiveEffectDuration,-0)}catch(e){sc(t,t.return,e)}}else Uu(e,t,n,r);break;case 23:break;case 22:l=t.stateNode,o=t.alternate,null!==t.memoizedState?2&l._visibility?Uu(e,t,n,r):Vu(e,t):2&l._visibility?Uu(e,t,n,r):(l._visibility|=2,Bu(e,t,n,r,!!(10256&t.subtreeFlags))),2048&a&&ju(o,t);break;case 24:Uu(e,t,n,r),2048&a&&$u(t.alternate,t)}}function Bu(e,t,n,r,a){for(a=a&&!!(10256&t.subtreeFlags),t=t.child;null!==t;){var l=e,o=t,i=n,u=r,s=o.flags;switch(o.tag){case 0:case 11:case 15:Bu(l,o,i,u,a),lu(8,o);break;case 23:break;case 22:var c=o.stateNode;null!==o.memoizedState?2&c._visibility?Bu(l,o,i,u,a):Vu(l,o):(c._visibility|=2,Bu(l,o,i,u,a)),a&&2048&s&&ju(o.alternate,o);break;case 24:Bu(l,o,i,u,a),a&&2048&s&&$u(o.alternate,o);break;default:Bu(l,o,i,u,a)}t=t.sibling}}function Vu(e,t){if(10256&t.subtreeFlags)for(t=t.child;null!==t;){var n=e,r=t,a=r.flags;switch(r.tag){case 22:Vu(n,r),2048&a&&ju(r.alternate,r);break;case 24:Vu(n,r),2048&a&&$u(r.alternate,r);break;default:Vu(n,r)}t=t.sibling}}var Wu=8192;function Qu(e){if(e.subtreeFlags&Wu)for(e=e.child;null!==e;)qu(e),e=e.sibling}function qu(e){switch(e.tag){case 26:Qu(e),e.flags&Wu&&null!==e.memoizedState&&function(e,t,n){if(null===Hf)throw Error(o(475));var r=Hf;if(!("stylesheet"!==t.type||"string"==typeof n.media&&!1===matchMedia(n.media).matches||4&t.state.loading)){if(null===t.instance){var a=Nf(n.href),l=e.querySelector(Lf(a));if(l)return null!==(e=l._p)&&"object"==typeof e&&"function"==typeof e.then&&(r.count++,r=Vf.bind(r),e.then(r,r)),t.state.loading|=4,t.instance=l,void Ve(l);l=e.ownerDocument||e,n=Tf(n),(a=wf.get(a))&&Df(n,a),Ve(l=l.createElement("link"));var i=l;i._p=new Promise(function(e,t){i.onload=e,i.onerror=t}),Jc(l,"link",n),t.instance=l}null===r.stylesheets&&(r.stylesheets=new Map),r.stylesheets.set(t,e),(e=t.state.preload)&&!(3&t.state.loading)&&(r.count++,t=Vf.bind(r),e.addEventListener("load",t),e.addEventListener("error",t))}}(Au,e.memoizedState,e.memoizedProps);break;case 5:default:Qu(e);break;case 3:case 4:var t=Au;Au=Ef(e.stateNode.containerInfo),Qu(e),Au=t;break;case 22:null===e.memoizedState&&(null!==(t=e.alternate)&&null!==t.memoizedState?(t=Wu,Wu=16777216,Qu(e),Wu=t):Qu(e))}}function Gu(e){var t=e.alternate;if(null!==t&&null!==(e=t.child)){t.child=null;do{t=e.sibling,e.sibling=null,e=t}while(null!==e)}}function Yu(e){var t=e.deletions;if(16&e.flags){if(null!==t)for(var n=0;n<t.length;n++){var r=t[n];wu=r,Zu(r,e)}Gu(e)}if(10256&e.subtreeFlags)for(e=e.child;null!==e;)Ku(e),e=e.sibling}function Ku(e){switch(e.tag){case 0:case 11:case 15:Yu(e),2048&e.flags&&ou(9,e,e.return);break;case 3:case 12:default:Yu(e);break;case 22:var t=e.stateNode;null!==e.memoizedState&&2&t._visibility&&(null===e.return||13!==e.return.tag)?(t._visibility&=-3,Xu(e)):Yu(e)}}function Xu(e){var t=e.deletions;if(16&e.flags){if(null!==t)for(var n=0;n<t.length;n++){var r=t[n];wu=r,Zu(r,e)}Gu(e)}for(e=e.child;null!==e;){switch((t=e).tag){case 0:case 11:case 15:ou(8,t,t.return),Xu(t);break;case 22:2&(n=t.stateNode)._visibility&&(n._visibility&=-3,Xu(t));break;default:Xu(t)}e=e.sibling}}function Zu(e,t){for(;null!==wu;){var n=wu;switch(n.tag){case 0:case 11:case 15:ou(8,n,t);break;case 23:case 22:if(null!==n.memoizedState&&null!==n.memoizedState.cachePool){var r=n.memoizedState.cachePool.pool;null!=r&&r.refCount++}break;case 24:Ra(n.memoizedState.cache)}if(null!==(r=n.child))r.return=n,wu=r;else e:for(n=e;null!==wu;){var a=(r=wu).sibling,l=r.return;if(Eu(r),r===n){wu=null;break e}if(null!==a){a.return=l,wu=a;break e}wu=l}}}var Ju={getCacheForType:function(e){var t=Ca(Ta),n=t.data.get(e);return void 0===n&&(n=e(),t.data.set(e,n)),n}},es="function"==typeof WeakMap?WeakMap:Map,ts=0,ns=null,rs=null,as=0,ls=0,os=null,is=!1,us=!1,ss=!1,cs=0,fs=0,ds=0,ps=0,hs=0,ms=0,gs=0,ys=null,vs=null,bs=!1,Ss=0,ks=1/0,ws=null,xs=null,Es=0,Cs=null,zs=null,Ps=0,_s=0,Ns=null,Ls=null,Ts=0,As=null;function Rs(){return 2&ts&&0!==as?as&-as:null!==R.T?0!==Da?Da:_c():Ne()}function Os(){0===ms&&(ms=536870912&as&&!aa?536870912:ke());var e=ri.current;return null!==e&&(e.flags|=32),ms}function Is(e,t,n){(e!==ns||2!==ls&&9!==ls)&&null===e.cancelPendingCommit||(Hs(e,0),js(e,as,ms,!1)),Ee(e,n),2&ts&&e===ns||(e===ns&&(!(2&ts)&&(ps|=n),4===fs&&js(e,as,ms,!1)),kc(e))}function Ds(e,t,n){if(6&ts)throw Error(o(327));for(var r=!n&&!(124&t)&&0===(t&e.expiredLanes)||be(e,t),a=r?function(e,t){var n=ts;ts|=2;var r=Vs(),a=Ws();ns!==e||as!==t?(ws=null,ks=ee()+500,Hs(e,t)):us=be(e,t);e:for(;;)try{if(0!==ls&&null!==rs){t=rs;var l=os;t:switch(ls){case 1:ls=0,os=null,Zs(e,t,l,1);break;case 2:case 9:if(Ga(l)){ls=0,os=null,Xs(t);break}t=function(){2!==ls&&9!==ls||ns!==e||(ls=7),kc(e)},l.then(t,t);break e;case 3:ls=7;break e;case 4:ls=5;break e;case 7:Ga(l)?(ls=0,os=null,Xs(t)):(ls=0,os=null,Zs(e,t,l,7));break;case 5:var i=null;switch(rs.tag){case 26:i=rs.memoizedState;case 5:case 27:var u=rs;if(!i||Uf(i)){ls=0,os=null;var s=u.sibling;if(null!==s)rs=s;else{var c=u.return;null!==c?(rs=c,Js(c)):rs=null}break t}}ls=0,os=null,Zs(e,t,l,5);break;case 6:ls=0,os=null,Zs(e,t,l,6);break;case 8:Us(),fs=6;break e;default:throw Error(o(462))}}Ys();break}catch(t){Bs(e,t)}return ya=ga=null,R.H=r,R.A=a,ts=n,null!==rs?0:(ns=null,as=0,Pr(),fs)}(e,t):qs(e,t,!0),l=r;;){if(0===a){us&&!r&&js(e,t,0,!1);break}if(n=e.current.alternate,!l||Fs(n)){if(2===a){if(l=t,e.errorRecoveryDisabledLanes&l)var i=0;else i=0!=(i=-536870913&e.pendingLanes)?i:536870912&i?536870912:0;if(0!==i){t=i;e:{var u=e;a=ys;var s=u.current.memoizedState.isDehydrated;if(s&&(Hs(u,i).flags|=256),2!==(i=qs(u,i,!1))){if(ss&&!s){u.errorRecoveryDisabledLanes|=l,ps|=l,a=4;break e}l=vs,vs=a,null!==l&&(null===vs?vs=l:vs.push.apply(vs,l))}a=i}if(l=!1,2!==a)continue}}if(1===a){Hs(e,0),js(e,t,0,!0);break}e:{switch(r=e,l=a){case 0:case 1:throw Error(o(345));case 4:if((4194048&t)!==t)break;case 6:js(r,t,ms,!is);break e;case 2:vs=null;break;case 3:case 5:break;default:throw Error(o(329))}if((62914560&t)===t&&10<(a=Ss+300-ee())){if(js(r,t,ms,!is),0!==ve(r,0,!0))break e;r.timeoutHandle=uf(Ms.bind(null,r,n,vs,ws,bs,t,ms,ps,gs,is,l,2,-0,0),a)}else Ms(r,n,vs,ws,bs,t,ms,ps,gs,is,l,0,-0,0)}break}a=qs(e,t,!1),l=!1}kc(e)}function Ms(e,t,n,r,a,l,i,u,s,c,f,d,p,h){if(e.timeoutHandle=-1,(8192&(d=t.subtreeFlags)||!(16785408&~d))&&(Hf={stylesheets:null,count:0,unsuspend:Bf},qu(t),null!==(d=function(){if(null===Hf)throw Error(o(475));var e=Hf;return e.stylesheets&&0===e.count&&Qf(e,e.stylesheets),0<e.count?function(t){var n=setTimeout(function(){if(e.stylesheets&&Qf(e,e.stylesheets),e.unsuspend){var t=e.unsuspend;e.unsuspend=null,t()}},6e4);return e.unsuspend=t,function(){e.unsuspend=null,clearTimeout(n)}}:null}())))return e.cancelPendingCommit=d(tc.bind(null,e,t,l,n,r,a,i,u,s,f,1,p,h)),void js(e,l,i,!c);tc(e,t,l,n,r,a,i,u,s)}function Fs(e){for(var t=e;;){var n=t.tag;if((0===n||11===n||15===n)&&16384&t.flags&&null!==(n=t.updateQueue)&&null!==(n=n.stores))for(var r=0;r<n.length;r++){var a=n[r],l=a.getSnapshot;a=a.value;try{if(!Gn(l(),a))return!1}catch(e){return!1}}if(n=t.child,16384&t.subtreeFlags&&null!==n)n.return=t,t=n;else{if(t===e)break;for(;null===t.sibling;){if(null===t.return||t.return===e)return!0;t=t.return}t.sibling.return=t.return,t=t.sibling}}return!0}function js(e,t,n,r){t&=~hs,t&=~ps,e.suspendedLanes|=t,e.pingedLanes&=~t,r&&(e.warmLanes|=t),r=e.expirationTimes;for(var a=t;0<a;){var l=31-de(a),o=1<<l;r[l]=-1,a&=~o}0!==n&&Ce(e,n,t)}function $s(){return!!(6&ts)||(wc(0,!1),!1)}function Us(){if(null!==rs){if(0===ls)var e=rs.return;else ya=ga=null,Dl(e=rs),Go=null,Yo=0,e=rs;for(;null!==e;)au(e.alternate,e),e=e.return;rs=null}}function Hs(e,t){var n=e.timeoutHandle;-1!==n&&(e.timeoutHandle=-1,sf(n)),null!==(n=e.cancelPendingCommit)&&(e.cancelPendingCommit=null,n()),Us(),ns=e,rs=n=Mr(e.current,null),as=t,ls=0,os=null,is=!1,us=be(e,t),ss=!1,gs=ms=hs=ps=ds=fs=0,vs=ys=null,bs=!1,8&t&&(t|=32&t);var r=e.entangledLanes;if(0!==r)for(e=e.entanglements,r&=t;0<r;){var a=31-de(r),l=1<<a;t|=e[a],r&=~l}return cs=t,Pr(),n}function Bs(e,t){vl=null,R.H=Vo,t===Va||t===Qa?(t=Za(),ls=3):t===Wa?(t=Za(),ls=4):ls=t===Ci?8:null!==t&&"object"==typeof t&&"function"==typeof t.then?6:1,os=t,null===rs&&(fs=1,Si(e,xr(t,e.current)))}function Vs(){var e=R.H;return R.H=Vo,null===e?Vo:e}function Ws(){var e=R.A;return R.A=Ju,e}function Qs(){fs=4,is||(4194048&as)!==as&&null!==ri.current||(us=!0),!(134217727&ds)&&!(134217727&ps)||null===ns||js(ns,as,ms,!1)}function qs(e,t,n){var r=ts;ts|=2;var a=Vs(),l=Ws();ns===e&&as===t||(ws=null,Hs(e,t)),t=!1;var o=fs;e:for(;;)try{if(0!==ls&&null!==rs){var i=rs,u=os;switch(ls){case 8:Us(),o=6;break e;case 3:case 2:case 9:case 6:null===ri.current&&(t=!0);var s=ls;if(ls=0,os=null,Zs(e,i,u,s),n&&us){o=0;break e}break;default:s=ls,ls=0,os=null,Zs(e,i,u,s)}}Gs(),o=fs;break}catch(t){Bs(e,t)}return t&&e.shellSuspendCounter++,ya=ga=null,ts=r,R.H=a,R.A=l,null===rs&&(ns=null,as=0,Pr()),o}function Gs(){for(;null!==rs;)Ks(rs)}function Ys(){for(;null!==rs&&!Z();)Ks(rs)}function Ks(e){var t=Ki(e.alternate,e,cs);e.memoizedProps=e.pendingProps,null===t?Js(e):rs=t}function Xs(e){var t=e,n=t.alternate;switch(t.tag){case 15:case 0:t=Ii(n,t,t.pendingProps,t.type,void 0,as);break;case 11:t=Ii(n,t,t.pendingProps,t.type.render,t.ref,as);break;case 5:Dl(t);default:au(n,t),t=Ki(n,t=rs=Fr(t,cs),cs)}e.memoizedProps=e.pendingProps,null===t?Js(e):rs=t}function Zs(e,t,n,r){ya=ga=null,Dl(t),Go=null,Yo=0;var a=t.return;try{if(function(e,t,n,r,a){if(n.flags|=32768,null!==r&&"object"==typeof r&&"function"==typeof r.then){if(null!==(t=n.alternate)&&wa(t,n,a,!0),null!==(n=ri.current)){switch(n.tag){case 13:return null===ai?Qs():null===n.alternate&&0===fs&&(fs=3),n.flags&=-257,n.flags|=65536,n.lanes=a,r===qa?n.flags|=16384:(null===(t=n.updateQueue)?n.updateQueue=new Set([r]):t.add(r),cc(e,r,a)),!1;case 22:return n.flags|=65536,r===qa?n.flags|=16384:(null===(t=n.updateQueue)?(t={transitions:null,markerInstances:null,retryQueue:new Set([r])},n.updateQueue=t):null===(n=t.retryQueue)?t.retryQueue=new Set([r]):n.add(r),cc(e,r,a)),!1}throw Error(o(435,n.tag))}return cc(e,r,a),Qs(),!1}if(aa)return null!==(t=ri.current)?(!(65536&t.flags)&&(t.flags|=256),t.flags|=65536,t.lanes=a,r!==ia&&ha(xr(e=Error(o(422),{cause:r}),n))):(r!==ia&&ha(xr(t=Error(o(423),{cause:r}),n)),(e=e.current.alternate).flags|=65536,a&=-a,e.lanes|=a,r=xr(r,n),ol(e,a=wi(e.stateNode,r,a)),4!==fs&&(fs=2)),!1;var l=Error(o(520),{cause:r});if(l=xr(l,n),null===ys?ys=[l]:ys.push(l),4!==fs&&(fs=2),null===t)return!0;r=xr(r,n),n=t;do{switch(n.tag){case 3:return n.flags|=65536,e=a&-a,n.lanes|=e,ol(n,e=wi(n.stateNode,r,e)),!1;case 1:if(t=n.type,l=n.stateNode,!(128&n.flags||"function"!=typeof t.getDerivedStateFromError&&(null===l||"function"!=typeof l.componentDidCatch||null!==xs&&xs.has(l))))return n.flags|=65536,a&=-a,n.lanes|=a,Ei(a=xi(a),e,n,r),ol(n,a),!1}n=n.return}while(null!==n);return!1}(e,a,t,n,as))return fs=1,Si(e,xr(n,e.current)),void(rs=null)}catch(t){if(null!==a)throw rs=a,t;return fs=1,Si(e,xr(n,e.current)),void(rs=null)}32768&t.flags?(aa||1===r?e=!0:us||536870912&as?e=!1:(is=e=!0,(2===r||9===r||3===r||6===r)&&null!==(r=ri.current)&&13===r.tag&&(r.flags|=16384)),ec(t,e)):Js(t)}function Js(e){var t=e;do{if(32768&t.flags)return void ec(t,is);e=t.return;var n=nu(t.alternate,t,cs);if(null!==n)return void(rs=n);if(null!==(t=t.sibling))return void(rs=t);rs=t=e}while(null!==t);0===fs&&(fs=5)}function ec(e,t){do{var n=ru(e.alternate,e);if(null!==n)return n.flags&=32767,void(rs=n);if(null!==(n=e.return)&&(n.flags|=32768,n.subtreeFlags=0,n.deletions=null),!t&&null!==(e=e.sibling))return void(rs=e);rs=e=n}while(null!==e);fs=6,rs=null}function tc(e,t,n,r,a,l,i,u,s){e.cancelPendingCommit=null;do{oc()}while(0!==Es);if(6&ts)throw Error(o(327));if(null!==t){if(t===e.current)throw Error(o(177));if(l=t.lanes|t.childLanes,function(e,t,n,r,a,l){var o=e.pendingLanes;e.pendingLanes=n,e.suspendedLanes=0,e.pingedLanes=0,e.warmLanes=0,e.expiredLanes&=n,e.entangledLanes&=n,e.errorRecoveryDisabledLanes&=n,e.shellSuspendCounter=0;var i=e.entanglements,u=e.expirationTimes,s=e.hiddenUpdates;for(n=o&~n;0<n;){var c=31-de(n),f=1<<c;i[c]=0,u[c]=-1;var d=s[c];if(null!==d)for(s[c]=null,c=0;c<d.length;c++){var p=d[c];null!==p&&(p.lane&=-536870913)}n&=~f}0!==r&&Ce(e,r,0),0!==l&&0===a&&0!==e.tag&&(e.suspendedLanes|=l&~(o&~t))}(e,n,l|=zr,i,u,s),e===ns&&(rs=ns=null,as=0),zs=t,Cs=e,Ps=n,_s=l,Ns=a,Ls=r,10256&t.subtreeFlags||10256&t.flags?(e.callbackNode=null,e.callbackPriority=0,K(ae,function(){return ic(),null})):(e.callbackNode=null,e.callbackPriority=0),r=!!(13878&t.flags),13878&t.subtreeFlags||r){r=R.T,R.T=null,a=O.p,O.p=2,i=ts,ts|=4;try{!function(e,t){if(e=e.containerInfo,ef=ed,er(e=Jn(e))){if("selectionStart"in e)var n={start:e.selectionStart,end:e.selectionEnd};else e:{var r=(n=(n=e.ownerDocument)&&n.defaultView||window).getSelection&&n.getSelection();if(r&&0!==r.rangeCount){n=r.anchorNode;var a=r.anchorOffset,l=r.focusNode;r=r.focusOffset;try{n.nodeType,l.nodeType}catch(e){n=null;break e}var i=0,u=-1,s=-1,c=0,f=0,d=e,p=null;t:for(;;){for(var h;d!==n||0!==a&&3!==d.nodeType||(u=i+a),d!==l||0!==r&&3!==d.nodeType||(s=i+r),3===d.nodeType&&(i+=d.nodeValue.length),null!==(h=d.firstChild);)p=d,d=h;for(;;){if(d===e)break t;if(p===n&&++c===a&&(u=i),p===l&&++f===r&&(s=i),null!==(h=d.nextSibling))break;p=(d=p).parentNode}d=h}n=-1===u||-1===s?null:{start:u,end:s}}else n=null}n=n||{start:0,end:0}}else n=null;for(tf={focusedElem:e,selectionRange:n},ed=!1,wu=t;null!==wu;)if(e=(t=wu).child,1024&t.subtreeFlags&&null!==e)e.return=t,wu=e;else for(;null!==wu;){switch(l=(t=wu).alternate,e=t.flags,t.tag){case 0:case 11:case 15:case 5:case 26:case 27:case 6:case 4:case 17:break;case 1:if(1024&e&&null!==l){e=void 0,n=t,a=l.memoizedProps,l=l.memoizedState,r=n.stateNode;try{var m=mi(n.type,a,(n.elementType,n.type));e=r.getSnapshotBeforeUpdate(m,l),r.__reactInternalSnapshotBeforeUpdate=e}catch(e){sc(n,n.return,e)}}break;case 3:if(1024&e)if(9===(n=(e=t.stateNode.containerInfo).nodeType))mf(e);else if(1===n)switch(e.nodeName){case"HEAD":case"HTML":case"BODY":mf(e);break;default:e.textContent=""}break;default:if(1024&e)throw Error(o(163))}if(null!==(e=t.sibling)){e.return=t.return,wu=e;break}wu=t.return}}(e,t)}finally{ts=i,O.p=a,R.T=r}}Es=1,nc(),rc(),ac()}}function nc(){if(1===Es){Es=0;var e=Cs,t=zs,n=!!(13878&t.flags);if(13878&t.subtreeFlags||n){n=R.T,R.T=null;var r=O.p;O.p=2;var a=ts;ts|=4;try{Ru(t,e);var l=tf,o=Jn(e.containerInfo),i=l.focusedElem,u=l.selectionRange;if(o!==i&&i&&i.ownerDocument&&Zn(i.ownerDocument.documentElement,i)){if(null!==u&&er(i)){var s=u.start,c=u.end;if(void 0===c&&(c=s),"selectionStart"in i)i.selectionStart=s,i.selectionEnd=Math.min(c,i.value.length);else{var f=i.ownerDocument||document,d=f&&f.defaultView||window;if(d.getSelection){var p=d.getSelection(),h=i.textContent.length,m=Math.min(u.start,h),g=void 0===u.end?m:Math.min(u.end,h);!p.extend&&m>g&&(o=g,g=m,m=o);var y=Xn(i,m),v=Xn(i,g);if(y&&v&&(1!==p.rangeCount||p.anchorNode!==y.node||p.anchorOffset!==y.offset||p.focusNode!==v.node||p.focusOffset!==v.offset)){var b=f.createRange();b.setStart(y.node,y.offset),p.removeAllRanges(),m>g?(p.addRange(b),p.extend(v.node,v.offset)):(b.setEnd(v.node,v.offset),p.addRange(b))}}}}for(f=[],p=i;p=p.parentNode;)1===p.nodeType&&f.push({element:p,left:p.scrollLeft,top:p.scrollTop});for("function"==typeof i.focus&&i.focus(),i=0;i<f.length;i++){var S=f[i];S.element.scrollLeft=S.left,S.element.scrollTop=S.top}}ed=!!ef,tf=ef=null}finally{ts=a,O.p=r,R.T=n}}e.current=t,Es=2}}function rc(){if(2===Es){Es=0;var e=Cs,t=zs,n=!!(8772&t.flags);if(8772&t.subtreeFlags||n){n=R.T,R.T=null;var r=O.p;O.p=2;var a=ts;ts|=4;try{xu(e,t.alternate,t)}finally{ts=a,O.p=r,R.T=n}}Es=3}}function ac(){if(4===Es||3===Es){Es=0,J();var e=Cs,t=zs,n=Ps,r=Ls;10256&t.subtreeFlags||10256&t.flags?Es=5:(Es=0,zs=Cs=null,lc(e,e.pendingLanes));var a=e.pendingLanes;if(0===a&&(xs=null),_e(n),t=t.stateNode,ce&&"function"==typeof ce.onCommitFiberRoot)try{ce.onCommitFiberRoot(se,t,void 0,!(128&~t.current.flags))}catch(e){}if(null!==r){t=R.T,a=O.p,O.p=2,R.T=null;try{for(var l=e.onRecoverableError,o=0;o<r.length;o++){var i=r[o];l(i.value,{componentStack:i.stack})}}finally{R.T=t,O.p=a}}3&Ps&&oc(),kc(e),a=e.pendingLanes,4194090&n&&42&a?e===As?Ts++:(Ts=0,As=e):Ts=0,wc(0,!1)}}function lc(e,t){0===(e.pooledCacheLanes&=t)&&null!=(t=e.pooledCache)&&(e.pooledCache=null,Ra(t))}function oc(e){return nc(),rc(),ac(),ic()}function ic(){if(5!==Es)return!1;var e=Cs,t=_s;_s=0;var n=_e(Ps),r=R.T,a=O.p;try{O.p=32>n?32:n,R.T=null,n=Ns,Ns=null;var l=Cs,i=Ps;if(Es=0,zs=Cs=null,Ps=0,6&ts)throw Error(o(331));var u=ts;if(ts|=4,Ku(l.current),Hu(l,l.current,i,n),ts=u,wc(0,!1),ce&&"function"==typeof ce.onPostCommitFiberRoot)try{ce.onPostCommitFiberRoot(se,l)}catch(e){}return!0}finally{O.p=a,R.T=r,lc(e,t)}}function uc(e,t,n){t=xr(n,t),null!==(e=al(e,t=wi(e.stateNode,t,2),2))&&(Ee(e,2),kc(e))}function sc(e,t,n){if(3===e.tag)uc(e,e,n);else for(;null!==t;){if(3===t.tag){uc(t,e,n);break}if(1===t.tag){var r=t.stateNode;if("function"==typeof t.type.getDerivedStateFromError||"function"==typeof r.componentDidCatch&&(null===xs||!xs.has(r))){e=xr(n,e),null!==(r=al(t,n=xi(2),2))&&(Ei(n,r,t,e),Ee(r,2),kc(r));break}}t=t.return}}function cc(e,t,n){var r=e.pingCache;if(null===r){r=e.pingCache=new es;var a=new Set;r.set(t,a)}else void 0===(a=r.get(t))&&(a=new Set,r.set(t,a));a.has(n)||(ss=!0,a.add(n),e=fc.bind(null,e,t,n),t.then(e,e))}function fc(e,t,n){var r=e.pingCache;null!==r&&r.delete(t),e.pingedLanes|=e.suspendedLanes&n,e.warmLanes&=~n,ns===e&&(as&n)===n&&(4===fs||3===fs&&(62914560&as)===as&&300>ee()-Ss?!(2&ts)&&Hs(e,0):hs|=n,gs===as&&(gs=0)),kc(e)}function dc(e,t){0===t&&(t=we()),null!==(e=Lr(e,t))&&(Ee(e,t),kc(e))}function pc(e){var t=e.memoizedState,n=0;null!==t&&(n=t.retryLane),dc(e,n)}function hc(e,t){var n=0;switch(e.tag){case 13:var r=e.stateNode,a=e.memoizedState;null!==a&&(n=a.retryLane);break;case 19:r=e.stateNode;break;case 22:r=e.stateNode._retryCache;break;default:throw Error(o(314))}null!==r&&r.delete(t),dc(e,n)}var mc=null,gc=null,yc=!1,vc=!1,bc=!1,Sc=0;function kc(e){e!==gc&&null===e.next&&(null===gc?mc=gc=e:gc=gc.next=e),vc=!0,yc||(yc=!0,ff(function(){6&ts?K(ne,xc):Ec()}))}function wc(e,t){if(!bc&&vc){bc=!0;do{for(var n=!1,r=mc;null!==r;){if(!t)if(0!==e){var a=r.pendingLanes;if(0===a)var l=0;else{var o=r.suspendedLanes,i=r.pingedLanes;l=(1<<31-de(42|e)+1)-1,l=201326741&(l&=a&~(o&~i))?201326741&l|1:l?2|l:0}0!==l&&(n=!0,Pc(r,l))}else l=as,!(3&(l=ve(r,r===ns?l:0,null!==r.cancelPendingCommit||-1!==r.timeoutHandle)))||be(r,l)||(n=!0,Pc(r,l));r=r.next}}while(n);bc=!1}}function xc(){Ec()}function Ec(){vc=yc=!1;var e,t=0;0!==Sc&&(((e=window.event)&&"popstate"===e.type?e!==of&&(of=e,!0):(of=null,!1))&&(t=Sc),Sc=0);for(var n=ee(),r=null,a=mc;null!==a;){var l=a.next,o=Cc(a,n);0===o?(a.next=null,null===r?mc=l:r.next=l,null===l&&(gc=r)):(r=a,(0!==t||3&o)&&(vc=!0)),a=l}wc(t,!1)}function Cc(e,t){for(var n=e.suspendedLanes,r=e.pingedLanes,a=e.expirationTimes,l=-62914561&e.pendingLanes;0<l;){var o=31-de(l),i=1<<o,u=a[o];-1===u?0!==(i&n)&&0===(i&r)||(a[o]=Se(i,t)):u<=t&&(e.expiredLanes|=i),l&=~i}if(n=as,n=ve(e,e===(t=ns)?n:0,null!==e.cancelPendingCommit||-1!==e.timeoutHandle),r=e.callbackNode,0===n||e===t&&(2===ls||9===ls)||null!==e.cancelPendingCommit)return null!==r&&null!==r&&X(r),e.callbackNode=null,e.callbackPriority=0;if(!(3&n)||be(e,n)){if((t=n&-n)===e.callbackPriority)return t;switch(null!==r&&X(r),_e(n)){case 2:case 8:n=re;break;case 32:default:n=ae;break;case 268435456:n=oe}return r=zc.bind(null,e),n=K(n,r),e.callbackPriority=t,e.callbackNode=n,t}return null!==r&&null!==r&&X(r),e.callbackPriority=2,e.callbackNode=null,2}function zc(e,t){if(0!==Es&&5!==Es)return e.callbackNode=null,e.callbackPriority=0,null;var n=e.callbackNode;if(oc()&&e.callbackNode!==n)return null;var r=as;return 0===(r=ve(e,e===ns?r:0,null!==e.cancelPendingCommit||-1!==e.timeoutHandle))?null:(Ds(e,r,t),Cc(e,ee()),null!=e.callbackNode&&e.callbackNode===n?zc.bind(null,e):null)}function Pc(e,t){if(oc())return null;Ds(e,t,!0)}function _c(){return 0===Sc&&(Sc=ke()),Sc}function Nc(e){return null==e||"symbol"==typeof e||"boolean"==typeof e?null:"function"==typeof e?e:_t(""+e)}function Lc(e,t){var n=t.ownerDocument.createElement("input");return n.name=t.name,n.value=t.value,e.id&&n.setAttribute("form",e.id),t.parentNode.insertBefore(n,t),e=new FormData(e),n.parentNode.removeChild(n),e}for(var Tc=0;Tc<Sr.length;Tc++){var Ac=Sr[Tc];kr(Ac.toLowerCase(),"on"+(Ac[0].toUpperCase()+Ac.slice(1)))}kr(dr,"onAnimationEnd"),kr(pr,"onAnimationIteration"),kr(hr,"onAnimationStart"),kr("dblclick","onDoubleClick"),kr("focusin","onFocus"),kr("focusout","onBlur"),kr(mr,"onTransitionRun"),kr(gr,"onTransitionStart"),kr(yr,"onTransitionCancel"),kr(vr,"onTransitionEnd"),Ge("onMouseEnter",["mouseout","mouseover"]),Ge("onMouseLeave",["mouseout","mouseover"]),Ge("onPointerEnter",["pointerout","pointerover"]),Ge("onPointerLeave",["pointerout","pointerover"]),qe("onChange","change click focusin focusout input keydown keyup selectionchange".split(" ")),qe("onSelect","focusout contextmenu dragend focusin keydown keyup mousedown mouseup selectionchange".split(" ")),qe("onBeforeInput",["compositionend","keypress","textInput","paste"]),qe("onCompositionEnd","compositionend focusout keydown keypress keyup mousedown".split(" ")),qe("onCompositionStart","compositionstart focusout keydown keypress keyup mousedown".split(" ")),qe("onCompositionUpdate","compositionupdate focusout keydown keypress keyup mousedown".split(" "));var Rc="abort canplay canplaythrough durationchange emptied encrypted ended error loadeddata loadedmetadata loadstart pause play playing progress ratechange resize seeked seeking stalled suspend timeupdate volumechange waiting".split(" "),Oc=new Set("beforetoggle cancel close invalid load scroll scrollend toggle".split(" ").concat(Rc));function Ic(e,t){t=!!(4&t);for(var n=0;n<e.length;n++){var r=e[n],a=r.event;r=r.listeners;e:{var l=void 0;if(t)for(var o=r.length-1;0<=o;o--){var i=r[o],u=i.instance,s=i.currentTarget;if(i=i.listener,u!==l&&a.isPropagationStopped())break e;l=i,a.currentTarget=s;try{l(a)}catch(e){gi(e)}a.currentTarget=null,l=u}else for(o=0;o<r.length;o++){if(u=(i=r[o]).instance,s=i.currentTarget,i=i.listener,u!==l&&a.isPropagationStopped())break e;l=i,a.currentTarget=s;try{l(a)}catch(e){gi(e)}a.currentTarget=null,l=u}}}}function Dc(e,t){var n=t[Oe];void 0===n&&(n=t[Oe]=new Set);var r=e+"__bubble";n.has(r)||($c(t,e,2,!1),n.add(r))}function Mc(e,t,n){var r=0;t&&(r|=4),$c(n,e,r,t)}var Fc="_reactListening"+Math.random().toString(36).slice(2);function jc(e){if(!e[Fc]){e[Fc]=!0,We.forEach(function(t){"selectionchange"!==t&&(Oc.has(t)||Mc(t,!1,e),Mc(t,!0,e))});var t=9===e.nodeType?e:e.ownerDocument;null===t||t[Fc]||(t[Fc]=!0,Mc("selectionchange",!1,t))}}function $c(e,t,n,r){switch(id(t)){case 2:var a=td;break;case 8:a=nd;break;default:a=rd}n=a.bind(null,t,n,e),a=void 0,!Ft||"touchstart"!==t&&"touchmove"!==t&&"wheel"!==t||(a=!0),r?void 0!==a?e.addEventListener(t,n,{capture:!0,passive:a}):e.addEventListener(t,n,!0):void 0!==a?e.addEventListener(t,n,{passive:a}):e.addEventListener(t,n,!1)}function Uc(e,t,n,r,a){var l=r;if(!(1&t||2&t||null===r))e:for(;;){if(null===r)return;var o=r.tag;if(3===o||4===o){var u=r.stateNode.containerInfo;if(u===a)break;if(4===o)for(o=r.return;null!==o;){var s=o.tag;if((3===s||4===s)&&o.stateNode.containerInfo===a)return;o=o.return}for(;null!==u;){if(null===(o=$e(u)))return;if(5===(s=o.tag)||6===s||26===s||27===s){r=l=o;continue e}u=u.parentNode}}r=r.return}It(function(){var r=l,a=Lt(n),o=[];e:{var u=br.get(e);if(void 0!==u){var s=Zt,c=e;switch(e){case"keypress":if(0===Vt(n))break e;case"keydown":case"keyup":s=hn;break;case"focusin":c="focus",s=an;break;case"focusout":c="blur",s=an;break;case"beforeblur":case"afterblur":s=an;break;case"click":if(2===n.button)break e;case"auxclick":case"dblclick":case"mousedown":case"mousemove":case"mouseup":case"mouseout":case"mouseover":case"contextmenu":s=nn;break;case"drag":case"dragend":case"dragenter":case"dragexit":case"dragleave":case"dragover":case"dragstart":case"drop":s=rn;break;case"touchcancel":case"touchend":case"touchmove":case"touchstart":s=gn;break;case dr:case pr:case hr:s=ln;break;case vr:s=yn;break;case"scroll":case"scrollend":s=en;break;case"wheel":s=vn;break;case"copy":case"cut":case"paste":s=on;break;case"gotpointercapture":case"lostpointercapture":case"pointercancel":case"pointerdown":case"pointermove":case"pointerout":case"pointerover":case"pointerup":s=mn;break;case"toggle":case"beforetoggle":s=bn}var f=!!(4&t),d=!f&&("scroll"===e||"scrollend"===e),p=f?null!==u?u+"Capture":null:u;f=[];for(var h,m=r;null!==m;){var g=m;if(h=g.stateNode,5!==(g=g.tag)&&26!==g&&27!==g||null===h||null===p||null!=(g=Dt(m,p))&&f.push(Hc(m,g,h)),d)break;m=m.return}0<f.length&&(u=new s(u,c,null,n,a),o.push({event:u,listeners:f}))}}if(!(7&t)){if(s="mouseout"===e||"pointerout"===e,(!(u="mouseover"===e||"pointerover"===e)||n===Nt||!(c=n.relatedTarget||n.fromElement)||!$e(c)&&!c[Re])&&(s||u)&&(u=a.window===a?a:(u=a.ownerDocument)?u.defaultView||u.parentWindow:window,s?(s=r,null!==(c=(c=n.relatedTarget||n.toElement)?$e(c):null)&&(d=i(c),f=c.tag,c!==d||5!==f&&27!==f&&6!==f)&&(c=null)):(s=null,c=r),s!==c)){if(f=nn,g="onMouseLeave",p="onMouseEnter",m="mouse","pointerout"!==e&&"pointerover"!==e||(f=mn,g="onPointerLeave",p="onPointerEnter",m="pointer"),d=null==s?u:He(s),h=null==c?u:He(c),(u=new f(g,m+"leave",s,n,a)).target=d,u.relatedTarget=h,g=null,$e(a)===r&&((f=new f(p,m+"enter",c,n,a)).target=h,f.relatedTarget=d,g=f),d=g,s&&c)e:{for(p=c,m=0,h=f=s;h;h=Vc(h))m++;for(h=0,g=p;g;g=Vc(g))h++;for(;0<m-h;)f=Vc(f),m--;for(;0<h-m;)p=Vc(p),h--;for(;m--;){if(f===p||null!==p&&f===p.alternate)break e;f=Vc(f),p=Vc(p)}f=null}else f=null;null!==s&&Wc(o,u,s,f,!1),null!==c&&null!==d&&Wc(o,d,c,f,!0)}if("select"===(s=(u=r?He(r):window).nodeName&&u.nodeName.toLowerCase())||"input"===s&&"file"===u.type)var y=Mn;else if(Tn(u))if(Fn)y=qn;else{y=Wn;var v=Vn}else!(s=u.nodeName)||"input"!==s.toLowerCase()||"checkbox"!==u.type&&"radio"!==u.type?r&&Ct(r.elementType)&&(y=Mn):y=Qn;switch(y&&(y=y(e,r))?An(o,y,n,a):(v&&v(e,u,r),"focusout"===e&&r&&"number"===u.type&&null!=r.memoizedProps.value&&yt(u,"number",u.value)),v=r?He(r):window,e){case"focusin":(Tn(v)||"true"===v.contentEditable)&&(nr=v,rr=r,ar=null);break;case"focusout":ar=rr=nr=null;break;case"mousedown":lr=!0;break;case"contextmenu":case"mouseup":case"dragend":lr=!1,or(o,n,a);break;case"selectionchange":if(tr)break;case"keydown":case"keyup":or(o,n,a)}var b;if(kn)e:{switch(e){case"compositionstart":var S="onCompositionStart";break e;case"compositionend":S="onCompositionEnd";break e;case"compositionupdate":S="onCompositionUpdate";break e}S=void 0}else Nn?Pn(e,n)&&(S="onCompositionEnd"):"keydown"===e&&229===n.keyCode&&(S="onCompositionStart");S&&(En&&"ko"!==n.locale&&(Nn||"onCompositionStart"!==S?"onCompositionEnd"===S&&Nn&&(b=Bt()):(Ut="value"in($t=a)?$t.value:$t.textContent,Nn=!0)),0<(v=Bc(r,S)).length&&(S=new un(S,e,null,n,a),o.push({event:S,listeners:v}),(b||null!==(b=_n(n)))&&(S.data=b))),(b=xn?function(e,t){switch(e){case"compositionend":return _n(t);case"keypress":return 32!==t.which?null:(zn=!0,Cn);case"textInput":return(e=t.data)===Cn&&zn?null:e;default:return null}}(e,n):function(e,t){if(Nn)return"compositionend"===e||!kn&&Pn(e,t)?(e=Bt(),Ht=Ut=$t=null,Nn=!1,e):null;switch(e){case"paste":default:return null;case"keypress":if(!(t.ctrlKey||t.altKey||t.metaKey)||t.ctrlKey&&t.altKey){if(t.char&&1<t.char.length)return t.char;if(t.which)return String.fromCharCode(t.which)}return null;case"compositionend":return En&&"ko"!==t.locale?null:t.data}}(e,n))&&0<(S=Bc(r,"onBeforeInput")).length&&(v=new un("onBeforeInput","beforeinput",null,n,a),o.push({event:v,listeners:S}),v.data=b),function(e,t,n,r,a){if("submit"===t&&n&&n.stateNode===a){var l=Nc((a[Ae]||null).action),o=r.submitter;o&&null!==(t=(t=o[Ae]||null)?Nc(t.formAction):o.getAttribute("formAction"))&&(l=t,o=null);var i=new Zt("action","action",null,r,a);e.push({event:i,listeners:[{instance:null,listener:function(){if(r.defaultPrevented){if(0!==Sc){var e=o?Lc(a,o):new FormData(a);Lo(n,{pending:!0,data:e,method:a.method,action:l},null,e)}}else"function"==typeof l&&(i.preventDefault(),e=o?Lc(a,o):new FormData(a),Lo(n,{pending:!0,data:e,method:a.method,action:l},l,e))},currentTarget:a}]})}}(o,e,r,n,a)}Ic(o,t)})}function Hc(e,t,n){return{instance:e,listener:t,currentTarget:n}}function Bc(e,t){for(var n=t+"Capture",r=[];null!==e;){var a=e,l=a.stateNode;if(5!==(a=a.tag)&&26!==a&&27!==a||null===l||(null!=(a=Dt(e,n))&&r.unshift(Hc(e,a,l)),null!=(a=Dt(e,t))&&r.push(Hc(e,a,l))),3===e.tag)return r;e=e.return}return[]}function Vc(e){if(null===e)return null;do{e=e.return}while(e&&5!==e.tag&&27!==e.tag);return e||null}function Wc(e,t,n,r,a){for(var l=t._reactName,o=[];null!==n&&n!==r;){var i=n,u=i.alternate,s=i.stateNode;if(i=i.tag,null!==u&&u===r)break;5!==i&&26!==i&&27!==i||null===s||(u=s,a?null!=(s=Dt(n,l))&&o.unshift(Hc(n,s,u)):a||null!=(s=Dt(n,l))&&o.push(Hc(n,s,u))),n=n.return}0!==o.length&&e.push({event:t,listeners:o})}var Qc=/\r\n?/g,qc=/\u0000|\uFFFD/g;function Gc(e){return("string"==typeof e?e:""+e).replace(Qc,"\n").replace(qc,"")}function Yc(e,t){return t=Gc(t),Gc(e)===t}function Kc(){}function Xc(e,t,n,r,a,l){switch(n){case"children":"string"==typeof r?"body"===t||"textarea"===t&&""===r||kt(e,r):("number"==typeof r||"bigint"==typeof r)&&"body"!==t&&kt(e,""+r);break;case"className":tt(e,"class",r);break;case"tabIndex":tt(e,"tabindex",r);break;case"dir":case"role":case"viewBox":case"width":case"height":tt(e,n,r);break;case"style":Et(e,r,l);break;case"data":if("object"!==t){tt(e,"data",r);break}case"src":case"href":if(""===r&&("a"!==t||"href"!==n)){e.removeAttribute(n);break}if(null==r||"function"==typeof r||"symbol"==typeof r||"boolean"==typeof r){e.removeAttribute(n);break}r=_t(""+r),e.setAttribute(n,r);break;case"action":case"formAction":if("function"==typeof r){e.setAttribute(n,"javascript:throw new Error('A React form was unexpectedly submitted. If you called form.submit() manually, consider using form.requestSubmit() instead. If you\\'re trying to use event.stopPropagation() in a submit event handler, consider also calling event.preventDefault().')");break}if("function"==typeof l&&("formAction"===n?("input"!==t&&Xc(e,t,"name",a.name,a,null),Xc(e,t,"formEncType",a.formEncType,a,null),Xc(e,t,"formMethod",a.formMethod,a,null),Xc(e,t,"formTarget",a.formTarget,a,null)):(Xc(e,t,"encType",a.encType,a,null),Xc(e,t,"method",a.method,a,null),Xc(e,t,"target",a.target,a,null))),null==r||"symbol"==typeof r||"boolean"==typeof r){e.removeAttribute(n);break}r=_t(""+r),e.setAttribute(n,r);break;case"onClick":null!=r&&(e.onclick=Kc);break;case"onScroll":null!=r&&Dc("scroll",e);break;case"onScrollEnd":null!=r&&Dc("scrollend",e);break;case"dangerouslySetInnerHTML":if(null!=r){if("object"!=typeof r||!("__html"in r))throw Error(o(61));if(null!=(n=r.__html)){if(null!=a.children)throw Error(o(60));e.innerHTML=n}}break;case"multiple":e.multiple=r&&"function"!=typeof r&&"symbol"!=typeof r;break;case"muted":e.muted=r&&"function"!=typeof r&&"symbol"!=typeof r;break;case"suppressContentEditableWarning":case"suppressHydrationWarning":case"defaultValue":case"defaultChecked":case"innerHTML":case"ref":case"autoFocus":break;case"xlinkHref":if(null==r||"function"==typeof r||"boolean"==typeof r||"symbol"==typeof r){e.removeAttribute("xlink:href");break}n=_t(""+r),e.setAttributeNS("http://www.w3.org/1999/xlink","xlink:href",n);break;case"contentEditable":case"spellCheck":case"draggable":case"value":case"autoReverse":case"externalResourcesRequired":case"focusable":case"preserveAlpha":null!=r&&"function"!=typeof r&&"symbol"!=typeof r?e.setAttribute(n,""+r):e.removeAttribute(n);break;case"inert":case"allowFullScreen":case"async":case"autoPlay":case"controls":case"default":case"defer":case"disabled":case"disablePictureInPicture":case"disableRemotePlayback":case"formNoValidate":case"hidden":case"loop":case"noModule":case"noValidate":case"open":case"playsInline":case"readOnly":case"required":case"reversed":case"scoped":case"seamless":case"itemScope":r&&"function"!=typeof r&&"symbol"!=typeof r?e.setAttribute(n,""):e.removeAttribute(n);break;case"capture":case"download":!0===r?e.setAttribute(n,""):!1!==r&&null!=r&&"function"!=typeof r&&"symbol"!=typeof r?e.setAttribute(n,r):e.removeAttribute(n);break;case"cols":case"rows":case"size":case"span":null!=r&&"function"!=typeof r&&"symbol"!=typeof r&&!isNaN(r)&&1<=r?e.setAttribute(n,r):e.removeAttribute(n);break;case"rowSpan":case"start":null==r||"function"==typeof r||"symbol"==typeof r||isNaN(r)?e.removeAttribute(n):e.setAttribute(n,r);break;case"popover":Dc("beforetoggle",e),Dc("toggle",e),et(e,"popover",r);break;case"xlinkActuate":nt(e,"http://www.w3.org/1999/xlink","xlink:actuate",r);break;case"xlinkArcrole":nt(e,"http://www.w3.org/1999/xlink","xlink:arcrole",r);break;case"xlinkRole":nt(e,"http://www.w3.org/1999/xlink","xlink:role",r);break;case"xlinkShow":nt(e,"http://www.w3.org/1999/xlink","xlink:show",r);break;case"xlinkTitle":nt(e,"http://www.w3.org/1999/xlink","xlink:title",r);break;case"xlinkType":nt(e,"http://www.w3.org/1999/xlink","xlink:type",r);break;case"xmlBase":nt(e,"http://www.w3.org/XML/1998/namespace","xml:base",r);break;case"xmlLang":nt(e,"http://www.w3.org/XML/1998/namespace","xml:lang",r);break;case"xmlSpace":nt(e,"http://www.w3.org/XML/1998/namespace","xml:space",r);break;case"is":et(e,"is",r);break;case"innerText":case"textContent":break;default:(!(2<n.length)||"o"!==n[0]&&"O"!==n[0]||"n"!==n[1]&&"N"!==n[1])&&et(e,n=zt.get(n)||n,r)}}function Zc(e,t,n,r,a,l){switch(n){case"style":Et(e,r,l);break;case"dangerouslySetInnerHTML":if(null!=r){if("object"!=typeof r||!("__html"in r))throw Error(o(61));if(null!=(n=r.__html)){if(null!=a.children)throw Error(o(60));e.innerHTML=n}}break;case"children":"string"==typeof r?kt(e,r):("number"==typeof r||"bigint"==typeof r)&&kt(e,""+r);break;case"onScroll":null!=r&&Dc("scroll",e);break;case"onScrollEnd":null!=r&&Dc("scrollend",e);break;case"onClick":null!=r&&(e.onclick=Kc);break;case"suppressContentEditableWarning":case"suppressHydrationWarning":case"innerHTML":case"ref":case"innerText":case"textContent":break;default:Qe.hasOwnProperty(n)||("o"!==n[0]||"n"!==n[1]||(a=n.endsWith("Capture"),t=n.slice(2,a?n.length-7:void 0),"function"==typeof(l=null!=(l=e[Ae]||null)?l[n]:null)&&e.removeEventListener(t,l,a),"function"!=typeof r)?n in e?e[n]=r:!0===r?e.setAttribute(n,""):et(e,n,r):("function"!=typeof l&&null!==l&&(n in e?e[n]=null:e.hasAttribute(n)&&e.removeAttribute(n)),e.addEventListener(t,r,a)))}}function Jc(e,t,n){switch(t){case"div":case"span":case"svg":case"path":case"a":case"g":case"p":case"li":break;case"img":Dc("error",e),Dc("load",e);var r,a=!1,l=!1;for(r in n)if(n.hasOwnProperty(r)){var i=n[r];if(null!=i)switch(r){case"src":a=!0;break;case"srcSet":l=!0;break;case"children":case"dangerouslySetInnerHTML":throw Error(o(137,t));default:Xc(e,t,r,i,n,null)}}return l&&Xc(e,t,"srcSet",n.srcSet,n,null),void(a&&Xc(e,t,"src",n.src,n,null));case"input":Dc("invalid",e);var u=r=i=l=null,s=null,c=null;for(a in n)if(n.hasOwnProperty(a)){var f=n[a];if(null!=f)switch(a){case"name":l=f;break;case"type":i=f;break;case"checked":s=f;break;case"defaultChecked":c=f;break;case"value":r=f;break;case"defaultValue":u=f;break;case"children":case"dangerouslySetInnerHTML":if(null!=f)throw Error(o(137,t));break;default:Xc(e,t,a,f,n,null)}}return gt(e,r,u,s,c,i,l,!1),void ct(e);case"select":for(l in Dc("invalid",e),a=i=r=null,n)if(n.hasOwnProperty(l)&&null!=(u=n[l]))switch(l){case"value":r=u;break;case"defaultValue":i=u;break;case"multiple":a=u;default:Xc(e,t,l,u,n,null)}return t=r,n=i,e.multiple=!!a,void(null!=t?vt(e,!!a,t,!1):null!=n&&vt(e,!!a,n,!0));case"textarea":for(i in Dc("invalid",e),r=l=a=null,n)if(n.hasOwnProperty(i)&&null!=(u=n[i]))switch(i){case"value":a=u;break;case"defaultValue":l=u;break;case"children":r=u;break;case"dangerouslySetInnerHTML":if(null!=u)throw Error(o(91));break;default:Xc(e,t,i,u,n,null)}return St(e,a,l,r),void ct(e);case"option":for(s in n)n.hasOwnProperty(s)&&null!=(a=n[s])&&("selected"===s?e.selected=a&&"function"!=typeof a&&"symbol"!=typeof a:Xc(e,t,s,a,n,null));return;case"dialog":Dc("beforetoggle",e),Dc("toggle",e),Dc("cancel",e),Dc("close",e);break;case"iframe":case"object":Dc("load",e);break;case"video":case"audio":for(a=0;a<Rc.length;a++)Dc(Rc[a],e);break;case"image":Dc("error",e),Dc("load",e);break;case"details":Dc("toggle",e);break;case"embed":case"source":case"link":Dc("error",e),Dc("load",e);case"area":case"base":case"br":case"col":case"hr":case"keygen":case"meta":case"param":case"track":case"wbr":case"menuitem":for(c in n)if(n.hasOwnProperty(c)&&null!=(a=n[c]))switch(c){case"children":case"dangerouslySetInnerHTML":throw Error(o(137,t));default:Xc(e,t,c,a,n,null)}return;default:if(Ct(t)){for(f in n)n.hasOwnProperty(f)&&void 0!==(a=n[f])&&Zc(e,t,f,a,n,void 0);return}}for(u in n)n.hasOwnProperty(u)&&null!=(a=n[u])&&Xc(e,t,u,a,n,null)}var ef=null,tf=null;function nf(e){return 9===e.nodeType?e:e.ownerDocument}function rf(e){switch(e){case"http://www.w3.org/2000/svg":return 1;case"http://www.w3.org/1998/Math/MathML":return 2;default:return 0}}function af(e,t){if(0===e)switch(t){case"svg":return 1;case"math":return 2;default:return 0}return 1===e&&"foreignObject"===t?0:e}function lf(e,t){return"textarea"===e||"noscript"===e||"string"==typeof t.children||"number"==typeof t.children||"bigint"==typeof t.children||"object"==typeof t.dangerouslySetInnerHTML&&null!==t.dangerouslySetInnerHTML&&null!=t.dangerouslySetInnerHTML.__html}var of=null,uf="function"==typeof setTimeout?setTimeout:void 0,sf="function"==typeof clearTimeout?clearTimeout:void 0,cf="function"==typeof Promise?Promise:void 0,ff="function"==typeof queueMicrotask?queueMicrotask:void 0!==cf?function(e){return cf.resolve(null).then(e).catch(df)}:uf;function df(e){setTimeout(function(){throw e})}function pf(e){return"head"===e}function hf(e,t){var n=t,r=0,a=0;do{var l=n.nextSibling;if(e.removeChild(n),l&&8===l.nodeType)if("/$"===(n=l.data)){if(0<r&&8>r){n=r;var o=e.ownerDocument;if(1&n&&kf(o.documentElement),2&n&&kf(o.body),4&n)for(kf(n=o.head),o=n.firstChild;o;){var i=o.nextSibling,u=o.nodeName;o[Fe]||"SCRIPT"===u||"STYLE"===u||"LINK"===u&&"stylesheet"===o.rel.toLowerCase()||n.removeChild(o),o=i}}if(0===a)return e.removeChild(l),void Cd(t);a--}else"$"===n||"$?"===n||"$!"===n?a++:r=n.charCodeAt(0)-48;else r=0;n=l}while(n);Cd(t)}function mf(e){var t=e.firstChild;for(t&&10===t.nodeType&&(t=t.nextSibling);t;){var n=t;switch(t=t.nextSibling,n.nodeName){case"HTML":case"HEAD":case"BODY":mf(n),je(n);continue;case"SCRIPT":case"STYLE":continue;case"LINK":if("stylesheet"===n.rel.toLowerCase())continue}e.removeChild(n)}}function gf(e){return"$!"===e.data||"$?"===e.data&&"complete"===e.ownerDocument.readyState}function yf(e){for(;null!=e;e=e.nextSibling){var t=e.nodeType;if(1===t||3===t)break;if(8===t){if("$"===(t=e.data)||"$!"===t||"$?"===t||"F!"===t||"F"===t)break;if("/$"===t)return null}}return e}var vf=null;function bf(e){e=e.previousSibling;for(var t=0;e;){if(8===e.nodeType){var n=e.data;if("$"===n||"$!"===n||"$?"===n){if(0===t)return e;t--}else"/$"===n&&t++}e=e.previousSibling}return null}function Sf(e,t,n){switch(t=nf(n),e){case"html":if(!(e=t.documentElement))throw Error(o(452));return e;case"head":if(!(e=t.head))throw Error(o(453));return e;case"body":if(!(e=t.body))throw Error(o(454));return e;default:throw Error(o(451))}}function kf(e){for(var t=e.attributes;t.length;)e.removeAttributeNode(t[0]);je(e)}var wf=new Map,xf=new Set;function Ef(e){return"function"==typeof e.getRootNode?e.getRootNode():9===e.nodeType?e:e.ownerDocument}var Cf=O.d;O.d={f:function(){var e=Cf.f(),t=$s();return e||t},r:function(e){var t=Ue(e);null!==t&&5===t.tag&&"form"===t.type?Ao(t):Cf.r(e)},D:function(e){Cf.D(e),Pf("dns-prefetch",e,null)},C:function(e,t){Cf.C(e,t),Pf("preconnect",e,t)},L:function(e,t,n){Cf.L(e,t,n);var r=zf;if(r&&e&&t){var a='link[rel="preload"][as="'+ht(t)+'"]';"image"===t&&n&&n.imageSrcSet?(a+='[imagesrcset="'+ht(n.imageSrcSet)+'"]',"string"==typeof n.imageSizes&&(a+='[imagesizes="'+ht(n.imageSizes)+'"]')):a+='[href="'+ht(e)+'"]';var l=a;switch(t){case"style":l=Nf(e);break;case"script":l=Af(e)}wf.has(l)||(e=f({rel:"preload",href:"image"===t&&n&&n.imageSrcSet?void 0:e,as:t},n),wf.set(l,e),null!==r.querySelector(a)||"style"===t&&r.querySelector(Lf(l))||"script"===t&&r.querySelector(Rf(l))||(Jc(t=r.createElement("link"),"link",e),Ve(t),r.head.appendChild(t)))}},m:function(e,t){Cf.m(e,t);var n=zf;if(n&&e){var r=t&&"string"==typeof t.as?t.as:"script",a='link[rel="modulepreload"][as="'+ht(r)+'"][href="'+ht(e)+'"]',l=a;switch(r){case"audioworklet":case"paintworklet":case"serviceworker":case"sharedworker":case"worker":case"script":l=Af(e)}if(!wf.has(l)&&(e=f({rel:"modulepreload",href:e},t),wf.set(l,e),null===n.querySelector(a))){switch(r){case"audioworklet":case"paintworklet":case"serviceworker":case"sharedworker":case"worker":case"script":if(n.querySelector(Rf(l)))return}Jc(r=n.createElement("link"),"link",e),Ve(r),n.head.appendChild(r)}}},X:function(e,t){Cf.X(e,t);var n=zf;if(n&&e){var r=Be(n).hoistableScripts,a=Af(e),l=r.get(a);l||((l=n.querySelector(Rf(a)))||(e=f({src:e,async:!0},t),(t=wf.get(a))&&Mf(e,t),Ve(l=n.createElement("script")),Jc(l,"link",e),n.head.appendChild(l)),l={type:"script",instance:l,count:1,state:null},r.set(a,l))}},S:function(e,t,n){Cf.S(e,t,n);var r=zf;if(r&&e){var a=Be(r).hoistableStyles,l=Nf(e);t=t||"default";var o=a.get(l);if(!o){var i={loading:0,preload:null};if(o=r.querySelector(Lf(l)))i.loading=5;else{e=f({rel:"stylesheet",href:e,"data-precedence":t},n),(n=wf.get(l))&&Df(e,n);var u=o=r.createElement("link");Ve(u),Jc(u,"link",e),u._p=new Promise(function(e,t){u.onload=e,u.onerror=t}),u.addEventListener("load",function(){i.loading|=1}),u.addEventListener("error",function(){i.loading|=2}),i.loading|=4,If(o,t,r)}o={type:"stylesheet",instance:o,count:1,state:i},a.set(l,o)}}},M:function(e,t){Cf.M(e,t);var n=zf;if(n&&e){var r=Be(n).hoistableScripts,a=Af(e),l=r.get(a);l||((l=n.querySelector(Rf(a)))||(e=f({src:e,async:!0,type:"module"},t),(t=wf.get(a))&&Mf(e,t),Ve(l=n.createElement("script")),Jc(l,"link",e),n.head.appendChild(l)),l={type:"script",instance:l,count:1,state:null},r.set(a,l))}}};var zf="undefined"==typeof document?null:document;function Pf(e,t,n){var r=zf;if(r&&"string"==typeof t&&t){var a=ht(t);a='link[rel="'+e+'"][href="'+a+'"]',"string"==typeof n&&(a+='[crossorigin="'+n+'"]'),xf.has(a)||(xf.add(a),e={rel:e,crossOrigin:n,href:t},null===r.querySelector(a)&&(Jc(t=r.createElement("link"),"link",e),Ve(t),r.head.appendChild(t)))}}function _f(e,t,n,r){var a,l,i,u,s=(s=B.current)?Ef(s):null;if(!s)throw Error(o(446));switch(e){case"meta":case"title":return null;case"style":return"string"==typeof n.precedence&&"string"==typeof n.href?(t=Nf(n.href),(r=(n=Be(s).hoistableStyles).get(t))||(r={type:"style",instance:null,count:0,state:null},n.set(t,r)),r):{type:"void",instance:null,count:0,state:null};case"link":if("stylesheet"===n.rel&&"string"==typeof n.href&&"string"==typeof n.precedence){e=Nf(n.href);var c=Be(s).hoistableStyles,f=c.get(e);if(f||(s=s.ownerDocument||s,f={type:"stylesheet",instance:null,count:0,state:{loading:0,preload:null}},c.set(e,f),(c=s.querySelector(Lf(e)))&&!c._p&&(f.instance=c,f.state.loading=5),wf.has(e)||(n={rel:"preload",as:"style",href:n.href,crossOrigin:n.crossOrigin,integrity:n.integrity,media:n.media,hrefLang:n.hrefLang,referrerPolicy:n.referrerPolicy},wf.set(e,n),c||(a=s,l=e,i=n,u=f.state,a.querySelector('link[rel="preload"][as="style"]['+l+"]")?u.loading=1:(l=a.createElement("link"),u.preload=l,l.addEventListener("load",function(){return u.loading|=1}),l.addEventListener("error",function(){return u.loading|=2}),Jc(l,"link",i),Ve(l),a.head.appendChild(l))))),t&&null===r)throw Error(o(528,""));return f}if(t&&null!==r)throw Error(o(529,""));return null;case"script":return t=n.async,"string"==typeof(n=n.src)&&t&&"function"!=typeof t&&"symbol"!=typeof t?(t=Af(n),(r=(n=Be(s).hoistableScripts).get(t))||(r={type:"script",instance:null,count:0,state:null},n.set(t,r)),r):{type:"void",instance:null,count:0,state:null};default:throw Error(o(444,e))}}function Nf(e){return'href="'+ht(e)+'"'}function Lf(e){return'link[rel="stylesheet"]['+e+"]"}function Tf(e){return f({},e,{"data-precedence":e.precedence,precedence:null})}function Af(e){return'[src="'+ht(e)+'"]'}function Rf(e){return"script[async]"+e}function Of(e,t,n){if(t.count++,null===t.instance)switch(t.type){case"style":var r=e.querySelector('style[data-href~="'+ht(n.href)+'"]');if(r)return t.instance=r,Ve(r),r;var a=f({},n,{"data-href":n.href,"data-precedence":n.precedence,href:null,precedence:null});return Ve(r=(e.ownerDocument||e).createElement("style")),Jc(r,"style",a),If(r,n.precedence,e),t.instance=r;case"stylesheet":a=Nf(n.href);var l=e.querySelector(Lf(a));if(l)return t.state.loading|=4,t.instance=l,Ve(l),l;r=Tf(n),(a=wf.get(a))&&Df(r,a),Ve(l=(e.ownerDocument||e).createElement("link"));var i=l;return i._p=new Promise(function(e,t){i.onload=e,i.onerror=t}),Jc(l,"link",r),t.state.loading|=4,If(l,n.precedence,e),t.instance=l;case"script":return l=Af(n.src),(a=e.querySelector(Rf(l)))?(t.instance=a,Ve(a),a):(r=n,(a=wf.get(l))&&Mf(r=f({},n),a),Ve(a=(e=e.ownerDocument||e).createElement("script")),Jc(a,"link",r),e.head.appendChild(a),t.instance=a);case"void":return null;default:throw Error(o(443,t.type))}else"stylesheet"===t.type&&!(4&t.state.loading)&&(r=t.instance,t.state.loading|=4,If(r,n.precedence,e));return t.instance}function If(e,t,n){for(var r=n.querySelectorAll('link[rel="stylesheet"][data-precedence],style[data-precedence]'),a=r.length?r[r.length-1]:null,l=a,o=0;o<r.length;o++){var i=r[o];if(i.dataset.precedence===t)l=i;else if(l!==a)break}l?l.parentNode.insertBefore(e,l.nextSibling):(t=9===n.nodeType?n.head:n).insertBefore(e,t.firstChild)}function Df(e,t){null==e.crossOrigin&&(e.crossOrigin=t.crossOrigin),null==e.referrerPolicy&&(e.referrerPolicy=t.referrerPolicy),null==e.title&&(e.title=t.title)}function Mf(e,t){null==e.crossOrigin&&(e.crossOrigin=t.crossOrigin),null==e.referrerPolicy&&(e.referrerPolicy=t.referrerPolicy),null==e.integrity&&(e.integrity=t.integrity)}var Ff=null;function jf(e,t,n){if(null===Ff){var r=new Map,a=Ff=new Map;a.set(n,r)}else(r=(a=Ff).get(n))||(r=new Map,a.set(n,r));if(r.has(e))return r;for(r.set(e,null),n=n.getElementsByTagName(e),a=0;a<n.length;a++){var l=n[a];if(!(l[Fe]||l[Te]||"link"===e&&"stylesheet"===l.getAttribute("rel"))&&"http://www.w3.org/2000/svg"!==l.namespaceURI){var o=l.getAttribute(t)||"";o=e+o;var i=r.get(o);i?i.push(l):r.set(o,[l])}}return r}function $f(e,t,n){(e=e.ownerDocument||e).head.insertBefore(n,"title"===t?e.querySelector("head > title"):null)}function Uf(e){return!!("stylesheet"!==e.type||3&e.state.loading)}var Hf=null;function Bf(){}function Vf(){if(this.count--,0===this.count)if(this.stylesheets)Qf(this,this.stylesheets);else if(this.unsuspend){var e=this.unsuspend;this.unsuspend=null,e()}}var Wf=null;function Qf(e,t){e.stylesheets=null,null!==e.unsuspend&&(e.count++,Wf=new Map,t.forEach(qf,e),Wf=null,Vf.call(e))}function qf(e,t){if(!(4&t.state.loading)){var n=Wf.get(e);if(n)var r=n.get(null);else{n=new Map,Wf.set(e,n);for(var a=e.querySelectorAll("link[data-precedence],style[data-precedence]"),l=0;l<a.length;l++){var o=a[l];"LINK"!==o.nodeName&&"not all"===o.getAttribute("media")||(n.set(o.dataset.precedence,o),r=o)}r&&n.set(null,r)}o=(a=t.instance).getAttribute("data-precedence"),(l=n.get(o)||r)===r&&n.set(null,a),n.set(o,a),this.count++,r=Vf.bind(this),a.addEventListener("load",r),a.addEventListener("error",r),l?l.parentNode.insertBefore(a,l.nextSibling):(e=9===e.nodeType?e.head:e).insertBefore(a,e.firstChild),t.state.loading|=4}}var Gf={$$typeof:S,Provider:null,Consumer:null,_currentValue:I,_currentValue2:I,_threadCount:0};function Yf(e,t,n,r,a,l,o,i){this.tag=1,this.containerInfo=e,this.pingCache=this.current=this.pendingChildren=null,this.timeoutHandle=-1,this.callbackNode=this.next=this.pendingContext=this.context=this.cancelPendingCommit=null,this.callbackPriority=0,this.expirationTimes=xe(-1),this.entangledLanes=this.shellSuspendCounter=this.errorRecoveryDisabledLanes=this.expiredLanes=this.warmLanes=this.pingedLanes=this.suspendedLanes=this.pendingLanes=0,this.entanglements=xe(0),this.hiddenUpdates=xe(null),this.identifierPrefix=r,this.onUncaughtError=a,this.onCaughtError=l,this.onRecoverableError=o,this.pooledCache=null,this.pooledCacheLanes=0,this.formState=i,this.incompleteTransitions=new Map}function Kf(e,t,n,r,a,l){a=function(e){return e?e=Rr:Rr}(a),null===r.context?r.context=a:r.pendingContext=a,(r=rl(t)).payload={element:n},null!==(l=void 0===l?null:l)&&(r.callback=l),null!==(n=al(e,r,t))&&(Is(n,0,t),ll(n,e,t))}function Xf(e,t){if(null!==(e=e.memoizedState)&&null!==e.dehydrated){var n=e.retryLane;e.retryLane=0!==n&&n<t?n:t}}function Zf(e,t){Xf(e,t),(e=e.alternate)&&Xf(e,t)}function Jf(e){if(13===e.tag){var t=Lr(e,67108864);null!==t&&Is(t,0,67108864),Zf(e,67108864)}}var ed=!0;function td(e,t,n,r){var a=R.T;R.T=null;var l=O.p;try{O.p=2,rd(e,t,n,r)}finally{O.p=l,R.T=a}}function nd(e,t,n,r){var a=R.T;R.T=null;var l=O.p;try{O.p=8,rd(e,t,n,r)}finally{O.p=l,R.T=a}}function rd(e,t,n,r){if(ed){var a=ad(r);if(null===a)Uc(e,t,r,ld,n),gd(e,r);else if(function(e,t,n,r,a){switch(t){case"focusin":return sd=yd(sd,e,t,n,r,a),!0;case"dragenter":return cd=yd(cd,e,t,n,r,a),!0;case"mouseover":return fd=yd(fd,e,t,n,r,a),!0;case"pointerover":var l=a.pointerId;return dd.set(l,yd(dd.get(l)||null,e,t,n,r,a)),!0;case"gotpointercapture":return l=a.pointerId,pd.set(l,yd(pd.get(l)||null,e,t,n,r,a)),!0}return!1}(a,e,t,n,r))r.stopPropagation();else if(gd(e,r),4&t&&-1<md.indexOf(e)){for(;null!==a;){var l=Ue(a);if(null!==l)switch(l.tag){case 3:if((l=l.stateNode).current.memoizedState.isDehydrated){var o=ye(l.pendingLanes);if(0!==o){var i=l;for(i.pendingLanes|=2,i.entangledLanes|=2;o;){var u=1<<31-de(o);i.entanglements[1]|=u,o&=~u}kc(l),!(6&ts)&&(ks=ee()+500,wc(0,!1))}}break;case 13:null!==(i=Lr(l,2))&&Is(i,0,2),$s(),Zf(l,2)}if(null===(l=ad(r))&&Uc(e,t,r,ld,n),l===a)break;a=l}null!==a&&r.stopPropagation()}else Uc(e,t,r,null,n)}}function ad(e){return od(e=Lt(e))}var ld=null;function od(e){if(ld=null,null!==(e=$e(e))){var t=i(e);if(null===t)e=null;else{var n=t.tag;if(13===n){if(null!==(e=u(t)))return e;e=null}else if(3===n){if(t.stateNode.current.memoizedState.isDehydrated)return 3===t.tag?t.stateNode.containerInfo:null;e=null}else t!==e&&(e=null)}}return ld=e,null}function id(e){switch(e){case"beforetoggle":case"cancel":case"click":case"close":case"contextmenu":case"copy":case"cut":case"auxclick":case"dblclick":case"dragend":case"dragstart":case"drop":case"focusin":case"focusout":case"input":case"invalid":case"keydown":case"keypress":case"keyup":case"mousedown":case"mouseup":case"paste":case"pause":case"play":case"pointercancel":case"pointerdown":case"pointerup":case"ratechange":case"reset":case"resize":case"seeked":case"submit":case"toggle":case"touchcancel":case"touchend":case"touchstart":case"volumechange":case"change":case"selectionchange":case"textInput":case"compositionstart":case"compositionend":case"compositionupdate":case"beforeblur":case"afterblur":case"beforeinput":case"blur":case"fullscreenchange":case"focus":case"hashchange":case"popstate":case"select":case"selectstart":return 2;case"drag":case"dragenter":case"dragexit":case"dragleave":case"dragover":case"mousemove":case"mouseout":case"mouseover":case"pointermove":case"pointerout":case"pointerover":case"scroll":case"touchmove":case"wheel":case"mouseenter":case"mouseleave":case"pointerenter":case"pointerleave":return 8;case"message":switch(te()){case ne:return 2;case re:return 8;case ae:case le:return 32;case oe:return 268435456;default:return 32}default:return 32}}var ud=!1,sd=null,cd=null,fd=null,dd=new Map,pd=new Map,hd=[],md="mousedown mouseup touchcancel touchend touchstart auxclick dblclick pointercancel pointerdown pointerup dragend dragstart drop compositionend compositionstart keydown keypress keyup input textInput copy cut paste click change contextmenu reset".split(" ");function gd(e,t){switch(e){case"focusin":case"focusout":sd=null;break;case"dragenter":case"dragleave":cd=null;break;case"mouseover":case"mouseout":fd=null;break;case"pointerover":case"pointerout":dd.delete(t.pointerId);break;case"gotpointercapture":case"lostpointercapture":pd.delete(t.pointerId)}}function yd(e,t,n,r,a,l){return null===e||e.nativeEvent!==l?(e={blockedOn:t,domEventName:n,eventSystemFlags:r,nativeEvent:l,targetContainers:[a]},null!==t&&null!==(t=Ue(t))&&Jf(t),e):(e.eventSystemFlags|=r,t=e.targetContainers,null!==a&&-1===t.indexOf(a)&&t.push(a),e)}function vd(e){var t=$e(e.target);if(null!==t){var n=i(t);if(null!==n)if(13===(t=n.tag)){if(null!==(t=u(n)))return e.blockedOn=t,void function(e){var t=O.p;try{return O.p=e,function(){if(13===n.tag){var e=Rs();e=Pe(e);var t=Lr(n,e);null!==t&&Is(t,0,e),Zf(n,e)}}()}finally{O.p=t}}(e.priority)}else if(3===t&&n.stateNode.current.memoizedState.isDehydrated)return void(e.blockedOn=3===n.tag?n.stateNode.containerInfo:null)}e.blockedOn=null}function bd(e){if(null!==e.blockedOn)return!1;for(var t=e.targetContainers;0<t.length;){var n=ad(e.nativeEvent);if(null!==n)return null!==(t=Ue(n))&&Jf(t),e.blockedOn=n,!1;var r=new(n=e.nativeEvent).constructor(n.type,n);Nt=r,n.target.dispatchEvent(r),Nt=null,t.shift()}return!0}function Sd(e,t,n){bd(e)&&n.delete(t)}function kd(){ud=!1,null!==sd&&bd(sd)&&(sd=null),null!==cd&&bd(cd)&&(cd=null),null!==fd&&bd(fd)&&(fd=null),dd.forEach(Sd),pd.forEach(Sd)}function wd(e,t){e.blockedOn===t&&(e.blockedOn=null,ud||(ud=!0,r.unstable_scheduleCallback(r.unstable_NormalPriority,kd)))}var xd=null;function Ed(e){xd!==e&&(xd=e,r.unstable_scheduleCallback(r.unstable_NormalPriority,function(){xd===e&&(xd=null);for(var t=0;t<e.length;t+=3){var n=e[t],r=e[t+1],a=e[t+2];if("function"!=typeof r){if(null===od(r||n))continue;break}var l=Ue(n);null!==l&&(e.splice(t,3),t-=3,Lo(l,{pending:!0,data:a,method:n.method,action:r},r,a))}}))}function Cd(e){function t(t){return wd(t,e)}null!==sd&&wd(sd,e),null!==cd&&wd(cd,e),null!==fd&&wd(fd,e),dd.forEach(t),pd.forEach(t);for(var n=0;n<hd.length;n++){var r=hd[n];r.blockedOn===e&&(r.blockedOn=null)}for(;0<hd.length&&null===(n=hd[0]).blockedOn;)vd(n),null===n.blockedOn&&hd.shift();if(null!=(n=(e.ownerDocument||e).$$reactFormReplay))for(r=0;r<n.length;r+=3){var a=n[r],l=n[r+1],o=a[Ae]||null;if("function"==typeof l)o||Ed(n);else if(o){var i=null;if(l&&l.hasAttribute("formAction")){if(a=l,o=l[Ae]||null)i=o.formAction;else if(null!==od(a))continue}else i=o.action;"function"==typeof i?n[r+1]=i:(n.splice(r,3),r-=3),Ed(n)}}}function zd(e){this._internalRoot=e}function Pd(e){this._internalRoot=e}Pd.prototype.render=zd.prototype.render=function(e){var t=this._internalRoot;if(null===t)throw Error(o(409));Kf(t.current,Rs(),e,t,null,null)},Pd.prototype.unmount=zd.prototype.unmount=function(){var e=this._internalRoot;if(null!==e){this._internalRoot=null;var t=e.containerInfo;Kf(e.current,2,null,e,null,null),$s(),t[Re]=null}},Pd.prototype.unstable_scheduleHydration=function(e){if(e){var t=Ne();e={blockedOn:null,target:e,priority:t};for(var n=0;n<hd.length&&0!==t&&t<hd[n].priority;n++);hd.splice(n,0,e),0===n&&vd(e)}};var _d=a.version;if("19.1.0"!==_d)throw Error(o(527,_d,"19.1.0"));O.findDOMNode=function(e){var t=e._reactInternals;if(void 0===t){if("function"==typeof e.render)throw Error(o(188));throw e=Object.keys(e).join(","),Error(o(268,e))}return e=function(e){var t=e.alternate;if(!t){if(null===(t=i(e)))throw Error(o(188));return t!==e?null:e}for(var n=e,r=t;;){var a=n.return;if(null===a)break;var l=a.alternate;if(null===l){if(null!==(r=a.return)){n=r;continue}break}if(a.child===l.child){for(l=a.child;l;){if(l===n)return s(a),e;if(l===r)return s(a),t;l=l.sibling}throw Error(o(188))}if(n.return!==r.return)n=a,r=l;else{for(var u=!1,c=a.child;c;){if(c===n){u=!0,n=a,r=l;break}if(c===r){u=!0,r=a,n=l;break}c=c.sibling}if(!u){for(c=l.child;c;){if(c===n){u=!0,n=l,r=a;break}if(c===r){u=!0,r=l,n=a;break}c=c.sibling}if(!u)throw Error(o(189))}}if(n.alternate!==r)throw Error(o(190))}if(3!==n.tag)throw Error(o(188));return n.stateNode.current===n?e:t}(t),null===(e=null!==e?c(e):null)?null:e.stateNode};var Nd={bundleType:0,version:"19.1.0",rendererPackageName:"react-dom",currentDispatcherRef:R,reconcilerVersion:"19.1.0"};if("undefined"!=typeof __REACT_DEVTOOLS_GLOBAL_HOOK__){var Ld=__REACT_DEVTOOLS_GLOBAL_HOOK__;if(!Ld.isDisabled&&Ld.supportsFiber)try{se=Ld.inject(Nd),ce=Ld}catch(e){}}t.createRoot=function(e,t){if(!(n=e)||1!==n.nodeType&&9!==n.nodeType&&11!==n.nodeType)throw Error(o(299));var n,r=!1,a="",l=yi,i=vi,u=bi;return null!=t&&(!0===t.unstable_strictMode&&(r=!0),void 0!==t.identifierPrefix&&(a=t.identifierPrefix),void 0!==t.onUncaughtError&&(l=t.onUncaughtError),void 0!==t.onCaughtError&&(i=t.onCaughtError),void 0!==t.onRecoverableError&&(u=t.onRecoverableError),void 0!==t.unstable_transitionCallbacks&&t.unstable_transitionCallbacks),t=function(e,t,n,r,a,l,o,i,u,s,c,f){return e=new Yf(e,t,n,o,i,u,s,f),t=1,!0===l&&(t|=24),l=Ir(3,null,null,t),e.current=l,l.stateNode=e,(t=Aa()).refCount++,e.pooledCache=t,t.refCount++,l.memoizedState={element:r,isDehydrated:n,cache:t},tl(l),e}(e,1,!1,null,0,r,a,l,i,u,0,null),e[Re]=t.current,jc(e),new zd(t)}},338:(e,t,n)=>{"use strict";!function e(){if("undefined"!=typeof __REACT_DEVTOOLS_GLOBAL_HOOK__&&"function"==typeof __REACT_DEVTOOLS_GLOBAL_HOOK__.checkDCE)try{__REACT_DEVTOOLS_GLOBAL_HOOK__.checkDCE(e)}catch(e){console.error(e)}}(),e.exports=n(247)},477:(e,t)=>{"use strict";function n(e,t){var n=e.length;e.push(t);e:for(;0<n;){var r=n-1>>>1,a=e[r];if(!(0<l(a,t)))break e;e[r]=t,e[n]=a,n=r}}function r(e){return 0===e.length?null:e[0]}function a(e){if(0===e.length)return null;var t=e[0],n=e.pop();if(n!==t){e[0]=n;e:for(var r=0,a=e.length,o=a>>>1;r<o;){var i=2*(r+1)-1,u=e[i],s=i+1,c=e[s];if(0>l(u,n))s<a&&0>l(c,u)?(e[r]=c,e[s]=n,r=s):(e[r]=u,e[i]=n,r=i);else{if(!(s<a&&0>l(c,n)))break e;e[r]=c,e[s]=n,r=s}}}return t}function l(e,t){var n=e.sortIndex-t.sortIndex;return 0!==n?n:e.id-t.id}if(t.unstable_now=void 0,"object"==typeof performance&&"function"==typeof performance.now){var o=performance;t.unstable_now=function(){return o.now()}}else{var i=Date,u=i.now();t.unstable_now=function(){return i.now()-u}}var s=[],c=[],f=1,d=null,p=3,h=!1,m=!1,g=!1,y=!1,v="function"==typeof setTimeout?setTimeout:null,b="function"==typeof clearTimeout?clearTimeout:null,S="undefined"!=typeof setImmediate?setImmediate:null;function k(e){for(var t=r(c);null!==t;){if(null===t.callback)a(c);else{if(!(t.startTime<=e))break;a(c),t.sortIndex=t.expirationTime,n(s,t)}t=r(c)}}function w(e){if(g=!1,k(e),!m)if(null!==r(s))m=!0,E||(E=!0,x());else{var t=r(c);null!==t&&A(w,t.startTime-e)}}var x,E=!1,C=-1,z=5,P=-1;function _(){return!(!y&&t.unstable_now()-P<z)}function N(){if(y=!1,E){var e=t.unstable_now();P=e;var n=!0;try{e:{m=!1,g&&(g=!1,b(C),C=-1),h=!0;var l=p;try{t:{for(k(e),d=r(s);null!==d&&!(d.expirationTime>e&&_());){var o=d.callback;if("function"==typeof o){d.callback=null,p=d.priorityLevel;var i=o(d.expirationTime<=e);if(e=t.unstable_now(),"function"==typeof i){d.callback=i,k(e),n=!0;break t}d===r(s)&&a(s),k(e)}else a(s);d=r(s)}if(null!==d)n=!0;else{var u=r(c);null!==u&&A(w,u.startTime-e),n=!1}}break e}finally{d=null,p=l,h=!1}n=void 0}}finally{n?x():E=!1}}}if("function"==typeof S)x=function(){S(N)};else if("undefined"!=typeof MessageChannel){var L=new MessageChannel,T=L.port2;L.port1.onmessage=N,x=function(){T.postMessage(null)}}else x=function(){v(N,0)};function A(e,n){C=v(function(){e(t.unstable_now())},n)}t.unstable_IdlePriority=5,t.unstable_ImmediatePriority=1,t.unstable_LowPriority=4,t.unstable_NormalPriority=3,t.unstable_Profiling=null,t.unstable_UserBlockingPriority=2,t.unstable_cancelCallback=function(e){e.callback=null},t.unstable_forceFrameRate=function(e){0>e||125<e?console.error("forceFrameRate takes a positive int between 0 and 125, forcing frame rates higher than 125 fps is not supported"):z=0<e?Math.floor(1e3/e):5},t.unstable_getCurrentPriorityLevel=function(){return p},t.unstable_next=function(e){switch(p){case 1:case 2:case 3:var t=3;break;default:t=p}var n=p;p=t;try{return e()}finally{p=n}},t.unstable_requestPaint=function(){y=!0},t.unstable_runWithPriority=function(e,t){switch(e){case 1:case 2:case 3:case 4:case 5:break;default:e=3}var n=p;p=e;try{return t()}finally{p=n}},t.unstable_scheduleCallback=function(e,a,l){var o=t.unstable_now();switch(l="object"==typeof l&&null!==l&&"number"==typeof(l=l.delay)&&0<l?o+l:o,e){case 1:var i=-1;break;case 2:i=250;break;case 5:i=1073741823;break;case 4:i=1e4;break;default:i=5e3}return e={id:f++,callback:a,priorityLevel:e,startTime:l,expirationTime:i=l+i,sortIndex:-1},l>o?(e.sortIndex=l,n(c,e),null===r(s)&&e===r(c)&&(g?(b(C),C=-1):g=!0,A(w,l-o))):(e.sortIndex=i,n(s,e),m||h||(m=!0,E||(E=!0,x()))),e},t.unstable_shouldYield=_,t.unstable_wrapCallback=function(e){var t=p;return function(){var n=p;p=t;try{return e.apply(this,arguments)}finally{p=n}}}},540:(e,t,n)=>{"use strict";e.exports=n(869)},698:(e,t)=>{"use strict";var n=Symbol.for("react.transitional.element"),r=Symbol.for("react.fragment");function a(e,t,r){var a=null;if(void 0!==r&&(a=""+r),void 0!==t.key&&(a=""+t.key),"key"in t)for(var l in r={},t)"key"!==l&&(r[l]=t[l]);else r=t;return t=r.ref,{$$typeof:n,type:e,key:a,ref:void 0!==t?t:null,props:r}}t.Fragment=r,t.jsx=a,t.jsxs=a},833:e=>{e.exports=function(e,t,n,r){var a=n?n.call(r,e,t):void 0;if(void 0!==a)return!!a;if(e===t)return!0;if("object"!=typeof e||!e||"object"!=typeof t||!t)return!1;var l=Object.keys(e),o=Object.keys(t);if(l.length!==o.length)return!1;for(var i=Object.prototype.hasOwnProperty.bind(t),u=0;u<l.length;u++){var s=l[u];if(!i(s))return!1;var c=e[s],f=t[s];if(!1===(a=n?n.call(r,c,f,s):void 0)||void 0===a&&c!==f)return!1}return!0}},848:(e,t,n)=>{"use strict";e.exports=n(698)},869:(e,t)=>{"use strict";var n=Symbol.for("react.transitional.element"),r=Symbol.for("react.portal"),a=Symbol.for("react.fragment"),l=Symbol.for("react.strict_mode"),o=Symbol.for("react.profiler"),i=Symbol.for("react.consumer"),u=Symbol.for("react.context"),s=Symbol.for("react.forward_ref"),c=Symbol.for("react.suspense"),f=Symbol.for("react.memo"),d=Symbol.for("react.lazy"),p=Symbol.iterator,h={isMounted:function(){return!1},enqueueForceUpdate:function(){},enqueueReplaceState:function(){},enqueueSetState:function(){}},m=Object.assign,g={};function y(e,t,n){this.props=e,this.context=t,this.refs=g,this.updater=n||h}function v(){}function b(e,t,n){this.props=e,this.context=t,this.refs=g,this.updater=n||h}y.prototype.isReactComponent={},y.prototype.setState=function(e,t){if("object"!=typeof e&&"function"!=typeof e&&null!=e)throw Error("takes an object of state variables to update or a function which returns an object of state variables.");this.updater.enqueueSetState(this,e,t,"setState")},y.prototype.forceUpdate=function(e){this.updater.enqueueForceUpdate(this,e,"forceUpdate")},v.prototype=y.prototype;var S=b.prototype=new v;S.constructor=b,m(S,y.prototype),S.isPureReactComponent=!0;var k=Array.isArray,w={H:null,A:null,T:null,S:null,V:null},x=Object.prototype.hasOwnProperty;function E(e,t,r,a,l,o){return r=o.ref,{$$typeof:n,type:e,key:t,ref:void 0!==r?r:null,props:o}}function C(e){return"object"==typeof e&&null!==e&&e.$$typeof===n}var z=/\/+/g;function P(e,t){return"object"==typeof e&&null!==e&&null!=e.key?(n=""+e.key,r={"=":"=0",":":"=2"},"$"+n.replace(/[=:]/g,function(e){return r[e]})):t.toString(36);var n,r}function _(){}function N(e,t,a,l,o){var i=typeof e;"undefined"!==i&&"boolean"!==i||(e=null);var u,s,c=!1;if(null===e)c=!0;else switch(i){case"bigint":case"string":case"number":c=!0;break;case"object":switch(e.$$typeof){case n:case r:c=!0;break;case d:return N((c=e._init)(e._payload),t,a,l,o)}}if(c)return o=o(e),c=""===l?"."+P(e,0):l,k(o)?(a="",null!=c&&(a=c.replace(z,"$&/")+"/"),N(o,t,a,"",function(e){return e})):null!=o&&(C(o)&&(u=o,s=a+(null==o.key||e&&e.key===o.key?"":(""+o.key).replace(z,"$&/")+"/")+c,o=E(u.type,s,void 0,0,0,u.props)),t.push(o)),1;c=0;var f,h=""===l?".":l+":";if(k(e))for(var m=0;m<e.length;m++)c+=N(l=e[m],t,a,i=h+P(l,m),o);else if("function"==typeof(m=null===(f=e)||"object"!=typeof f?null:"function"==typeof(f=p&&f[p]||f["@@iterator"])?f:null))for(e=m.call(e),m=0;!(l=e.next()).done;)c+=N(l=l.value,t,a,i=h+P(l,m++),o);else if("object"===i){if("function"==typeof e.then)return N(function(e){switch(e.status){case"fulfilled":return e.value;case"rejected":throw e.reason;default:switch("string"==typeof e.status?e.then(_,_):(e.status="pending",e.then(function(t){"pending"===e.status&&(e.status="fulfilled",e.value=t)},function(t){"pending"===e.status&&(e.status="rejected",e.reason=t)})),e.status){case"fulfilled":return e.value;case"rejected":throw e.reason}}throw e}(e),t,a,l,o);throw t=String(e),Error("Objects are not valid as a React child (found: "+("[object Object]"===t?"object with keys {"+Object.keys(e).join(", ")+"}":t)+"). If you meant to render a collection of children, use an array instead.")}return c}function L(e,t,n){if(null==e)return e;var r=[],a=0;return N(e,r,"","",function(e){return t.call(n,e,a++)}),r}function T(e){if(-1===e._status){var t=e._result;(t=t()).then(function(t){0!==e._status&&-1!==e._status||(e._status=1,e._result=t)},function(t){0!==e._status&&-1!==e._status||(e._status=2,e._result=t)}),-1===e._status&&(e._status=0,e._result=t)}if(1===e._status)return e._result.default;throw e._result}var A="function"==typeof reportError?reportError:function(e){if("object"==typeof window&&"function"==typeof window.ErrorEvent){var t=new window.ErrorEvent("error",{bubbles:!0,cancelable:!0,message:"object"==typeof e&&null!==e&&"string"==typeof e.message?String(e.message):String(e),error:e});if(!window.dispatchEvent(t))return}else if("object"==typeof process&&"function"==typeof process.emit)return void process.emit("uncaughtException",e);console.error(e)};function R(){}t.Children={map:L,forEach:function(e,t,n){L(e,function(){t.apply(this,arguments)},n)},count:function(e){var t=0;return L(e,function(){t++}),t},toArray:function(e){return L(e,function(e){return e})||[]},only:function(e){if(!C(e))throw Error("React.Children.only expected to receive a single React element child.");return e}},t.Component=y,t.Fragment=a,t.Profiler=o,t.PureComponent=b,t.StrictMode=l,t.Suspense=c,t.__CLIENT_INTERNALS_DO_NOT_USE_OR_WARN_USERS_THEY_CANNOT_UPGRADE=w,t.__COMPILER_RUNTIME={__proto__:null,c:function(e){return w.H.useMemoCache(e)}},t.cache=function(e){return function(){return e.apply(null,arguments)}},t.cloneElement=function(e,t,n){if(null==e)throw Error("The argument must be a React element, but you passed "+e+".");var r=m({},e.props),a=e.key;if(null!=t)for(l in t.ref,void 0!==t.key&&(a=""+t.key),t)!x.call(t,l)||"key"===l||"__self"===l||"__source"===l||"ref"===l&&void 0===t.ref||(r[l]=t[l]);var l=arguments.length-2;if(1===l)r.children=n;else if(1<l){for(var o=Array(l),i=0;i<l;i++)o[i]=arguments[i+2];r.children=o}return E(e.type,a,void 0,0,0,r)},t.createContext=function(e){return(e={$$typeof:u,_currentValue:e,_currentValue2:e,_threadCount:0,Provider:null,Consumer:null}).Provider=e,e.Consumer={$$typeof:i,_context:e},e},t.createElement=function(e,t,n){var r,a={},l=null;if(null!=t)for(r in void 0!==t.key&&(l=""+t.key),t)x.call(t,r)&&"key"!==r&&"__self"!==r&&"__source"!==r&&(a[r]=t[r]);var o=arguments.length-2;if(1===o)a.children=n;else if(1<o){for(var i=Array(o),u=0;u<o;u++)i[u]=arguments[u+2];a.children=i}if(e&&e.defaultProps)for(r in o=e.defaultProps)void 0===a[r]&&(a[r]=o[r]);return E(e,l,void 0,0,0,a)},t.createRef=function(){return{current:null}},t.forwardRef=function(e){return{$$typeof:s,render:e}},t.isValidElement=C,t.lazy=function(e){return{$$typeof:d,_payload:{_status:-1,_result:e},_init:T}},t.memo=function(e,t){return{$$typeof:f,type:e,compare:void 0===t?null:t}},t.startTransition=function(e){var t=w.T,n={};w.T=n;try{var r=e(),a=w.S;null!==a&&a(n,r),"object"==typeof r&&null!==r&&"function"==typeof r.then&&r.then(R,A)}catch(e){A(e)}finally{w.T=t}},t.unstable_useCacheRefresh=function(){return w.H.useCacheRefresh()},t.use=function(e){return w.H.use(e)},t.useActionState=function(e,t,n){return w.H.useActionState(e,t,n)},t.useCallback=function(e,t){return w.H.useCallback(e,t)},t.useContext=function(e){return w.H.useContext(e)},t.useDebugValue=function(){},t.useDeferredValue=function(e,t){return w.H.useDeferredValue(e,t)},t.useEffect=function(e,t,n){var r=w.H;if("function"==typeof n)throw Error("useEffect CRUD overload is not enabled in this build of React.");return r.useEffect(e,t)},t.useId=function(){return w.H.useId()},t.useImperativeHandle=function(e,t,n){return w.H.useImperativeHandle(e,t,n)},t.useInsertionEffect=function(e,t){return w.H.useInsertionEffect(e,t)},t.useLayoutEffect=function(e,t){return w.H.useLayoutEffect(e,t)},t.useMemo=function(e,t){return w.H.useMemo(e,t)},t.useOptimistic=function(e,t){return w.H.useOptimistic(e,t)},t.useReducer=function(e,t,n){return w.H.useReducer(e,t,n)},t.useRef=function(e){return w.H.useRef(e)},t.useState=function(e){return w.H.useState(e)},t.useSyncExternalStore=function(e,t,n){return w.H.useSyncExternalStore(e,t,n)},t.useTransition=function(){return w.H.useTransition()},t.version="19.1.0"},961:(e,t,n)=>{"use strict";!function e(){if("undefined"!=typeof __REACT_DEVTOOLS_GLOBAL_HOOK__&&"function"==typeof __REACT_DEVTOOLS_GLOBAL_HOOK__.checkDCE)try{__REACT_DEVTOOLS_GLOBAL_HOOK__.checkDCE(e)}catch(e){console.error(e)}}(),e.exports=n(221)},982:(e,t,n)=>{"use strict";e.exports=n(477)}},r={};function a(e){var t=r[e];if(void 0!==t)return t.exports;var l=r[e]={exports:{}};return n[e](l,l.exports,a),l.exports}a.n=e=>{var t=e&&e.__esModule?()=>e.default:()=>e;return a.d(t,{a:t}),t},t=Object.getPrototypeOf?e=>Object.getPrototypeOf(e):e=>e.__proto__,a.t=function(n,r){if(1&r&&(n=this(n)),8&r)return n;if("object"==typeof n&&n){if(4&r&&n.__esModule)return n;if(16&r&&"function"==typeof n.then)return n}var l=Object.create(null);a.r(l);var o={};e=e||[null,t({}),t([]),t(t)];for(var i=2&r&&n;"object"==typeof i&&!~e.indexOf(i);i=t(i))Object.getOwnPropertyNames(i).forEach(e=>o[e]=()=>n[e]);return o.default=()=>n,a.d(l,o),l},a.d=(e,t)=>{for(var n in t)a.o(t,n)&&!a.o(e,n)&&Object.defineProperty(e,n,{enumerable:!0,get:t[n]})},a.o=(e,t)=>Object.prototype.hasOwnProperty.call(e,t),a.r=e=>{"undefined"!=typeof Symbol&&Symbol.toStringTag&&Object.defineProperty(e,Symbol.toStringTag,{value:"Module"}),Object.defineProperty(e,"__esModule",{value:!0})},a.nc=void 0,(()=>{"use strict";var e=a(848),t=a(338),n=a(540),r=a.t(n,2),l=function(){return l=Object.assign||function(e){for(var t,n=1,r=arguments.length;n<r;n++)for(var a in t=arguments[n])Object.prototype.hasOwnProperty.call(t,a)&&(e[a]=t[a]);return e},l.apply(this,arguments)};function o(e,t,n){if(n||2===arguments.length)for(var r,a=0,l=t.length;a<l;a++)!r&&a in t||(r||(r=Array.prototype.slice.call(t,0,a)),r[a]=t[a]);return e.concat(r||Array.prototype.slice.call(t))}Object.create,Object.create,"function"==typeof SuppressedError&&SuppressedError;var i=a(833),u=a.n(i),s="-ms-",c="-moz-",f="-webkit-",d="comm",p="rule",h="decl",m="@keyframes",g=Math.abs,y=String.fromCharCode,v=Object.assign;function b(e){return e.trim()}function S(e,t){return(e=t.exec(e))?e[0]:e}function k(e,t,n){return e.replace(t,n)}function w(e,t,n){return e.indexOf(t,n)}function x(e,t){return 0|e.charCodeAt(t)}function E(e,t,n){return e.slice(t,n)}function C(e){return e.length}function z(e){return e.length}function P(e,t){return t.push(e),e}function _(e,t){return e.filter(function(e){return!S(e,t)})}var N=1,L=1,T=0,A=0,R=0,O="";function I(e,t,n,r,a,l,o,i){return{value:e,root:t,parent:n,type:r,props:a,children:l,line:N,column:L,length:o,return:"",siblings:i}}function D(e,t){return v(I("",null,null,"",null,null,0,e.siblings),e,{length:-e.length},t)}function M(e){for(;e.root;)e=D(e.root,{children:[e]});P(e,e.siblings)}function F(){return R=A>0?x(O,--A):0,L--,10===R&&(L=1,N--),R}function j(){return R=A<T?x(O,A++):0,L++,10===R&&(L=1,N++),R}function $(){return x(O,A)}function U(){return A}function H(e,t){return E(O,e,t)}function B(e){switch(e){case 0:case 9:case 10:case 13:case 32:return 5;case 33:case 43:case 44:case 47:case 62:case 64:case 126:case 59:case 123:case 125:return 4;case 58:return 3;case 34:case 39:case 40:case 91:return 2;case 41:case 93:return 1}return 0}function V(e){return b(H(A-1,q(91===e?e+2:40===e?e+1:e)))}function W(e){for(;(R=$())&&R<33;)j();return B(e)>2||B(R)>3?"":" "}function Q(e,t){for(;--t&&j()&&!(R<48||R>102||R>57&&R<65||R>70&&R<97););return H(e,U()+(t<6&&32==$()&&32==j()))}function q(e){for(;j();)switch(R){case e:return A;case 34:case 39:34!==e&&39!==e&&q(R);break;case 40:41===e&&q(e);break;case 92:j()}return A}function G(e,t){for(;j()&&e+R!==57&&(e+R!==84||47!==$()););return"/*"+H(t,A-1)+"*"+y(47===e?e:j())}function Y(e){for(;!B($());)j();return H(e,A)}function K(e,t){for(var n="",r=0;r<e.length;r++)n+=t(e[r],r,e,t)||"";return n}function X(e,t,n,r){switch(e.type){case"@layer":if(e.children.length)break;case"@import":case h:return e.return=e.return||e.value;case d:return"";case m:return e.return=e.value+"{"+K(e.children,r)+"}";case p:if(!C(e.value=e.props.join(",")))return""}return C(n=K(e.children,r))?e.return=e.value+"{"+n+"}":""}function Z(e,t,n){switch(function(e,t){return 45^x(e,0)?(((t<<2^x(e,0))<<2^x(e,1))<<2^x(e,2))<<2^x(e,3):0}(e,t)){case 5103:return f+"print-"+e+e;case 5737:case 4201:case 3177:case 3433:case 1641:case 4457:case 2921:case 5572:case 6356:case 5844:case 3191:case 6645:case 3005:case 6391:case 5879:case 5623:case 6135:case 4599:case 4855:case 4215:case 6389:case 5109:case 5365:case 5621:case 3829:return f+e+e;case 4789:return c+e+e;case 5349:case 4246:case 4810:case 6968:case 2756:return f+e+c+e+s+e+e;case 5936:switch(x(e,t+11)){case 114:return f+e+s+k(e,/[svh]\w+-[tblr]{2}/,"tb")+e;case 108:return f+e+s+k(e,/[svh]\w+-[tblr]{2}/,"tb-rl")+e;case 45:return f+e+s+k(e,/[svh]\w+-[tblr]{2}/,"lr")+e}case 6828:case 4268:case 2903:return f+e+s+e+e;case 6165:return f+e+s+"flex-"+e+e;case 5187:return f+e+k(e,/(\w+).+(:[^]+)/,f+"box-$1$2"+s+"flex-$1$2")+e;case 5443:return f+e+s+"flex-item-"+k(e,/flex-|-self/g,"")+(S(e,/flex-|baseline/)?"":s+"grid-row-"+k(e,/flex-|-self/g,""))+e;case 4675:return f+e+s+"flex-line-pack"+k(e,/align-content|flex-|-self/g,"")+e;case 5548:return f+e+s+k(e,"shrink","negative")+e;case 5292:return f+e+s+k(e,"basis","preferred-size")+e;case 6060:return f+"box-"+k(e,"-grow","")+f+e+s+k(e,"grow","positive")+e;case 4554:return f+k(e,/([^-])(transform)/g,"$1"+f+"$2")+e;case 6187:return k(k(k(e,/(zoom-|grab)/,f+"$1"),/(image-set)/,f+"$1"),e,"")+e;case 5495:case 3959:return k(e,/(image-set\([^]*)/,f+"$1$`$1");case 4968:return k(k(e,/(.+:)(flex-)?(.*)/,f+"box-pack:$3"+s+"flex-pack:$3"),/s.+-b[^;]+/,"justify")+f+e+e;case 4200:if(!S(e,/flex-|baseline/))return s+"grid-column-align"+E(e,t)+e;break;case 2592:case 3360:return s+k(e,"template-","")+e;case 4384:case 3616:return n&&n.some(function(e,n){return t=n,S(e.props,/grid-\w+-end/)})?~w(e+(n=n[t].value),"span",0)?e:s+k(e,"-start","")+e+s+"grid-row-span:"+(~w(n,"span",0)?S(n,/\d+/):+S(n,/\d+/)-+S(e,/\d+/))+";":s+k(e,"-start","")+e;case 4896:case 4128:return n&&n.some(function(e){return S(e.props,/grid-\w+-start/)})?e:s+k(k(e,"-end","-span"),"span ","")+e;case 4095:case 3583:case 4068:case 2532:return k(e,/(.+)-inline(.+)/,f+"$1$2")+e;case 8116:case 7059:case 5753:case 5535:case 5445:case 5701:case 4933:case 4677:case 5533:case 5789:case 5021:case 4765:if(C(e)-1-t>6)switch(x(e,t+1)){case 109:if(45!==x(e,t+4))break;case 102:return k(e,/(.+:)(.+)-([^]+)/,"$1"+f+"$2-$3$1"+c+(108==x(e,t+3)?"$3":"$2-$3"))+e;case 115:return~w(e,"stretch",0)?Z(k(e,"stretch","fill-available"),t,n)+e:e}break;case 5152:case 5920:return k(e,/(.+?):(\d+)(\s*\/\s*(span)?\s*(\d+))?(.*)/,function(t,n,r,a,l,o,i){return s+n+":"+r+i+(a?s+n+"-span:"+(l?o:+o-+r)+i:"")+e});case 4949:if(121===x(e,t+6))return k(e,":",":"+f)+e;break;case 6444:switch(x(e,45===x(e,14)?18:11)){case 120:return k(e,/(.+:)([^;\s!]+)(;|(\s+)?!.+)?/,"$1"+f+(45===x(e,14)?"inline-":"")+"box$3$1"+f+"$2$3$1"+s+"$2box$3")+e;case 100:return k(e,":",":"+s)+e}break;case 5719:case 2647:case 2135:case 3927:case 2391:return k(e,"scroll-","scroll-snap-")+e}return e}function J(e,t,n,r){if(e.length>-1&&!e.return)switch(e.type){case h:return void(e.return=Z(e.value,e.length,n));case m:return K([D(e,{value:k(e.value,"@","@"+f)})],r);case p:if(e.length)return function(e,t){return e.map(t).join("")}(n=e.props,function(t){switch(S(t,r=/(::plac\w+|:read-\w+)/)){case":read-only":case":read-write":M(D(e,{props:[k(t,/:(read-\w+)/,":-moz-$1")]})),M(D(e,{props:[t]})),v(e,{props:_(n,r)});break;case"::placeholder":M(D(e,{props:[k(t,/:(plac\w+)/,":"+f+"input-$1")]})),M(D(e,{props:[k(t,/:(plac\w+)/,":-moz-$1")]})),M(D(e,{props:[k(t,/:(plac\w+)/,s+"input-$1")]})),M(D(e,{props:[t]})),v(e,{props:_(n,r)})}return""})}}function ee(e){return function(e){return O="",e}(te("",null,null,null,[""],e=function(e){return N=L=1,T=C(O=e),A=0,[]}(e),0,[0],e))}function te(e,t,n,r,a,l,o,i,u){for(var s=0,c=0,f=o,d=0,p=0,h=0,m=1,v=1,b=1,S=0,E="",z=a,_=l,N=r,L=E;v;)switch(h=S,S=j()){case 40:if(108!=h&&58==x(L,f-1)){-1!=w(L+=k(V(S),"&","&\f"),"&\f",g(s?i[s-1]:0))&&(b=-1);break}case 34:case 39:case 91:L+=V(S);break;case 9:case 10:case 13:case 32:L+=W(h);break;case 92:L+=Q(U()-1,7);continue;case 47:switch($()){case 42:case 47:P(re(G(j(),U()),t,n,u),u);break;default:L+="/"}break;case 123*m:i[s++]=C(L)*b;case 125*m:case 59:case 0:switch(S){case 0:case 125:v=0;case 59+c:-1==b&&(L=k(L,/\f/g,"")),p>0&&C(L)-f&&P(p>32?ae(L+";",r,n,f-1,u):ae(k(L," ","")+";",r,n,f-2,u),u);break;case 59:L+=";";default:if(P(N=ne(L,t,n,s,c,a,i,E,z=[],_=[],f,l),l),123===S)if(0===c)te(L,t,N,N,z,l,f,i,_);else switch(99===d&&110===x(L,3)?100:d){case 100:case 108:case 109:case 115:te(e,N,N,r&&P(ne(e,N,N,0,0,a,i,E,a,z=[],f,_),_),a,_,f,i,r?z:_);break;default:te(L,N,N,N,[""],_,0,i,_)}}s=c=p=0,m=b=1,E=L="",f=o;break;case 58:f=1+C(L),p=h;default:if(m<1)if(123==S)--m;else if(125==S&&0==m++&&125==F())continue;switch(L+=y(S),S*m){case 38:b=c>0?1:(L+="\f",-1);break;case 44:i[s++]=(C(L)-1)*b,b=1;break;case 64:45===$()&&(L+=V(j())),d=$(),c=f=C(E=L+=Y(U())),S++;break;case 45:45===h&&2==C(L)&&(m=0)}}return l}function ne(e,t,n,r,a,l,o,i,u,s,c,f){for(var d=a-1,h=0===a?l:[""],m=z(h),y=0,v=0,S=0;y<r;++y)for(var w=0,x=E(e,d+1,d=g(v=o[y])),C=e;w<m;++w)(C=b(v>0?h[w]+" "+x:k(x,/&\f/g,h[w])))&&(u[S++]=C);return I(e,t,n,0===a?p:i,u,s,c,f)}function re(e,t,n,r){return I(e,t,n,d,y(R),E(e,2,-2),0,r)}function ae(e,t,n,r,a){return I(e,t,n,h,E(e,0,r),E(e,r+1,-1),r,a)}var le={animationIterationCount:1,aspectRatio:1,borderImageOutset:1,borderImageSlice:1,borderImageWidth:1,boxFlex:1,boxFlexGroup:1,boxOrdinalGroup:1,columnCount:1,columns:1,flex:1,flexGrow:1,flexPositive:1,flexShrink:1,flexNegative:1,flexOrder:1,gridRow:1,gridRowEnd:1,gridRowSpan:1,gridRowStart:1,gridColumn:1,gridColumnEnd:1,gridColumnSpan:1,gridColumnStart:1,msGridRow:1,msGridRowSpan:1,msGridColumn:1,msGridColumnSpan:1,fontWeight:1,lineHeight:1,opacity:1,order:1,orphans:1,tabSize:1,widows:1,zIndex:1,zoom:1,WebkitLineClamp:1,fillOpacity:1,floodOpacity:1,stopOpacity:1,strokeDasharray:1,strokeDashoffset:1,strokeMiterlimit:1,strokeOpacity:1,strokeWidth:1},oe="undefined"!=typeof process&&void 0!==process.env&&(process.env.REACT_APP_SC_ATTR||process.env.SC_ATTR)||"data-styled",ie="active",ue="data-styled-version",se="6.1.19",ce="/*!sc*/\n",fe="undefined"!=typeof window&&"undefined"!=typeof document,de=Boolean("boolean"==typeof SC_DISABLE_SPEEDY?SC_DISABLE_SPEEDY:"undefined"!=typeof process&&void 0!==process.env&&void 0!==process.env.REACT_APP_SC_DISABLE_SPEEDY&&""!==process.env.REACT_APP_SC_DISABLE_SPEEDY?"false"!==process.env.REACT_APP_SC_DISABLE_SPEEDY&&process.env.REACT_APP_SC_DISABLE_SPEEDY:"undefined"!=typeof process&&void 0!==process.env&&void 0!==process.env.SC_DISABLE_SPEEDY&&""!==process.env.SC_DISABLE_SPEEDY&&"false"!==process.env.SC_DISABLE_SPEEDY&&process.env.SC_DISABLE_SPEEDY),pe=(new Set,Object.freeze([])),he=Object.freeze({});var me=new Set(["a","abbr","address","area","article","aside","audio","b","base","bdi","bdo","big","blockquote","body","br","button","canvas","caption","cite","code","col","colgroup","data","datalist","dd","del","details","dfn","dialog","div","dl","dt","em","embed","fieldset","figcaption","figure","footer","form","h1","h2","h3","h4","h5","h6","header","hgroup","hr","html","i","iframe","img","input","ins","kbd","keygen","label","legend","li","link","main","map","mark","menu","menuitem","meta","meter","nav","noscript","object","ol","optgroup","option","output","p","param","picture","pre","progress","q","rp","rt","ruby","s","samp","script","section","select","small","source","span","strong","style","sub","summary","sup","table","tbody","td","textarea","tfoot","th","thead","time","tr","track","u","ul","use","var","video","wbr","circle","clipPath","defs","ellipse","foreignObject","g","image","line","linearGradient","marker","mask","path","pattern","polygon","polyline","radialGradient","rect","stop","svg","text","tspan"]),ge=/[!"#$%&'()*+,./:;<=>?@[\\\]^`{|}~-]+/g,ye=/(^-|-$)/g;function ve(e){return e.replace(ge,"-").replace(ye,"")}var be=/(a)(d)/gi,Se=function(e){return String.fromCharCode(e+(e>25?39:97))};function ke(e){var t,n="";for(t=Math.abs(e);t>52;t=t/52|0)n=Se(t%52)+n;return(Se(t%52)+n).replace(be,"$1-$2")}var we,xe=function(e,t){for(var n=t.length;n;)e=33*e^t.charCodeAt(--n);return e},Ee=function(e){return xe(5381,e)};function Ce(e){return"string"==typeof e&&!0}var ze="function"==typeof Symbol&&Symbol.for,Pe=ze?Symbol.for("react.memo"):60115,_e=ze?Symbol.for("react.forward_ref"):60112,Ne={childContextTypes:!0,contextType:!0,contextTypes:!0,defaultProps:!0,displayName:!0,getDefaultProps:!0,getDerivedStateFromError:!0,getDerivedStateFromProps:!0,mixins:!0,propTypes:!0,type:!0},Le={name:!0,length:!0,prototype:!0,caller:!0,callee:!0,arguments:!0,arity:!0},Te={$$typeof:!0,compare:!0,defaultProps:!0,displayName:!0,propTypes:!0,type:!0},Ae=((we={})[_e]={$$typeof:!0,render:!0,defaultProps:!0,displayName:!0,propTypes:!0},we[Pe]=Te,we);function Re(e){return("type"in(t=e)&&t.type.$$typeof)===Pe?Te:"$$typeof"in e?Ae[e.$$typeof]:Ne;var t}var Oe=Object.defineProperty,Ie=Object.getOwnPropertyNames,De=Object.getOwnPropertySymbols,Me=Object.getOwnPropertyDescriptor,Fe=Object.getPrototypeOf,je=Object.prototype;function $e(e,t,n){if("string"!=typeof t){if(je){var r=Fe(t);r&&r!==je&&$e(e,r,n)}var a=Ie(t);De&&(a=a.concat(De(t)));for(var l=Re(e),o=Re(t),i=0;i<a.length;++i){var u=a[i];if(!(u in Le||n&&n[u]||o&&u in o||l&&u in l)){var s=Me(t,u);try{Oe(e,u,s)}catch(e){}}}}return e}function Ue(e){return"function"==typeof e}function He(e){return"object"==typeof e&&"styledComponentId"in e}function Be(e,t){return e&&t?"".concat(e," ").concat(t):e||t||""}function Ve(e,t){if(0===e.length)return"";for(var n=e[0],r=1;r<e.length;r++)n+=t?t+e[r]:e[r];return n}function We(e){return null!==e&&"object"==typeof e&&e.constructor.name===Object.name&&!("props"in e&&e.$$typeof)}function Qe(e,t,n){if(void 0===n&&(n=!1),!n&&!We(e)&&!Array.isArray(e))return t;if(Array.isArray(t))for(var r=0;r<t.length;r++)e[r]=Qe(e[r],t[r]);else if(We(t))for(var r in t)e[r]=Qe(e[r],t[r]);return e}function qe(e,t){Object.defineProperty(e,"toString",{value:t})}function Ge(e){for(var t=[],n=1;n<arguments.length;n++)t[n-1]=arguments[n];return new Error("An error occurred. See https://github.com/styled-components/styled-components/blob/main/packages/styled-components/src/utils/errors.md#".concat(e," for more information.").concat(t.length>0?" Args: ".concat(t.join(", ")):""))}var Ye=function(){function e(e){this.groupSizes=new Uint32Array(512),this.length=512,this.tag=e}return e.prototype.indexOfGroup=function(e){for(var t=0,n=0;n<e;n++)t+=this.groupSizes[n];return t},e.prototype.insertRules=function(e,t){if(e>=this.groupSizes.length){for(var n=this.groupSizes,r=n.length,a=r;e>=a;)if((a<<=1)<0)throw Ge(16,"".concat(e));this.groupSizes=new Uint32Array(a),this.groupSizes.set(n),this.length=a;for(var l=r;l<a;l++)this.groupSizes[l]=0}for(var o=this.indexOfGroup(e+1),i=(l=0,t.length);l<i;l++)this.tag.insertRule(o,t[l])&&(this.groupSizes[e]++,o++)},e.prototype.clearGroup=function(e){if(e<this.length){var t=this.groupSizes[e],n=this.indexOfGroup(e),r=n+t;this.groupSizes[e]=0;for(var a=n;a<r;a++)this.tag.deleteRule(n)}},e.prototype.getGroup=function(e){var t="";if(e>=this.length||0===this.groupSizes[e])return t;for(var n=this.groupSizes[e],r=this.indexOfGroup(e),a=r+n,l=r;l<a;l++)t+="".concat(this.tag.getRule(l)).concat(ce);return t},e}(),Ke=new Map,Xe=new Map,Ze=1,Je=function(e){if(Ke.has(e))return Ke.get(e);for(;Xe.has(Ze);)Ze++;var t=Ze++;return Ke.set(e,t),Xe.set(t,e),t},et=function(e,t){Ze=t+1,Ke.set(e,t),Xe.set(t,e)},tt="style[".concat(oe,"][").concat(ue,'="').concat(se,'"]'),nt=new RegExp("^".concat(oe,'\\.g(\\d+)\\[id="([\\w\\d-]+)"\\].*?"([^"]*)')),rt=function(e,t,n){for(var r,a=n.split(","),l=0,o=a.length;l<o;l++)(r=a[l])&&e.registerName(t,r)},at=function(e,t){for(var n,r=(null!==(n=t.textContent)&&void 0!==n?n:"").split(ce),a=[],l=0,o=r.length;l<o;l++){var i=r[l].trim();if(i){var u=i.match(nt);if(u){var s=0|parseInt(u[1],10),c=u[2];0!==s&&(et(c,s),rt(e,c,u[3]),e.getTag().insertRules(s,a)),a.length=0}else a.push(i)}}},lt=function(e){for(var t=document.querySelectorAll(tt),n=0,r=t.length;n<r;n++){var a=t[n];a&&a.getAttribute(oe)!==ie&&(at(e,a),a.parentNode&&a.parentNode.removeChild(a))}};function ot(){return a.nc}var it=function(e){var t=document.head,n=e||t,r=document.createElement("style"),a=function(e){var t=Array.from(e.querySelectorAll("style[".concat(oe,"]")));return t[t.length-1]}(n),l=void 0!==a?a.nextSibling:null;r.setAttribute(oe,ie),r.setAttribute(ue,se);var o=ot();return o&&r.setAttribute("nonce",o),n.insertBefore(r,l),r},ut=function(){function e(e){this.element=it(e),this.element.appendChild(document.createTextNode("")),this.sheet=function(e){if(e.sheet)return e.sheet;for(var t=document.styleSheets,n=0,r=t.length;n<r;n++){var a=t[n];if(a.ownerNode===e)return a}throw Ge(17)}(this.element),this.length=0}return e.prototype.insertRule=function(e,t){try{return this.sheet.insertRule(t,e),this.length++,!0}catch(e){return!1}},e.prototype.deleteRule=function(e){this.sheet.deleteRule(e),this.length--},e.prototype.getRule=function(e){var t=this.sheet.cssRules[e];return t&&t.cssText?t.cssText:""},e}(),st=function(){function e(e){this.element=it(e),this.nodes=this.element.childNodes,this.length=0}return e.prototype.insertRule=function(e,t){if(e<=this.length&&e>=0){var n=document.createTextNode(t);return this.element.insertBefore(n,this.nodes[e]||null),this.length++,!0}return!1},e.prototype.deleteRule=function(e){this.element.removeChild(this.nodes[e]),this.length--},e.prototype.getRule=function(e){return e<this.length?this.nodes[e].textContent:""},e}(),ct=function(){function e(e){this.rules=[],this.length=0}return e.prototype.insertRule=function(e,t){return e<=this.length&&(this.rules.splice(e,0,t),this.length++,!0)},e.prototype.deleteRule=function(e){this.rules.splice(e,1),this.length--},e.prototype.getRule=function(e){return e<this.length?this.rules[e]:""},e}(),ft=fe,dt={isServer:!fe,useCSSOMInjection:!de},pt=function(){function e(e,t,n){void 0===e&&(e=he),void 0===t&&(t={});var r=this;this.options=l(l({},dt),e),this.gs=t,this.names=new Map(n),this.server=!!e.isServer,!this.server&&fe&&ft&&(ft=!1,lt(this)),qe(this,function(){return function(e){for(var t=e.getTag(),n=t.length,r="",a=function(n){var a=function(e){return Xe.get(e)}(n);if(void 0===a)return"continue";var l=e.names.get(a),o=t.getGroup(n);if(void 0===l||!l.size||0===o.length)return"continue";var i="".concat(oe,".g").concat(n,'[id="').concat(a,'"]'),u="";void 0!==l&&l.forEach(function(e){e.length>0&&(u+="".concat(e,","))}),r+="".concat(o).concat(i,'{content:"').concat(u,'"}').concat(ce)},l=0;l<n;l++)a(l);return r}(r)})}return e.registerId=function(e){return Je(e)},e.prototype.rehydrate=function(){!this.server&&fe&&lt(this)},e.prototype.reconstructWithOptions=function(t,n){return void 0===n&&(n=!0),new e(l(l({},this.options),t),this.gs,n&&this.names||void 0)},e.prototype.allocateGSInstance=function(e){return this.gs[e]=(this.gs[e]||0)+1},e.prototype.getTag=function(){return this.tag||(this.tag=(e=function(e){var t=e.useCSSOMInjection,n=e.target;return e.isServer?new ct(n):t?new ut(n):new st(n)}(this.options),new Ye(e)));var e},e.prototype.hasNameForId=function(e,t){return this.names.has(e)&&this.names.get(e).has(t)},e.prototype.registerName=function(e,t){if(Je(e),this.names.has(e))this.names.get(e).add(t);else{var n=new Set;n.add(t),this.names.set(e,n)}},e.prototype.insertRules=function(e,t,n){this.registerName(e,t),this.getTag().insertRules(Je(e),n)},e.prototype.clearNames=function(e){this.names.has(e)&&this.names.get(e).clear()},e.prototype.clearRules=function(e){this.getTag().clearGroup(Je(e)),this.clearNames(e)},e.prototype.clearTag=function(){this.tag=void 0},e}(),ht=/&/g,mt=/^\s*\/\/.*$/gm;function gt(e,t){return e.map(function(e){return"rule"===e.type&&(e.value="".concat(t," ").concat(e.value),e.value=e.value.replaceAll(",",",".concat(t," ")),e.props=e.props.map(function(e){return"".concat(t," ").concat(e)})),Array.isArray(e.children)&&"@keyframes"!==e.type&&(e.children=gt(e.children,t)),e})}function yt(e){var t,n,r,a=void 0===e?he:e,l=a.options,o=void 0===l?he:l,i=a.plugins,u=void 0===i?pe:i,s=function(e,r,a){return a.startsWith(n)&&a.endsWith(n)&&a.replaceAll(n,"").length>0?".".concat(t):e},c=u.slice();c.push(function(e){e.type===p&&e.value.includes("&")&&(e.props[0]=e.props[0].replace(ht,n).replace(r,s))}),o.prefix&&c.push(J),c.push(X);var f=function(e,a,l,i){void 0===a&&(a=""),void 0===l&&(l=""),void 0===i&&(i="&"),t=i,n=a,r=new RegExp("\\".concat(n,"\\b"),"g");var u=e.replace(mt,""),s=ee(l||a?"".concat(l," ").concat(a," { ").concat(u," }"):u);o.namespace&&(s=gt(s,o.namespace));var f,d,p,h=[];return K(s,(f=c.concat((p=function(e){return h.push(e)},function(e){e.root||(e=e.return)&&p(e)})),d=z(f),function(e,t,n,r){for(var a="",l=0;l<d;l++)a+=f[l](e,t,n,r)||"";return a})),h};return f.hash=u.length?u.reduce(function(e,t){return t.name||Ge(15),xe(e,t.name)},5381).toString():"",f}var vt=new pt,bt=yt(),St=n.createContext({shouldForwardProp:void 0,styleSheet:vt,stylis:bt}),kt=(St.Consumer,n.createContext(void 0));function wt(){return(0,n.useContext)(St)}function xt(e){var t=(0,n.useState)(e.stylisPlugins),r=t[0],a=t[1],l=wt().styleSheet,o=(0,n.useMemo)(function(){var t=l;return e.sheet?t=e.sheet:e.target&&(t=t.reconstructWithOptions({target:e.target},!1)),e.disableCSSOMInjection&&(t=t.reconstructWithOptions({useCSSOMInjection:!1})),t},[e.disableCSSOMInjection,e.sheet,e.target,l]),i=(0,n.useMemo)(function(){return yt({options:{namespace:e.namespace,prefix:e.enableVendorPrefixes},plugins:r})},[e.enableVendorPrefixes,e.namespace,r]);(0,n.useEffect)(function(){u()(r,e.stylisPlugins)||a(e.stylisPlugins)},[e.stylisPlugins]);var s=(0,n.useMemo)(function(){return{shouldForwardProp:e.shouldForwardProp,styleSheet:o,stylis:i}},[e.shouldForwardProp,o,i]);return n.createElement(St.Provider,{value:s},n.createElement(kt.Provider,{value:i},e.children))}var Et=function(){function e(e,t){var n=this;this.inject=function(e,t){void 0===t&&(t=bt);var r=n.name+t.hash;e.hasNameForId(n.id,r)||e.insertRules(n.id,r,t(n.rules,r,"@keyframes"))},this.name=e,this.id="sc-keyframes-".concat(e),this.rules=t,qe(this,function(){throw Ge(12,String(n.name))})}return e.prototype.getName=function(e){return void 0===e&&(e=bt),this.name+e.hash},e}(),Ct=function(e){return e>="A"&&e<="Z"};function zt(e){for(var t="",n=0;n<e.length;n++){var r=e[n];if(1===n&&"-"===r&&"-"===e[0])return e;Ct(r)?t+="-"+r.toLowerCase():t+=r}return t.startsWith("ms-")?"-"+t:t}var Pt=function(e){return null==e||!1===e||""===e},_t=function(e){var t,n,r=[];for(var a in e){var l=e[a];e.hasOwnProperty(a)&&!Pt(l)&&(Array.isArray(l)&&l.isCss||Ue(l)?r.push("".concat(zt(a),":"),l,";"):We(l)?r.push.apply(r,o(o(["".concat(a," {")],_t(l),!1),["}"],!1)):r.push("".concat(zt(a),": ").concat((t=a,null==(n=l)||"boolean"==typeof n||""===n?"":"number"!=typeof n||0===n||t in le||t.startsWith("--")?String(n).trim():"".concat(n,"px")),";")))}return r};function Nt(e,t,n,r){return Pt(e)?[]:He(e)?[".".concat(e.styledComponentId)]:Ue(e)?!Ue(a=e)||a.prototype&&a.prototype.isReactComponent||!t?[e]:Nt(e(t),t,n,r):e instanceof Et?n?(e.inject(n,r),[e.getName(r)]):[e]:We(e)?_t(e):Array.isArray(e)?Array.prototype.concat.apply(pe,e.map(function(e){return Nt(e,t,n,r)})):[e.toString()];var a}function Lt(e){for(var t=0;t<e.length;t+=1){var n=e[t];if(Ue(n)&&!He(n))return!1}return!0}var Tt=Ee(se),At=function(){function e(e,t,n){this.rules=e,this.staticRulesId="",this.isStatic=(void 0===n||n.isStatic)&&Lt(e),this.componentId=t,this.baseHash=xe(Tt,t),this.baseStyle=n,pt.registerId(t)}return e.prototype.generateAndInjectStyles=function(e,t,n){var r=this.baseStyle?this.baseStyle.generateAndInjectStyles(e,t,n):"";if(this.isStatic&&!n.hash)if(this.staticRulesId&&t.hasNameForId(this.componentId,this.staticRulesId))r=Be(r,this.staticRulesId);else{var a=Ve(Nt(this.rules,e,t,n)),l=ke(xe(this.baseHash,a)>>>0);if(!t.hasNameForId(this.componentId,l)){var o=n(a,".".concat(l),void 0,this.componentId);t.insertRules(this.componentId,l,o)}r=Be(r,l),this.staticRulesId=l}else{for(var i=xe(this.baseHash,n.hash),u="",s=0;s<this.rules.length;s++){var c=this.rules[s];if("string"==typeof c)u+=c;else if(c){var f=Ve(Nt(c,e,t,n));i=xe(i,f+s),u+=f}}if(u){var d=ke(i>>>0);t.hasNameForId(this.componentId,d)||t.insertRules(this.componentId,d,n(u,".".concat(d),void 0,this.componentId)),r=Be(r,d)}}return r},e}(),Rt=n.createContext(void 0);Rt.Consumer;var Ot={};function It(e,t,r){var a=He(e),o=e,i=!Ce(e),u=t.attrs,s=void 0===u?pe:u,c=t.componentId,f=void 0===c?function(e,t){var n="string"!=typeof e?"sc":ve(e);Ot[n]=(Ot[n]||0)+1;var r="".concat(n,"-").concat(function(e){return ke(Ee(e)>>>0)}(se+n+Ot[n]));return t?"".concat(t,"-").concat(r):r}(t.displayName,t.parentComponentId):c,d=t.displayName,p=void 0===d?function(e){return Ce(e)?"styled.".concat(e):"Styled(".concat(function(e){return e.displayName||e.name||"Component"}(e),")")}(e):d,h=t.displayName&&t.componentId?"".concat(ve(t.displayName),"-").concat(t.componentId):t.componentId||f,m=a&&o.attrs?o.attrs.concat(s).filter(Boolean):s,g=t.shouldForwardProp;if(a&&o.shouldForwardProp){var y=o.shouldForwardProp;if(t.shouldForwardProp){var v=t.shouldForwardProp;g=function(e,t){return y(e,t)&&v(e,t)}}else g=y}var b=new At(r,h,a?o.componentStyle:void 0);function S(e,t){return function(e,t,r){var a=e.attrs,o=e.componentStyle,i=e.defaultProps,u=e.foldedComponentIds,s=e.styledComponentId,c=e.target,f=n.useContext(Rt),d=wt(),p=e.shouldForwardProp||d.shouldForwardProp,h=function(e,t,n){return void 0===n&&(n=he),e.theme!==n.theme&&e.theme||t||n.theme}(t,f,i)||he,m=function(e,t,n){for(var r,a=l(l({},t),{className:void 0,theme:n}),o=0;o<e.length;o+=1){var i=Ue(r=e[o])?r(a):r;for(var u in i)a[u]="className"===u?Be(a[u],i[u]):"style"===u?l(l({},a[u]),i[u]):i[u]}return t.className&&(a.className=Be(a.className,t.className)),a}(a,t,h),g=m.as||c,y={};for(var v in m)void 0===m[v]||"$"===v[0]||"as"===v||"theme"===v&&m.theme===h||("forwardedAs"===v?y.as=m.forwardedAs:p&&!p(v,g)||(y[v]=m[v]));var b=function(e,t){var n=wt();return e.generateAndInjectStyles(t,n.styleSheet,n.stylis)}(o,m),S=Be(u,s);return b&&(S+=" "+b),m.className&&(S+=" "+m.className),y[Ce(g)&&!me.has(g)?"class":"className"]=S,r&&(y.ref=r),(0,n.createElement)(g,y)}(k,e,t)}S.displayName=p;var k=n.forwardRef(S);return k.attrs=m,k.componentStyle=b,k.displayName=p,k.shouldForwardProp=g,k.foldedComponentIds=a?Be(o.foldedComponentIds,o.styledComponentId):"",k.styledComponentId=h,k.target=a?o.target:e,Object.defineProperty(k,"defaultProps",{get:function(){return this._foldedDefaultProps},set:function(e){this._foldedDefaultProps=a?function(e){for(var t=[],n=1;n<arguments.length;n++)t[n-1]=arguments[n];for(var r=0,a=t;r<a.length;r++)Qe(e,a[r],!0);return e}({},o.defaultProps,e):e}}),qe(k,function(){return".".concat(k.styledComponentId)}),i&&$e(k,e,{attrs:!0,componentStyle:!0,displayName:!0,foldedComponentIds:!0,shouldForwardProp:!0,styledComponentId:!0,target:!0}),k}function Dt(e,t){for(var n=[e[0]],r=0,a=t.length;r<a;r+=1)n.push(t[r],e[r+1]);return n}new Set;var Mt=function(e){return Object.assign(e,{isCss:!0})};function Ft(e){for(var t=[],n=1;n<arguments.length;n++)t[n-1]=arguments[n];if(Ue(e)||We(e))return Mt(Nt(Dt(pe,o([e],t,!0))));var r=e;return 0===t.length&&1===r.length&&"string"==typeof r[0]?Nt(r):Mt(Nt(Dt(r,t)))}function jt(e,t,n){if(void 0===n&&(n=he),!t)throw Ge(1,t);var r=function(r){for(var a=[],l=1;l<arguments.length;l++)a[l-1]=arguments[l];return e(t,n,Ft.apply(void 0,o([r],a,!1)))};return r.attrs=function(r){return jt(e,t,l(l({},n),{attrs:Array.prototype.concat(n.attrs,r).filter(Boolean)}))},r.withConfig=function(r){return jt(e,t,l(l({},n),r))},r}var $t=function(e){return jt(It,e)},Ut=$t;me.forEach(function(e){Ut[e]=$t(e)}),function(){function e(e,t){this.rules=e,this.componentId=t,this.isStatic=Lt(e),pt.registerId(this.componentId+1)}e.prototype.createStyles=function(e,t,n,r){var a=r(Ve(Nt(this.rules,t,n,r)),""),l=this.componentId+e;n.insertRules(l,l,a)},e.prototype.removeStyles=function(e,t){t.clearRules(this.componentId+e)},e.prototype.renderStyles=function(e,t,n,r){e>2&&pt.registerId(this.componentId+e),this.removeStyles(e,n),this.createStyles(e,t,n,r)}}(),function(){function e(){var e=this;this._emitSheetCSS=function(){var t=e.instance.toString();if(!t)return"";var n=ot(),r=Ve([n&&'nonce="'.concat(n,'"'),"".concat(oe,'="true"'),"".concat(ue,'="').concat(se,'"')].filter(Boolean)," ");return"<style ".concat(r,">").concat(t,"</style>")},this.getStyleTags=function(){if(e.sealed)throw Ge(2);return e._emitSheetCSS()},this.getStyleElement=function(){var t;if(e.sealed)throw Ge(2);var r=e.instance.toString();if(!r)return[];var a=((t={})[oe]="",t[ue]=se,t.dangerouslySetInnerHTML={__html:r},t),o=ot();return o&&(a.nonce=o),[n.createElement("style",l({},a,{key:"sc-0-0"}))]},this.seal=function(){e.sealed=!0},this.instance=new pt({isServer:!0}),this.sealed=!1}e.prototype.collectStyles=function(e){if(this.sealed)throw Ge(2);return n.createElement(xt,{sheet:this.instance},e)},e.prototype.interleaveWithNodeStream=function(e){throw Ge(3)}}(),"__sc-".concat(oe,"__");const Ht=(0,n.createContext)(null);Ht.displayName="PanelGroupContext";const Bt="data-panel-group",Vt="data-panel-group-direction",Wt="data-panel-group-id",Qt="data-panel",qt="data-panel-collapsible",Gt="data-panel-id",Yt="data-panel-size",Kt="data-resize-handle",Xt="data-resize-handle-active",Zt="data-panel-resize-handle-enabled",Jt="data-panel-resize-handle-id",en="data-resize-handle-state",tn=n.useLayoutEffect,nn=r["useId".toString()],rn="function"==typeof nn?nn:()=>null;let an=0;function ln(e=null){const t=rn(),r=(0,n.useRef)(e||t||null);return null===r.current&&(r.current=""+an++),null!=e?e:r.current}function on({children:e,className:t="",collapsedSize:r,collapsible:a,defaultSize:l,forwardedRef:o,id:i,maxSize:u,minSize:s,onCollapse:c,onExpand:f,onResize:d,order:p,style:h,tagName:m="div",...g}){const y=(0,n.useContext)(Ht);if(null===y)throw Error("Panel components must be rendered within a PanelGroup container");const{collapsePanel:v,expandPanel:b,getPanelSize:S,getPanelStyle:k,groupId:w,isPanelCollapsed:x,reevaluatePanelConstraints:E,registerPanel:C,resizePanel:z,unregisterPanel:P}=y,_=ln(i),N=(0,n.useRef)({callbacks:{onCollapse:c,onExpand:f,onResize:d},constraints:{collapsedSize:r,collapsible:a,defaultSize:l,maxSize:u,minSize:s},id:_,idIsFromProps:void 0!==i,order:p});(0,n.useRef)({didLogMissingDefaultSizeWarning:!1}),tn(()=>{const{callbacks:e,constraints:t}=N.current,n={...t};N.current.id=_,N.current.idIsFromProps=void 0!==i,N.current.order=p,e.onCollapse=c,e.onExpand=f,e.onResize=d,t.collapsedSize=r,t.collapsible=a,t.defaultSize=l,t.maxSize=u,t.minSize=s,n.collapsedSize===t.collapsedSize&&n.collapsible===t.collapsible&&n.maxSize===t.maxSize&&n.minSize===t.minSize||E(N.current,n)}),tn(()=>{const e=N.current;return C(e),()=>{P(e)}},[p,_,C,P]),(0,n.useImperativeHandle)(o,()=>({collapse:()=>{v(N.current)},expand:e=>{b(N.current,e)},getId:()=>_,getSize:()=>S(N.current),isCollapsed:()=>x(N.current),isExpanded:()=>!x(N.current),resize:e=>{z(N.current,e)}}),[v,b,S,x,_,z]);const L=k(N.current,l);return(0,n.createElement)(m,{...g,children:e,className:t,id:_,style:{...L,...h},[Wt]:w,[Qt]:"",[qt]:a||void 0,[Gt]:_,[Yt]:parseFloat(""+L.flexGrow).toFixed(1)})}const un=(0,n.forwardRef)((e,t)=>(0,n.createElement)(on,{...e,forwardedRef:t}));on.displayName="Panel",un.displayName="forwardRef(Panel)";let sn=null,cn=-1,fn=null;function dn(e,t){var n,r;const a=function(e,t){if(t){const e=0!==(t&zn),n=0!==(t&Pn);if(0!==(t&En))return e?"se-resize":n?"ne-resize":"e-resize";if(0!==(t&Cn))return e?"sw-resize":n?"nw-resize":"w-resize";if(e)return"s-resize";if(n)return"n-resize"}switch(e){case"horizontal":return"ew-resize";case"intersection":return"move";case"vertical":return"ns-resize"}}(e,t);if(sn!==a){if(sn=a,null===fn){fn=document.createElement("style");const e=void 0;e&&fn.setAttribute("nonce",e),document.head.appendChild(fn)}var l;cn>=0&&(null===(l=fn.sheet)||void 0===l||l.removeRule(cn)),cn=null!==(n=null===(r=fn.sheet)||void 0===r?void 0:r.insertRule(`*{cursor: ${a} !important;}`))&&void 0!==n?n:-1}}function pn(e){return"keydown"===e.type}function hn(e){return e.type.startsWith("pointer")}function mn(e){return e.type.startsWith("mouse")}function gn(e){if(hn(e)){if(e.isPrimary)return{x:e.clientX,y:e.clientY}}else if(mn(e))return{x:e.clientX,y:e.clientY};return{x:1/0,y:1/0}}function yn(e,t,n){return n?e.x<t.x+t.width&&e.x+e.width>t.x&&e.y<t.y+t.height&&e.y+e.height>t.y:e.x<=t.x+t.width&&e.x+e.width>=t.x&&e.y<=t.y+t.height&&e.y+e.height>=t.y}const vn=/\b(?:position|zIndex|opacity|transform|webkitTransform|mixBlendMode|filter|webkitFilter|isolation)\b/;function bn(e){const t=getComputedStyle(e);return"fixed"===t.position||!("auto"===t.zIndex||"static"===t.position&&!function(e){var t;const n=getComputedStyle(null!==(t=xn(e))&&void 0!==t?t:e).display;return"flex"===n||"inline-flex"===n}(e))||+t.opacity<1||"transform"in t&&"none"!==t.transform||"webkitTransform"in t&&"none"!==t.webkitTransform||"mixBlendMode"in t&&"normal"!==t.mixBlendMode||"filter"in t&&"none"!==t.filter||"webkitFilter"in t&&"none"!==t.webkitFilter||"isolation"in t&&"isolate"===t.isolation||!!vn.test(t.willChange)||"touch"===t.webkitOverflowScrolling}function Sn(e){let t=e.length;for(;t--;){const n=e[t];if(Bn(n,"Missing node"),bn(n))return n}return null}function kn(e){return e&&Number(getComputedStyle(e).zIndex)||0}function wn(e){const t=[];for(;e;)t.push(e),e=xn(e);return t}function xn(e){const{parentNode:t}=e;return t&&t instanceof ShadowRoot?t.host:t}const En=1,Cn=2,zn=4,Pn=8,_n="coarse"===function(){if("function"==typeof matchMedia)return matchMedia("(pointer:coarse)").matches?"coarse":"fine"}();let Nn=[],Ln=!1,Tn=new Map,An=new Map;const Rn=new Set;function On(e){const{target:t}=e,{x:n,y:r}=gn(e);Ln=!0,Fn({target:t,x:n,y:r}),Un(),Nn.length>0&&(Hn("down",e),e.preventDefault(),Mn(t)||e.stopImmediatePropagation())}function In(e){const{x:t,y:n}=gn(e);if(Ln&&0===e.buttons&&(Ln=!1,Hn("up",e)),!Ln){const{target:r}=e;Fn({target:r,x:t,y:n})}Hn("move",e),jn(),Nn.length>0&&e.preventDefault()}function Dn(e){const{target:t}=e,{x:n,y:r}=gn(e);An.clear(),Ln=!1,Nn.length>0&&(e.preventDefault(),Mn(t)||e.stopImmediatePropagation()),Hn("up",e),Fn({target:t,x:n,y:r}),jn(),Un()}function Mn(e){let t=e;for(;t;){if(t.hasAttribute(Kt))return!0;t=t.parentElement}return!1}function Fn({target:e,x:t,y:n}){Nn.splice(0);let r=null;(e instanceof HTMLElement||e instanceof SVGElement)&&(r=e),Rn.forEach(e=>{const{element:a,hitAreaMargins:l}=e,o=a.getBoundingClientRect(),{bottom:i,left:u,right:s,top:c}=o,f=_n?l.coarse:l.fine;if(t>=u-f&&t<=s+f&&n>=c-f&&n<=i+f){if(null!==r&&document.contains(r)&&a!==r&&!a.contains(r)&&!r.contains(a)&&function(e,t){if(e===t)throw new Error("Cannot compare node with itself");const n={a:wn(e),b:wn(t)};let r;for(;n.a.at(-1)===n.b.at(-1);)e=n.a.pop(),t=n.b.pop(),r=e;Bn(r,"Stacking order can only be calculated for elements with a common ancestor");const a=kn(Sn(n.a)),l=kn(Sn(n.b));if(a===l){const e=r.childNodes,t={a:n.a.at(-1),b:n.b.at(-1)};let a=e.length;for(;a--;){const n=e[a];if(n===t.a)return 1;if(n===t.b)return-1}}return Math.sign(a-l)}(r,a)>0){let e=r,t=!1;for(;e&&!e.contains(a);){if(yn(e.getBoundingClientRect(),o,!0)){t=!0;break}e=e.parentElement}if(t)return}Nn.push(e)}})}function jn(){let e=!1,t=!1;Nn.forEach(n=>{const{direction:r}=n;"horizontal"===r?e=!0:t=!0});let n=0;An.forEach(e=>{n|=e}),e&&t?dn("intersection",n):e?dn("horizontal",n):t?dn("vertical",n):null!==fn&&(document.head.removeChild(fn),sn=null,fn=null,cn=-1)}let $n;function Un(){var e;null===(e=$n)||void 0===e||e.abort(),$n=new AbortController;const t={capture:!0,signal:$n.signal};Rn.size&&(Ln?(Nn.length>0&&Tn.forEach((e,n)=>{const{body:r}=n;e>0&&(r.addEventListener("contextmenu",Dn,t),r.addEventListener("pointerleave",In,t),r.addEventListener("pointermove",In,t))}),Tn.forEach((e,n)=>{const{body:r}=n;r.addEventListener("pointerup",Dn,t),r.addEventListener("pointercancel",Dn,t)})):Tn.forEach((e,n)=>{const{body:r}=n;e>0&&(r.addEventListener("pointerdown",On,t),r.addEventListener("pointermove",In,t))}))}function Hn(e,t){Rn.forEach(n=>{const{setResizeHandlerState:r}=n,a=Nn.includes(n);r(e,a,t)})}function Bn(e,t){if(!e)throw console.error(t),Error(t)}function Vn(e,t,n=10){return e.toFixed(n)===t.toFixed(n)?0:e>t?1:-1}function Wn(e,t,n=10){return 0===Vn(e,t,n)}function Qn(e,t,n){return 0===Vn(e,t,n)}function qn({panelConstraints:e,panelIndex:t,size:n}){const r=e[t];Bn(null!=r,`Panel constraints not found for index ${t}`);let{collapsedSize:a=0,collapsible:l,maxSize:o=100,minSize:i=0}=r;return Vn(n,i)<0&&(n=l&&Vn(n,(a+i)/2)<0?a:i),n=Math.min(o,n),parseFloat(n.toFixed(10))}function Gn({delta:e,initialLayout:t,panelConstraints:n,pivotIndices:r,prevLayout:a,trigger:l}){if(Qn(e,0))return t;const o=[...t],[i,u]=r;Bn(null!=i,"Invalid first pivot index"),Bn(null!=u,"Invalid second pivot index");let s=0;if("keyboard"===l){{const r=e<0?u:i,a=n[r];Bn(a,`Panel constraints not found for index ${r}`);const{collapsedSize:l=0,collapsible:o,minSize:s=0}=a;if(o){const n=t[r];if(Bn(null!=n,`Previous layout not found for panel index ${r}`),Qn(n,l)){const t=s-n;Vn(t,Math.abs(e))>0&&(e=e<0?0-t:t)}}}{const r=e<0?i:u,a=n[r];Bn(a,`No panel constraints found for index ${r}`);const{collapsedSize:l=0,collapsible:o,minSize:s=0}=a;if(o){const n=t[r];if(Bn(null!=n,`Previous layout not found for panel index ${r}`),Qn(n,s)){const t=n-l;Vn(t,Math.abs(e))>0&&(e=e<0?0-t:t)}}}}{const r=e<0?1:-1;let a=e<0?u:i,l=0;for(;;){const e=t[a];if(Bn(null!=e,`Previous layout not found for panel index ${a}`),l+=qn({panelConstraints:n,panelIndex:a,size:100})-e,a+=r,a<0||a>=n.length)break}const o=Math.min(Math.abs(e),Math.abs(l));e=e<0?0-o:o}{let r=e<0?i:u;for(;r>=0&&r<n.length;){const a=Math.abs(e)-Math.abs(s),l=t[r];Bn(null!=l,`Previous layout not found for panel index ${r}`);const i=qn({panelConstraints:n,panelIndex:r,size:l-a});if(!Qn(l,i)&&(s+=l-i,o[r]=i,s.toPrecision(3).localeCompare(Math.abs(e).toPrecision(3),void 0,{numeric:!0})>=0))break;e<0?r--:r++}}if(function(e,t){if(e.length!==t.length)return!1;for(let n=0;n<e.length;n++)if(!Qn(e[n],t[n],undefined))return!1;return!0}(a,o))return a;{const r=e<0?u:i,a=t[r];Bn(null!=a,`Previous layout not found for panel index ${r}`);const l=a+s,c=qn({panelConstraints:n,panelIndex:r,size:l});if(o[r]=c,!Qn(c,l)){let t=l-c,r=e<0?u:i;for(;r>=0&&r<n.length;){const a=o[r];Bn(null!=a,`Previous layout not found for panel index ${r}`);const l=qn({panelConstraints:n,panelIndex:r,size:a+t});if(Qn(a,l)||(t-=l-a,o[r]=l),Qn(t,0))break;e>0?r--:r++}}}return Qn(o.reduce((e,t)=>t+e,0),100)?o:a}function Yn({layout:e,panelsArray:t,pivotIndices:n}){let r=0,a=100,l=0,o=0;const i=n[0];return Bn(null!=i,"No pivot index found"),t.forEach((e,t)=>{const{constraints:n}=e,{maxSize:u=100,minSize:s=0}=n;t===i?(r=s,a=u):(l+=s,o+=u)}),{valueMax:Math.min(a,100-l),valueMin:Math.max(r,100-o),valueNow:e[i]}}function Kn(e,t=document){return Array.from(t.querySelectorAll(`[${Jt}][data-panel-group-id="${e}"]`))}function Xn(e,t,n=document){const r=Kn(e,n).findIndex(e=>e.getAttribute(Jt)===t);return null!=r?r:null}function Zn(e,t,n){const r=Xn(e,t,n);return null!=r?[r,r+1]:[-1,-1]}function Jn(e,t=document){if(((n=t)instanceof HTMLElement||"object"==typeof n&&null!==n&&"tagName"in n&&"getAttribute"in n)&&t.dataset.panelGroupId==e)return t;var n;return t.querySelector(`[data-panel-group][data-panel-group-id="${e}"]`)||null}function er(e,t=document){return t.querySelector(`[${Jt}="${e}"]`)||null}function tr(e,t){if(e.length!==t.length)return!1;for(let n=0;n<e.length;n++)if(e[n]!==t[n])return!1;return!0}function nr(e,t){const n="horizontal"===e,{x:r,y:a}=gn(t);return n?r:a}function rr(e,t,n){t.forEach((t,r)=>{const a=e[r];Bn(a,`Panel data not found for index ${r}`);const{callbacks:l,constraints:o,id:i}=a,{collapsedSize:u=0,collapsible:s}=o,c=n[i];if(null==c||t!==c){n[i]=t;const{onCollapse:e,onExpand:r,onResize:a}=l;a&&a(t,c),s&&(e||r)&&(!r||null!=c&&!Wn(c,u)||Wn(t,u)||r(),!e||null!=c&&Wn(c,u)||!Wn(t,u)||e())}})}function ar(e,t){if(e.length!==t.length)return!1;for(let n=0;n<e.length;n++)if(e[n]!=t[n])return!1;return!0}function lr(e){try{if("undefined"==typeof localStorage)throw new Error("localStorage not supported in this environment");e.getItem=e=>localStorage.getItem(e),e.setItem=(e,t)=>{localStorage.setItem(e,t)}}catch(t){console.error(t),e.getItem=()=>null,e.setItem=()=>{}}}function or(e){return`react-resizable-panels:${e}`}function ir(e){return e.map(e=>{const{constraints:t,id:n,idIsFromProps:r,order:a}=e;return r?n:a?`${a}:${JSON.stringify(t)}`:JSON.stringify(t)}).sort((e,t)=>e.localeCompare(t)).join(",")}function ur(e,t){try{const n=or(e),r=t.getItem(n);if(r){const e=JSON.parse(r);if("object"==typeof e&&null!=e)return e}}catch(e){}return null}function sr(e,t,n,r,a){var l;const o=or(e),i=ir(t),u=null!==(l=ur(e,a))&&void 0!==l?l:{};u[i]={expandToSizes:Object.fromEntries(n.entries()),layout:r};try{a.setItem(o,JSON.stringify(u))}catch(e){console.error(e)}}function cr({layout:e,panelConstraints:t}){const n=[...e],r=n.reduce((e,t)=>e+t,0);if(n.length!==t.length)throw Error(`Invalid ${t.length} panel layout: ${n.map(e=>`${e}%`).join(", ")}`);if(!Qn(r,100)&&n.length>0)for(let e=0;e<t.length;e++){const t=n[e];Bn(null!=t,`No layout data found for index ${e}`);const a=100/r*t;n[e]=a}let a=0;for(let e=0;e<t.length;e++){const r=n[e];Bn(null!=r,`No layout data found for index ${e}`);const l=qn({panelConstraints:t,panelIndex:e,size:r});r!=l&&(a+=r-l,n[e]=l)}if(!Qn(a,0))for(let e=0;e<t.length;e++){const r=n[e];Bn(null!=r,`No layout data found for index ${e}`);const l=qn({panelConstraints:t,panelIndex:e,size:r+a});if(r!==l&&(a-=l-r,n[e]=l,Qn(a,0)))break}return n}const fr={getItem:e=>(lr(fr),fr.getItem(e)),setItem:(e,t)=>{lr(fr),fr.setItem(e,t)}},dr={};function pr({autoSaveId:e=null,children:t,className:r="",direction:a,forwardedRef:l,id:o=null,onLayout:i=null,keyboardResizeBy:u=null,storage:s=fr,style:c,tagName:f="div",...d}){const p=ln(o),h=(0,n.useRef)(null),[m,g]=(0,n.useState)(null),[y,v]=(0,n.useState)([]),b=function(){const[e,t]=(0,n.useState)(0);return(0,n.useCallback)(()=>t(e=>e+1),[])}(),S=(0,n.useRef)({}),k=(0,n.useRef)(new Map),w=(0,n.useRef)(0),x=(0,n.useRef)({autoSaveId:e,direction:a,dragState:m,id:p,keyboardResizeBy:u,onLayout:i,storage:s}),E=(0,n.useRef)({layout:y,panelDataArray:[],panelDataArrayChanged:!1});(0,n.useRef)({didLogIdAndOrderWarning:!1,didLogPanelConstraintsWarning:!1,prevPanelIds:[]}),(0,n.useImperativeHandle)(l,()=>({getId:()=>x.current.id,getLayout:()=>{const{layout:e}=E.current;return e},setLayout:e=>{const{onLayout:t}=x.current,{layout:n,panelDataArray:r}=E.current,a=cr({layout:e,panelConstraints:r.map(e=>e.constraints)});tr(n,a)||(v(a),E.current.layout=a,t&&t(a),rr(r,a,S.current))}}),[]),tn(()=>{x.current.autoSaveId=e,x.current.direction=a,x.current.dragState=m,x.current.id=p,x.current.onLayout=i,x.current.storage=s}),function({committedValuesRef:e,eagerValuesRef:t,groupId:r,layout:a,panelDataArray:l,panelGroupElement:o,setLayout:i}){(0,n.useRef)({didWarnAboutMissingResizeHandle:!1}),tn(()=>{if(!o)return;const e=Kn(r,o);for(let t=0;t<l.length-1;t++){const{valueMax:n,valueMin:r,valueNow:o}=Yn({layout:a,panelsArray:l,pivotIndices:[t,t+1]}),i=e[t];if(null==i);else{const e=l[t];Bn(e,`No panel data found for index "${t}"`),i.setAttribute("aria-controls",e.id),i.setAttribute("aria-valuemax",""+Math.round(n)),i.setAttribute("aria-valuemin",""+Math.round(r)),i.setAttribute("aria-valuenow",null!=o?""+Math.round(o):"")}}return()=>{e.forEach((e,t)=>{e.removeAttribute("aria-controls"),e.removeAttribute("aria-valuemax"),e.removeAttribute("aria-valuemin"),e.removeAttribute("aria-valuenow")})}},[r,a,l,o]),(0,n.useEffect)(()=>{if(!o)return;const e=t.current;Bn(e,"Eager values not found");const{panelDataArray:n}=e;Bn(null!=Jn(r,o),`No group found for id "${r}"`);const l=Kn(r,o);Bn(l,`No resize handles found for group id "${r}"`);const u=l.map(e=>{const t=e.getAttribute(Jt);Bn(t,"Resize handle element has no handle id attribute");const[l,u]=function(e,t,n,r=document){var a,l,o,i;const u=er(t,r),s=Kn(e,r),c=u?s.indexOf(u):-1;return[null!==(a=null===(l=n[c])||void 0===l?void 0:l.id)&&void 0!==a?a:null,null!==(o=null===(i=n[c+1])||void 0===i?void 0:i.id)&&void 0!==o?o:null]}(r,t,n,o);if(null==l||null==u)return()=>{};const s=e=>{if(!e.defaultPrevented)switch(e.key){case"Enter":{e.preventDefault();const u=n.findIndex(e=>e.id===l);if(u>=0){const e=n[u];Bn(e,`No panel data found for index ${u}`);const l=a[u],{collapsedSize:s=0,collapsible:c,minSize:f=0}=e.constraints;if(null!=l&&c){const e=Gn({delta:Qn(l,s)?f-s:s-l,initialLayout:a,panelConstraints:n.map(e=>e.constraints),pivotIndices:Zn(r,t,o),prevLayout:a,trigger:"keyboard"});a!==e&&i(e)}}break}}};return e.addEventListener("keydown",s),()=>{e.removeEventListener("keydown",s)}});return()=>{u.forEach(e=>e())}},[o,e,t,r,a,l,i])}({committedValuesRef:x,eagerValuesRef:E,groupId:p,layout:y,panelDataArray:E.current.panelDataArray,setLayout:v,panelGroupElement:h.current}),(0,n.useEffect)(()=>{const{panelDataArray:t}=E.current;if(e){if(0===y.length||y.length!==t.length)return;let n=dr[e];null==n&&(n=function(e,t=10){let n=null;return(...r)=>{null!==n&&clearTimeout(n),n=setTimeout(()=>{e(...r)},t)}}(sr,100),dr[e]=n);const r=[...t],a=new Map(k.current);n(e,r,a,y,s)}},[e,y,s]),(0,n.useEffect)(()=>{});const C=(0,n.useCallback)(e=>{const{onLayout:t}=x.current,{layout:n,panelDataArray:r}=E.current;if(e.constraints.collapsible){const a=r.map(e=>e.constraints),{collapsedSize:l=0,panelSize:o,pivotIndices:i}=gr(r,e,n);if(Bn(null!=o,`Panel size not found for panel "${e.id}"`),!Wn(o,l)){k.current.set(e.id,o);const u=Gn({delta:mr(r,e)===r.length-1?o-l:l-o,initialLayout:n,panelConstraints:a,pivotIndices:i,prevLayout:n,trigger:"imperative-api"});ar(n,u)||(v(u),E.current.layout=u,t&&t(u),rr(r,u,S.current))}}},[]),z=(0,n.useCallback)((e,t)=>{const{onLayout:n}=x.current,{layout:r,panelDataArray:a}=E.current;if(e.constraints.collapsible){const l=a.map(e=>e.constraints),{collapsedSize:o=0,panelSize:i=0,minSize:u=0,pivotIndices:s}=gr(a,e,r),c=null!=t?t:u;if(Wn(i,o)){const t=k.current.get(e.id),o=null!=t&&t>=c?t:c,u=Gn({delta:mr(a,e)===a.length-1?i-o:o-i,initialLayout:r,panelConstraints:l,pivotIndices:s,prevLayout:r,trigger:"imperative-api"});ar(r,u)||(v(u),E.current.layout=u,n&&n(u),rr(a,u,S.current))}}},[]),P=(0,n.useCallback)(e=>{const{layout:t,panelDataArray:n}=E.current,{panelSize:r}=gr(n,e,t);return Bn(null!=r,`Panel size not found for panel "${e.id}"`),r},[]),_=(0,n.useCallback)((e,t)=>{const{panelDataArray:n}=E.current,r=mr(n,e);return function({defaultSize:e,dragState:t,layout:n,panelData:r,panelIndex:a,precision:l=3}){const o=n[a];let i;return i=null==o?null!=e?e.toPrecision(l):"1":1===r.length?"1":o.toPrecision(l),{flexBasis:0,flexGrow:i,flexShrink:1,overflow:"hidden",pointerEvents:null!==t?"none":void 0}}({defaultSize:t,dragState:m,layout:y,panelData:n,panelIndex:r})},[m,y]),N=(0,n.useCallback)(e=>{const{layout:t,panelDataArray:n}=E.current,{collapsedSize:r=0,collapsible:a,panelSize:l}=gr(n,e,t);return Bn(null!=l,`Panel size not found for panel "${e.id}"`),!0===a&&Wn(l,r)},[]),L=(0,n.useCallback)(e=>{const{layout:t,panelDataArray:n}=E.current,{collapsedSize:r=0,collapsible:a,panelSize:l}=gr(n,e,t);return Bn(null!=l,`Panel size not found for panel "${e.id}"`),!a||Vn(l,r)>0},[]),T=(0,n.useCallback)(e=>{const{panelDataArray:t}=E.current;t.push(e),t.sort((e,t)=>{const n=e.order,r=t.order;return null==n&&null==r?0:null==n?-1:null==r?1:n-r}),E.current.panelDataArrayChanged=!0,b()},[b]);tn(()=>{if(E.current.panelDataArrayChanged){E.current.panelDataArrayChanged=!1;const{autoSaveId:e,onLayout:t,storage:n}=x.current,{layout:r,panelDataArray:a}=E.current;let l=null;if(e){const t=function(e,t,n){var r,a;return null!==(a=(null!==(r=ur(e,n))&&void 0!==r?r:{})[ir(t)])&&void 0!==a?a:null}(e,a,n);t&&(k.current=new Map(Object.entries(t.expandToSizes)),l=t.layout)}null==l&&(l=function({panelDataArray:e}){const t=Array(e.length),n=e.map(e=>e.constraints);let r=0,a=100;for(let l=0;l<e.length;l++){const e=n[l];Bn(e,`Panel constraints not found for index ${l}`);const{defaultSize:o}=e;null!=o&&(r++,t[l]=o,a-=o)}for(let l=0;l<e.length;l++){const o=n[l];Bn(o,`Panel constraints not found for index ${l}`);const{defaultSize:i}=o;if(null!=i)continue;const u=a/(e.length-r);r++,t[l]=u,a-=u}return t}({panelDataArray:a}));const o=cr({layout:l,panelConstraints:a.map(e=>e.constraints)});tr(r,o)||(v(o),E.current.layout=o,t&&t(o),rr(a,o,S.current))}}),tn(()=>{const e=E.current;return()=>{e.layout=[]}},[]);const A=(0,n.useCallback)(e=>{let t=!1;const n=h.current;return n&&"rtl"===window.getComputedStyle(n,null).getPropertyValue("direction")&&(t=!0),function(n){n.preventDefault();const r=h.current;if(!r)return()=>null;const{direction:a,dragState:l,id:o,keyboardResizeBy:i,onLayout:u}=x.current,{layout:s,panelDataArray:c}=E.current,{initialLayout:f}=null!=l?l:{},d=Zn(o,e,r);let p=function(e,t,n,r,a,l){if(pn(e)){const t="horizontal"===n;let r=0;r=e.shiftKey?100:null!=a?a:10;let l=0;switch(e.key){case"ArrowDown":l=t?0:r;break;case"ArrowLeft":l=t?-r:0;break;case"ArrowRight":l=t?r:0;break;case"ArrowUp":l=t?0:-r;break;case"End":l=100;break;case"Home":l=-100}return l}return null==r?0:function(e,t,n,r,a){const l="horizontal"===n,o=er(t,a);Bn(o,`No resize handle element found for id "${t}"`);const i=o.getAttribute(Wt);Bn(i,"Resize handle element has no group id attribute");let{initialCursorPosition:u}=r;const s=nr(n,e),c=Jn(i,a);Bn(c,`No group element found for id "${i}"`);const f=c.getBoundingClientRect();return(s-u)/(l?f.width:f.height)*100}(e,t,n,r,l)}(n,e,a,l,i,r);const m="horizontal"===a;m&&t&&(p=-p);const g=Gn({delta:p,initialLayout:null!=f?f:s,panelConstraints:c.map(e=>e.constraints),pivotIndices:d,prevLayout:s,trigger:pn(n)?"keyboard":"mouse-or-touch"}),y=!ar(s,g);var b,k;(hn(n)||mn(n))&&w.current!=p&&(w.current=p,b=e,k=y||0===p?0:m?p<0?En:Cn:p<0?zn:Pn,An.set(b,k)),y&&(v(g),E.current.layout=g,u&&u(g),rr(c,g,S.current))}},[]),R=(0,n.useCallback)((e,t)=>{const{onLayout:n}=x.current,{layout:r,panelDataArray:a}=E.current,l=a.map(e=>e.constraints),{panelSize:o,pivotIndices:i}=gr(a,e,r);Bn(null!=o,`Panel size not found for panel "${e.id}"`);const u=Gn({delta:mr(a,e)===a.length-1?o-t:t-o,initialLayout:r,panelConstraints:l,pivotIndices:i,prevLayout:r,trigger:"imperative-api"});ar(r,u)||(v(u),E.current.layout=u,n&&n(u),rr(a,u,S.current))},[]),O=(0,n.useCallback)((e,t)=>{const{layout:n,panelDataArray:r}=E.current,{collapsedSize:a=0,collapsible:l}=t,{collapsedSize:o=0,collapsible:i,maxSize:u=100,minSize:s=0}=e.constraints,{panelSize:c}=gr(r,e,n);null!=c&&(l&&i&&Wn(c,a)?Wn(a,o)||R(e,o):c<s?R(e,s):c>u&&R(e,u))},[R]),I=(0,n.useCallback)((e,t)=>{const{direction:n}=x.current,{layout:r}=E.current;if(!h.current)return;const a=er(e,h.current);Bn(a,`Drag handle element not found for id "${e}"`);const l=nr(n,t);g({dragHandleId:e,dragHandleRect:a.getBoundingClientRect(),initialCursorPosition:l,initialLayout:r})},[]),D=(0,n.useCallback)(()=>{g(null)},[]),M=(0,n.useCallback)(e=>{const{panelDataArray:t}=E.current,n=mr(t,e);n>=0&&(t.splice(n,1),delete S.current[e.id],E.current.panelDataArrayChanged=!0,b())},[b]),F=(0,n.useMemo)(()=>({collapsePanel:C,direction:a,dragState:m,expandPanel:z,getPanelSize:P,getPanelStyle:_,groupId:p,isPanelCollapsed:N,isPanelExpanded:L,reevaluatePanelConstraints:O,registerPanel:T,registerResizeHandle:A,resizePanel:R,startDragging:I,stopDragging:D,unregisterPanel:M,panelGroupElement:h.current}),[C,m,a,z,P,_,p,N,L,O,T,A,R,I,D,M]),j={display:"flex",flexDirection:"horizontal"===a?"row":"column",height:"100%",overflow:"hidden",width:"100%"};return(0,n.createElement)(Ht.Provider,{value:F},(0,n.createElement)(f,{...d,children:t,className:r,id:o,ref:h,style:{...j,...c},[Bt]:"",[Vt]:a,[Wt]:p}))}const hr=(0,n.forwardRef)((e,t)=>(0,n.createElement)(pr,{...e,forwardedRef:t}));function mr(e,t){return e.findIndex(e=>e===t||e.id===t.id)}function gr(e,t,n){const r=mr(e,t),a=r===e.length-1?[r-1,r]:[r,r+1],l=n[r];return{...t.constraints,panelSize:l,pivotIndices:a}}function yr({children:e=null,className:t="",disabled:r=!1,hitAreaMargins:a,id:l,onBlur:o,onClick:i,onDragging:u,onFocus:s,onPointerDown:c,onPointerUp:f,style:d={},tabIndex:p=0,tagName:h="div",...m}){var g,y;const v=(0,n.useRef)(null),b=(0,n.useRef)({onClick:i,onDragging:u,onPointerDown:c,onPointerUp:f});(0,n.useEffect)(()=>{b.current.onClick=i,b.current.onDragging=u,b.current.onPointerDown=c,b.current.onPointerUp=f});const S=(0,n.useContext)(Ht);if(null===S)throw Error("PanelResizeHandle components must be rendered within a PanelGroup container");const{direction:k,groupId:w,registerResizeHandle:x,startDragging:E,stopDragging:C,panelGroupElement:z}=S,P=ln(l),[_,N]=(0,n.useState)("inactive"),[L,T]=(0,n.useState)(!1),[A,R]=(0,n.useState)(null),O=(0,n.useRef)({state:_});tn(()=>{O.current.state=_}),(0,n.useEffect)(()=>{if(r)R(null);else{const e=x(P);R(()=>e)}},[r,P,x]);const I=null!==(g=null==a?void 0:a.coarse)&&void 0!==g?g:15,D=null!==(y=null==a?void 0:a.fine)&&void 0!==y?y:5;return(0,n.useEffect)(()=>{if(r||null==A)return;const e=v.current;Bn(e,"Element ref not attached");let t=!1;return function(e,t,n,r,a){var l;const{ownerDocument:o}=t,i={direction:n,element:t,hitAreaMargins:r,setResizeHandlerState:a},u=null!==(l=Tn.get(o))&&void 0!==l?l:0;return Tn.set(o,u+1),Rn.add(i),Un(),function(){var t;An.delete(e),Rn.delete(i);const n=null!==(t=Tn.get(o))&&void 0!==t?t:1;if(Tn.set(o,n-1),Un(),1===n&&Tn.delete(o),Nn.includes(i)){const e=Nn.indexOf(i);e>=0&&Nn.splice(e,1),jn(),a("up",!0,null)}}}(P,e,k,{coarse:I,fine:D},(e,n,r)=>{if(n)switch(e){case"down":{N("drag"),t=!1,Bn(r,'Expected event to be defined for "down" action'),E(P,r);const{onDragging:e,onPointerDown:n}=b.current;null==e||e(!0),null==n||n();break}case"move":{const{state:e}=O.current;t=!0,"drag"!==e&&N("hover"),Bn(r,'Expected event to be defined for "move" action'),A(r);break}case"up":{N("hover"),C();const{onClick:e,onDragging:n,onPointerUp:r}=b.current;null==n||n(!1),null==r||r(),t||null==e||e();break}}else N("inactive")})},[I,k,r,D,x,P,A,E,C]),function({disabled:e,handleId:t,resizeHandler:r,panelGroupElement:a}){(0,n.useEffect)(()=>{if(e||null==r||null==a)return;const n=er(t,a);if(null==n)return;const l=e=>{if(!e.defaultPrevented)switch(e.key){case"ArrowDown":case"ArrowLeft":case"ArrowRight":case"ArrowUp":case"End":case"Home":e.preventDefault(),r(e);break;case"F6":{e.preventDefault();const r=n.getAttribute(Wt);Bn(r,`No group element found for id "${r}"`);const l=Kn(r,a),o=Xn(r,t,a);Bn(null!==o,`No resize element found for id "${t}"`),l[e.shiftKey?o>0?o-1:l.length-1:o+1<l.length?o+1:0].focus();break}}};return n.addEventListener("keydown",l),()=>{n.removeEventListener("keydown",l)}},[a,e,t,r])}({disabled:r,handleId:P,resizeHandler:A,panelGroupElement:z}),(0,n.createElement)(h,{...m,children:e,className:t,id:l,onBlur:()=>{T(!1),null==o||o()},onFocus:()=>{T(!0),null==s||s()},ref:v,role:"separator",style:{touchAction:"none",userSelect:"none",...d},tabIndex:p,[Vt]:k,[Wt]:w,[Kt]:"",[Xt]:"drag"===_?"pointer":L?"keyboard":void 0,[Zt]:!r,[Jt]:P,[en]:_})}pr.displayName="PanelGroup",hr.displayName="forwardRef(PanelGroup)",yr.displayName="PanelResizeHandle";class vr{static generateId(){return Math.random().toString(36).substr(2,9)}static parseM3U(e,t,n){const r=e.split("\n").map(e=>e.trim()).filter(e=>e),a=[],l=new Set;let o=null;for(let e=0;e<r.length;e++){const t=r[e];t.startsWith("#EXTINF:")?o=this.parseExtinf(t):(t.startsWith("http://")||t.startsWith("https://"))&&o&&(o.url=t,o.id=this.generateId(),o.type=this.detectContentType(o),this.extractXtreamInfo(o),a.push(o),o.group&&l.add(o.group),o=null)}return{id:this.generateId(),name:t,filePath:n,entries:a,groups:Array.from(l),lastUpdated:new Date}}static parseExtinf(e){const t={},n=e.match(/#EXTINF:(-?\d+(?:\.\d+)?)/);t.duration=n?parseFloat(n[1]):-1;const r=this.extractAttributes(e);t.title=r["tvg-name"]||r.title||"Unknown",t.group=r["group-title"]||"Uncategorized",t.logo=r["tvg-logo"]||r.logo,t.metadata={year:r.year?parseInt(r.year):void 0,genre:r.genre,plot:r.plot,cast:r.cast,director:r.director,rating:r.rating?parseFloat(r.rating):void 0};const a=e.match(/,(.+)$/);return a&&(t.title=a[1].trim()),t}static extractAttributes(e){const t={},n=/(\w+(?:-\w+)*)="([^"]*)"/g;let r;for(;null!==(r=n.exec(e));)t[r[1]]=r[2];return t}static detectContentType(e){const t=e.url.toLowerCase(),n=e.title.toLowerCase(),r=e.group.toLowerCase();return this.isSeriesContent(t,n,r)?"series":this.isMovieContent(t,n,r)?"movie":this.isLiveContent(t,n,r)?"live":"unknown"}static isSeriesContent(e,t,n){return[/s\d+e\d+/i,/season\s*\d+/i,/episode\s*\d+/i,/\d+x\d+/,/temporada/i,/capitulo/i,/series/i,/tv.*show/i].some(n=>n.test(e)||n.test(t))||[/series/i,/tv/i,/shows/i,/temporadas/i].some(e=>e.test(n))}static isMovieContent(e,t,n){return[/movies?/i,/films?/i,/cinema/i,/peliculas?/i,/\b\d{4}\b/].some(n=>n.test(e)||n.test(t))||[/movies?/i,/films?/i,/cinema/i,/peliculas?/i].some(e=>e.test(n))}static isLiveContent(e,t,n){return[/live/i,/tv/i,/channel/i,/canal/i,/directo/i,/en.*vivo/i].some(r=>r.test(e)||r.test(t)||r.test(n))}static extractXtreamInfo(e){const t=e.url,n=t.match(/\/([^\/]+)\/([^\/]+)\/(\d+)/);if(n){const[,r,a,l]=n;"series"===e.type&&this.extractSeriesInfo(e,t)}}static extractSeriesInfo(e,t){const n=e.title.match(/[Ss](\d+)[Ee](\d+)/);n&&(e.metadata={...e.metadata,season:parseInt(n[1]),episode:parseInt(n[2])});const r=t.match(/series\/(\d+)/);r&&(e.metadata={...e.metadata,seriesId:r[1]})}static groupSeriesByShow(e){const t=new Map;return e.filter(e=>"series"===e.type&&e.metadata?.seriesId).forEach(e=>{const n=e.metadata.seriesId,r=e.metadata.season||1,a=e.metadata.episode||1;t.has(n)||t.set(n,{id:n,title:this.extractSeriesTitle(e.title),seasons:[],totalEpisodes:0,logo:e.logo,plot:e.metadata?.plot,year:e.metadata?.year,genre:e.metadata?.genre});const l=t.get(n);let o=l.seasons.find(e=>e.number===r);o||(o={number:r,episodes:[]},l.seasons.push(o)),o.episodes.push({number:a,title:e.title,url:e.url,duration:e.duration,plot:e.metadata?.plot}),l.totalEpisodes++}),t.forEach(e=>{e.seasons.sort((e,t)=>e.number-t.number),e.seasons.forEach(e=>{e.episodes.sort((e,t)=>e.number-t.number)})}),Array.from(t.values())}static extractSeriesTitle(e){return e.replace(/[Ss]\d+[Ee]\d+.*$/,"").replace(/\d+x\d+.*$/,"").replace(/Season\s*\d+.*$/i,"").replace(/Episode\s*\d+.*$/i,"").trim()}}const{ipcRenderer:br}=window.require("electron"),Sr=Ut.div`
  height: 100%;
  background: #252525;
  border-right: 1px solid #404040;
  display: flex;
  flex-direction: column;
`,kr=Ut.div`
  padding: 15px;
  background: #2d2d2d;
  border-bottom: 1px solid #404040;
`,wr=Ut.h2`
  font-size: 16px;
  font-weight: 600;
  margin: 0 0 10px 0;
  color: #ffffff;
`,xr=Ut.div`
  display: flex;
  gap: 10px;
`,Er=Ut.button`
  padding: 8px 16px;
  background: #0078d4;
  color: white;
  border: none;
  border-radius: 4px;
  cursor: pointer;
  font-size: 12px;
  transition: background-color 0.2s;

  &:hover {
    background: #106ebe;
  }

  &:disabled {
    background: #404040;
    cursor: not-allowed;
  }
`,Cr=Ut.div`
  flex: 1;
  overflow: hidden;
  display: flex;
  flex-direction: column;
`,zr=Ut.select`
  width: 100%;
  padding: 8px;
  margin-bottom: 10px;
  background: #404040;
  color: white;
  border: 1px solid #606060;
  border-radius: 4px;
  font-size: 12px;

  option {
    background: #404040;
    color: white;
  }
`,Pr=Ut.div`
  padding: 10px 15px;
  background: #2a2a2a;
  border-bottom: 1px solid #404040;
`,_r=Ut.input`
  width: 100%;
  padding: 8px;
  background: #404040;
  color: white;
  border: 1px solid #606060;
  border-radius: 4px;
  font-size: 12px;

  &::placeholder {
    color: #888;
  }
`,Nr=Ut.div`
  display: flex;
  background: #2a2a2a;
  border-bottom: 1px solid #404040;
`,Lr=Ut.button`
  flex: 1;
  padding: 10px;
  background: ${e=>e.active?"#0078d4":"transparent"};
  color: ${e=>e.active?"white":"#ccc"};
  border: none;
  cursor: pointer;
  font-size: 12px;
  transition: all 0.2s;

  &:hover {
    background: ${e=>e.active?"#106ebe":"#404040"};
  }
`,Tr=Ut.div`
  flex: 1;
  overflow-y: auto;
  padding: 10px;
`,Ar=Ut.div`
  padding: 8px;
  margin-bottom: 4px;
  background: ${e=>e.selected?"#0078d4":"#353535"};
  border-radius: 4px;
  cursor: pointer;
  transition: background-color 0.2s;
  font-size: 12px;
  line-height: 1.4;

  &:hover {
    background: ${e=>e.selected?"#106ebe":"#404040"};
  }
`,Rr=Ut.div`
  font-weight: 500;
  color: #ffffff;
  margin-bottom: 2px;
`,Or=Ut.div`
  color: #ccc;
  font-size: 11px;
`,Ir=({playlists:t,currentPlaylist:r,onPlaylistUpdate:a,onDownloadRequest:l})=>{const[o,i]=(0,n.useState)(new Set),[u,s]=(0,n.useState)(""),[c,f]=(0,n.useState)("all"),d=t.find(e=>e.id===r),p=(0,n.useCallback)(async()=>{try{const e=await br.invoke("select-m3u-file");if(e.success){const t=vr.parseM3U(e.content,e.filePath.split("\\").pop()||"Playlist",e.filePath);a(t)}}catch(e){console.error("Error loading M3U file:",e)}},[a]),h=(0,n.useCallback)(()=>{const e=prompt("Ingrese la URL de la lista M3U:");e&&fetch(e).then(e=>e.text()).then(t=>{const n=vr.parseM3U(t,"URL Playlist",e);a(n)}).catch(e=>{console.error("Error loading M3U from URL:",e),alert("Error al cargar la lista M3U desde la URL")})},[a]),m=d?.entries.filter(e=>{const t=e.title.toLowerCase().includes(u.toLowerCase())||e.group.toLowerCase().includes(u.toLowerCase()),n="all"===c||e.type===c;return t&&n})||[],g=e=>d?"all"===e?d.entries.length:d.entries.filter(t=>t.type===e).length:0;return(0,e.jsxs)(Sr,{children:[(0,e.jsxs)(kr,{children:[(0,e.jsx)(wr,{children:"Listas M3U"}),(0,e.jsxs)(xr,{children:[(0,e.jsx)(Er,{onClick:p,children:"Cargar Archivo"}),(0,e.jsx)(Er,{onClick:h,children:"Cargar URL"})]})]}),(0,e.jsxs)(Cr,{children:[t.length>0&&(0,e.jsx)("div",{style:{padding:"10px 15px"},children:(0,e.jsxs)(zr,{value:r||"",onChange:e=>{console.log("Change playlist to:",e.target.value)},children:[(0,e.jsx)("option",{value:"",children:"Seleccionar playlist..."}),t.map(t=>(0,e.jsxs)("option",{value:t.id,children:[t.name," (",t.entries.length," elementos)"]},t.id))]})}),d&&(0,e.jsxs)(e.Fragment,{children:[(0,e.jsx)(Pr,{children:(0,e.jsx)(_r,{type:"text",placeholder:"Buscar contenido...",value:u,onChange:e=>s(e.target.value)})}),(0,e.jsxs)(Nr,{children:[(0,e.jsxs)(Lr,{active:"all"===c,onClick:()=>f("all"),children:["Todos (",g("all"),")"]}),(0,e.jsxs)(Lr,{active:"movies"===c,onClick:()=>f("movies"),children:["Películas (",g("movies"),")"]}),(0,e.jsxs)(Lr,{active:"series"===c,onClick:()=>f("series"),children:["Series (",g("series"),")"]}),(0,e.jsxs)(Lr,{active:"live"===c,onClick:()=>f("live"),children:["En Vivo (",g("live"),")"]})]}),(0,e.jsx)(Tr,{children:m.map(t=>(0,e.jsxs)(Ar,{selected:o.has(t.id),onClick:e=>((e,t)=>{if(t){const t=new Set(o);t.has(e)?t.delete(e):t.add(e),i(t)}else i(new Set([e]))})(t.id,e.ctrlKey),onContextMenu:e=>((e,t)=>{e.preventDefault(),o.has(t)||i(new Set([t])),console.log("Context menu for entries:",Array.from(o))})(e,t.id),children:[(0,e.jsx)(Rr,{children:t.title}),(0,e.jsxs)(Or,{children:[t.group," • ",t.type,t.metadata?.season&&t.metadata?.episode&&` • S${t.metadata.season}E${t.metadata.episode}`]})]},t.id))})]})]})]})},Dr=Ut.div`
  height: 100%;
  background: #252525;
  display: flex;
  flex-direction: column;
`,Mr=Ut.div`
  padding: 15px;
  background: #2d2d2d;
  border-bottom: 1px solid #404040;
`,Fr=Ut.h2`
  font-size: 16px;
  font-weight: 600;
  margin: 0 0 10px 0;
  color: #ffffff;
`,jr=Ut.div`
  flex: 1;
  display: flex;
  align-items: center;
  justify-content: center;
  color: #888;
  font-size: 14px;
`,$r=({connections:t,currentConnection:n,selectedDestination:r,onConnectionUpdate:a,onDestinationSelect:l})=>(0,e.jsxs)(Dr,{children:[(0,e.jsx)(Mr,{children:(0,e.jsx)(Fr,{children:"Conexión SSH/SFTP"})}),(0,e.jsx)(jr,{children:"Panel SFTP - En desarrollo"})]}),Ur=Ut.div`
  height: 100%;
  background: #252525;
  border-top: 1px solid #404040;
  display: flex;
  flex-direction: column;
`,Hr=Ut.div`
  padding: 15px;
  background: #2d2d2d;
  border-bottom: 1px solid #404040;
`,Br=Ut.h2`
  font-size: 16px;
  font-weight: 600;
  margin: 0;
  color: #ffffff;
`,Vr=Ut.div`
  flex: 1;
  display: flex;
  align-items: center;
  justify-content: center;
  color: #888;
  font-size: 14px;
`,Wr=({downloadQueue:t,onQueueUpdate:n})=>(0,e.jsxs)(Ur,{children:[(0,e.jsx)(Hr,{children:(0,e.jsx)(Br,{children:"Cola de Descargas"})}),(0,e.jsx)(Vr,{children:"Panel de Descargas - En desarrollo"})]}),Qr=Ut.div`
  width: 100vw;
  height: 100vh;
  background: #1a1a1a;
  color: #ffffff;
  font-family: 'Segoe UI', Tahoma, Geneva, Verdana, sans-serif;
  display: flex;
  flex-direction: column;
`,qr=Ut.div`
  height: 60px;
  background: #2d2d2d;
  border-bottom: 1px solid #404040;
  display: flex;
  align-items: center;
  padding: 0 20px;
  box-shadow: 0 2px 4px rgba(0, 0, 0, 0.3);
`,Gr=Ut.h1`
  font-size: 18px;
  font-weight: 600;
  color: #ffffff;
  margin: 0;
`,Yr=Ut.div`
  flex: 1;
  display: flex;
  flex-direction: column;
`,Kr=Ut.div`
  flex: 1;
  display: flex;
`,Xr=Ut(yr)`
  width: 4px;
  background: #404040;
  cursor: col-resize;
  transition: background-color 0.2s;

  &:hover {
    background: #0078d4;
  }

  &[data-resize-handle-active] {
    background: #0078d4;
  }
`,Zr=Ut(yr)`
  height: 4px;
  background: #404040;
  cursor: row-resize;
  transition: background-color 0.2s;

  &:hover {
    background: #0078d4;
  }

  &[data-resize-handle-active] {
    background: #0078d4;
  }
`,Jr=document.getElementById("root");if(!Jr)throw new Error("Root element not found");(0,t.createRoot)(Jr).render((0,e.jsx)(()=>{const[t,r]=(0,n.useState)({m3uPlaylists:[],sshConnections:[],downloadQueue:{tasks:[],isRunning:!1,totalTasks:0,completedTasks:0,failedTasks:0}});return(0,e.jsxs)(Qr,{children:[(0,e.jsx)(qr,{children:(0,e.jsx)(Gr,{children:"M3U Import Cloud X2"})}),(0,e.jsx)(Yr,{children:(0,e.jsxs)(hr,{direction:"vertical",children:[(0,e.jsx)(un,{defaultSize:75,minSize:50,children:(0,e.jsx)(Kr,{children:(0,e.jsxs)(hr,{direction:"horizontal",children:[(0,e.jsx)(un,{defaultSize:50,minSize:30,children:(0,e.jsx)(Ir,{playlists:t.m3uPlaylists,currentPlaylist:t.currentPlaylist,onPlaylistUpdate:e=>{r(t=>({...t,m3uPlaylists:[...t.m3uPlaylists.filter(t=>t.id!==e.id),e],currentPlaylist:e.id}))},onDownloadRequest:e=>{console.log("Download requested:",e)}})}),(0,e.jsx)(Xr,{}),(0,e.jsx)(un,{defaultSize:50,minSize:30,children:(0,e.jsx)($r,{connections:t.sshConnections,currentConnection:t.currentConnection,selectedDestination:t.selectedDestination,onConnectionUpdate:e=>{r(t=>({...t,sshConnections:[...t.sshConnections.filter(t=>t.id!==e.id),e],currentConnection:e.id}))},onDestinationSelect:e=>{r(t=>({...t,selectedDestination:e}))}})})]})})}),(0,e.jsx)(Zr,{}),(0,e.jsx)(un,{defaultSize:25,minSize:15,children:(0,e.jsx)(Wr,{downloadQueue:t.downloadQueue,onQueueUpdate:e=>{r(t=>({...t,downloadQueue:e}))}})})]})})]})},{}))})()})();
//# sourceMappingURL=bundle.js.map