/*! For license information please see bundle.js.LICENSE.txt */
(()=>{var e={221:(e,t,n)=>{"use strict";var r=n(540);function a(e){var t="https://react.dev/errors/"+e;if(1<arguments.length){t+="?args[]="+encodeURIComponent(arguments[1]);for(var n=2;n<arguments.length;n++)t+="&args[]="+encodeURIComponent(arguments[n])}return"Minified React error #"+e+"; visit "+t+" for the full message or use the non-minified dev environment for full errors and additional helpful warnings."}function l(){}var o={d:{f:l,r:function(){throw Error(a(522))},D:l,C:l,L:l,m:l,X:l,S:l,M:l},p:0,findDOMNode:null},i=Symbol.for("react.portal"),s=r.__CLIENT_INTERNALS_DO_NOT_USE_OR_WARN_USERS_THEY_CANNOT_UPGRADE;function u(e,t){return"font"===e?"":"string"==typeof t?"use-credentials"===t?t:"":void 0}t.__DOM_INTERNALS_DO_NOT_USE_OR_WARN_USERS_THEY_CANNOT_UPGRADE=o,t.createPortal=function(e,t){var n=2<arguments.length&&void 0!==arguments[2]?arguments[2]:null;if(!t||1!==t.nodeType&&9!==t.nodeType&&11!==t.nodeType)throw Error(a(299));return function(e,t,n){var r=3<arguments.length&&void 0!==arguments[3]?arguments[3]:null;return{$$typeof:i,key:null==r?null:""+r,children:e,containerInfo:t,implementation:n}}(e,t,null,n)},t.flushSync=function(e){var t=s.T,n=o.p;try{if(s.T=null,o.p=2,e)return e()}finally{s.T=t,o.p=n,o.d.f()}},t.preconnect=function(e,t){"string"==typeof e&&(t=t?"string"==typeof(t=t.crossOrigin)?"use-credentials"===t?t:"":void 0:null,o.d.C(e,t))},t.prefetchDNS=function(e){"string"==typeof e&&o.d.D(e)},t.preinit=function(e,t){if("string"==typeof e&&t&&"string"==typeof t.as){var n=t.as,r=u(n,t.crossOrigin),a="string"==typeof t.integrity?t.integrity:void 0,l="string"==typeof t.fetchPriority?t.fetchPriority:void 0;"style"===n?o.d.S(e,"string"==typeof t.precedence?t.precedence:void 0,{crossOrigin:r,integrity:a,fetchPriority:l}):"script"===n&&o.d.X(e,{crossOrigin:r,integrity:a,fetchPriority:l,nonce:"string"==typeof t.nonce?t.nonce:void 0})}},t.preinitModule=function(e,t){if("string"==typeof e)if("object"==typeof t&&null!==t){if(null==t.as||"script"===t.as){var n=u(t.as,t.crossOrigin);o.d.M(e,{crossOrigin:n,integrity:"string"==typeof t.integrity?t.integrity:void 0,nonce:"string"==typeof t.nonce?t.nonce:void 0})}}else null==t&&o.d.M(e)},t.preload=function(e,t){if("string"==typeof e&&"object"==typeof t&&null!==t&&"string"==typeof t.as){var n=t.as,r=u(n,t.crossOrigin);o.d.L(e,n,{crossOrigin:r,integrity:"string"==typeof t.integrity?t.integrity:void 0,nonce:"string"==typeof t.nonce?t.nonce:void 0,type:"string"==typeof t.type?t.type:void 0,fetchPriority:"string"==typeof t.fetchPriority?t.fetchPriority:void 0,referrerPolicy:"string"==typeof t.referrerPolicy?t.referrerPolicy:void 0,imageSrcSet:"string"==typeof t.imageSrcSet?t.imageSrcSet:void 0,imageSizes:"string"==typeof t.imageSizes?t.imageSizes:void 0,media:"string"==typeof t.media?t.media:void 0})}},t.preloadModule=function(e,t){if("string"==typeof e)if(t){var n=u(t.as,t.crossOrigin);o.d.m(e,{as:"string"==typeof t.as&&"script"!==t.as?t.as:void 0,crossOrigin:n,integrity:"string"==typeof t.integrity?t.integrity:void 0})}else o.d.m(e)},t.requestFormReset=function(e){o.d.r(e)},t.unstable_batchedUpdates=function(e,t){return e(t)},t.useFormState=function(e,t,n){return s.H.useFormState(e,t,n)},t.useFormStatus=function(){return s.H.useHostTransitionStatus()},t.version="19.1.0"},247:(e,t,n)=>{"use strict";var r=n(982),a=n(540),l=n(961);function o(e){var t="https://react.dev/errors/"+e;if(1<arguments.length){t+="?args[]="+encodeURIComponent(arguments[1]);for(var n=2;n<arguments.length;n++)t+="&args[]="+encodeURIComponent(arguments[n])}return"Minified React error #"+e+"; visit "+t+" for the full message or use the non-minified dev environment for full errors and additional helpful warnings."}function i(e){var t=e,n=e;if(e.alternate)for(;t.return;)t=t.return;else{e=t;do{!!(4098&(t=e).flags)&&(n=t.return),e=t.return}while(e)}return 3===t.tag?n:null}function s(e){if(13===e.tag){var t=e.memoizedState;if(null===t&&null!==(e=e.alternate)&&(t=e.memoizedState),null!==t)return t.dehydrated}return null}function u(e){if(i(e)!==e)throw Error(o(188))}function c(e){var t=e.tag;if(5===t||26===t||27===t||6===t)return e;for(e=e.child;null!==e;){if(null!==(t=c(e)))return t;e=e.sibling}return null}var d=Object.assign,f=Symbol.for("react.element"),p=Symbol.for("react.transitional.element"),h=Symbol.for("react.portal"),m=Symbol.for("react.fragment"),g=Symbol.for("react.strict_mode"),y=Symbol.for("react.profiler"),v=Symbol.for("react.provider"),b=Symbol.for("react.consumer"),w=Symbol.for("react.context"),k=Symbol.for("react.forward_ref"),S=Symbol.for("react.suspense"),x=Symbol.for("react.suspense_list"),C=Symbol.for("react.memo"),E=Symbol.for("react.lazy");Symbol.for("react.scope");var P=Symbol.for("react.activity");Symbol.for("react.legacy_hidden"),Symbol.for("react.tracing_marker");var z=Symbol.for("react.memo_cache_sentinel");Symbol.for("react.view_transition");var _=Symbol.iterator;function N(e){return null===e||"object"!=typeof e?null:"function"==typeof(e=_&&e[_]||e["@@iterator"])?e:null}var T=Symbol.for("react.client.reference");function L(e){if(null==e)return null;if("function"==typeof e)return e.$$typeof===T?null:e.displayName||e.name||null;if("string"==typeof e)return e;switch(e){case m:return"Fragment";case y:return"Profiler";case g:return"StrictMode";case S:return"Suspense";case x:return"SuspenseList";case P:return"Activity"}if("object"==typeof e)switch(e.$$typeof){case h:return"Portal";case w:return(e.displayName||"Context")+".Provider";case b:return(e._context.displayName||"Context")+".Consumer";case k:var t=e.render;return(e=e.displayName)||(e=""!==(e=t.displayName||t.name||"")?"ForwardRef("+e+")":"ForwardRef"),e;case C:return null!==(t=e.displayName||null)?t:L(e.type)||"Memo";case E:t=e._payload,e=e._init;try{return L(e(t))}catch(e){}}return null}var O=Array.isArray,j=a.__CLIENT_INTERNALS_DO_NOT_USE_OR_WARN_USERS_THEY_CANNOT_UPGRADE,R=l.__DOM_INTERNALS_DO_NOT_USE_OR_WARN_USERS_THEY_CANNOT_UPGRADE,A={pending:!1,data:null,method:null,action:null},D=[],I=-1;function F(e){return{current:e}}function M(e){0>I||(e.current=D[I],D[I]=null,I--)}function $(e,t){I++,D[I]=e.current,e.current=t}var U=F(null),H=F(null),B=F(null),V=F(null);function W(e,t){switch($(B,t),$(H,e),$(U,null),t.nodeType){case 9:case 11:e=(e=t.documentElement)&&(e=e.namespaceURI)?rd(e):0;break;default:if(e=t.tagName,t=t.namespaceURI)e=ad(t=rd(t),e);else switch(e){case"svg":e=1;break;case"math":e=2;break;default:e=0}}M(U),$(U,e)}function Q(){M(U),M(H),M(B)}function q(e){null!==e.memoizedState&&$(V,e);var t=U.current,n=ad(t,e.type);t!==n&&($(H,e),$(U,n))}function G(e){H.current===e&&(M(U),M(H)),V.current===e&&(M(V),qd._currentValue=A)}var Y=Object.prototype.hasOwnProperty,K=r.unstable_scheduleCallback,X=r.unstable_cancelCallback,Z=r.unstable_shouldYield,J=r.unstable_requestPaint,ee=r.unstable_now,te=r.unstable_getCurrentPriorityLevel,ne=r.unstable_ImmediatePriority,re=r.unstable_UserBlockingPriority,ae=r.unstable_NormalPriority,le=r.unstable_LowPriority,oe=r.unstable_IdlePriority,ie=r.log,se=r.unstable_setDisableYieldValue,ue=null,ce=null;function de(e){if("function"==typeof ie&&se(e),ce&&"function"==typeof ce.setStrictMode)try{ce.setStrictMode(ue,e)}catch(e){}}var fe=Math.clz32?Math.clz32:function(e){return 0==(e>>>=0)?32:31-(pe(e)/he|0)|0},pe=Math.log,he=Math.LN2,me=256,ge=4194304;function ye(e){var t=42&e;if(0!==t)return t;switch(e&-e){case 1:return 1;case 2:return 2;case 4:return 4;case 8:return 8;case 16:return 16;case 32:return 32;case 64:return 64;case 128:return 128;case 256:case 512:case 1024:case 2048:case 4096:case 8192:case 16384:case 32768:case 65536:case 131072:case 262144:case 524288:case 1048576:case 2097152:return 4194048&e;case 4194304:case 8388608:case 16777216:case 33554432:return 62914560&e;case 67108864:return 67108864;case 134217728:return 134217728;case 268435456:return 268435456;case 536870912:return 536870912;case 1073741824:return 0;default:return e}}function ve(e,t,n){var r=e.pendingLanes;if(0===r)return 0;var a=0,l=e.suspendedLanes,o=e.pingedLanes;e=e.warmLanes;var i=134217727&r;return 0!==i?0!==(r=i&~l)?a=ye(r):0!==(o&=i)?a=ye(o):n||0!==(n=i&~e)&&(a=ye(n)):0!==(i=r&~l)?a=ye(i):0!==o?a=ye(o):n||0!==(n=r&~e)&&(a=ye(n)),0===a?0:0!==t&&t!==a&&0===(t&l)&&((l=a&-a)>=(n=t&-t)||32===l&&4194048&n)?t:a}function be(e,t){return 0===(e.pendingLanes&~(e.suspendedLanes&~e.pingedLanes)&t)}function we(e,t){switch(e){case 1:case 2:case 4:case 8:case 64:return t+250;case 16:case 32:case 128:case 256:case 512:case 1024:case 2048:case 4096:case 8192:case 16384:case 32768:case 65536:case 131072:case 262144:case 524288:case 1048576:case 2097152:return t+5e3;default:return-1}}function ke(){var e=me;return!(4194048&(me<<=1))&&(me=256),e}function Se(){var e=ge;return!(62914560&(ge<<=1))&&(ge=4194304),e}function xe(e){for(var t=[],n=0;31>n;n++)t.push(e);return t}function Ce(e,t){e.pendingLanes|=t,268435456!==t&&(e.suspendedLanes=0,e.pingedLanes=0,e.warmLanes=0)}function Ee(e,t,n){e.pendingLanes|=t,e.suspendedLanes&=~t;var r=31-fe(t);e.entangledLanes|=t,e.entanglements[r]=1073741824|e.entanglements[r]|4194090&n}function Pe(e,t){var n=e.entangledLanes|=t;for(e=e.entanglements;n;){var r=31-fe(n),a=1<<r;a&t|e[r]&t&&(e[r]|=t),n&=~a}}function ze(e){switch(e){case 2:e=1;break;case 8:e=4;break;case 32:e=16;break;case 256:case 512:case 1024:case 2048:case 4096:case 8192:case 16384:case 32768:case 65536:case 131072:case 262144:case 524288:case 1048576:case 2097152:case 4194304:case 8388608:case 16777216:case 33554432:e=128;break;case 268435456:e=134217728;break;default:e=0}return e}function _e(e){return 2<(e&=-e)?8<e?134217727&e?32:268435456:8:2}function Ne(){var e=R.p;return 0!==e?e:void 0===(e=window.event)?32:of(e.type)}var Te=Math.random().toString(36).slice(2),Le="__reactFiber$"+Te,Oe="__reactProps$"+Te,je="__reactContainer$"+Te,Re="__reactEvents$"+Te,Ae="__reactListeners$"+Te,De="__reactHandles$"+Te,Ie="__reactResources$"+Te,Fe="__reactMarker$"+Te;function Me(e){delete e[Le],delete e[Oe],delete e[Re],delete e[Ae],delete e[De]}function $e(e){var t=e[Le];if(t)return t;for(var n=e.parentNode;n;){if(t=n[je]||n[Le]){if(n=t.alternate,null!==t.child||null!==n&&null!==n.child)for(e=vd(e);null!==e;){if(n=e[Le])return n;e=vd(e)}return t}n=(e=n).parentNode}return null}function Ue(e){if(e=e[Le]||e[je]){var t=e.tag;if(5===t||6===t||13===t||26===t||27===t||3===t)return e}return null}function He(e){var t=e.tag;if(5===t||26===t||27===t||6===t)return e.stateNode;throw Error(o(33))}function Be(e){var t=e[Ie];return t||(t=e[Ie]={hoistableStyles:new Map,hoistableScripts:new Map}),t}function Ve(e){e[Fe]=!0}var We=new Set,Qe={};function qe(e,t){Ge(e,t),Ge(e+"Capture",t)}function Ge(e,t){for(Qe[e]=t,e=0;e<t.length;e++)We.add(t[e])}var Ye,Ke,Xe=RegExp("^[:A-Z_a-z\\u00C0-\\u00D6\\u00D8-\\u00F6\\u00F8-\\u02FF\\u0370-\\u037D\\u037F-\\u1FFF\\u200C-\\u200D\\u2070-\\u218F\\u2C00-\\u2FEF\\u3001-\\uD7FF\\uF900-\\uFDCF\\uFDF0-\\uFFFD][:A-Z_a-z\\u00C0-\\u00D6\\u00D8-\\u00F6\\u00F8-\\u02FF\\u0370-\\u037D\\u037F-\\u1FFF\\u200C-\\u200D\\u2070-\\u218F\\u2C00-\\u2FEF\\u3001-\\uD7FF\\uF900-\\uFDCF\\uFDF0-\\uFFFD\\-.0-9\\u00B7\\u0300-\\u036F\\u203F-\\u2040]*$"),Ze={},Je={};function et(e,t,n){if(a=t,Y.call(Je,a)||!Y.call(Ze,a)&&(Xe.test(a)?Je[a]=!0:(Ze[a]=!0,0)))if(null===n)e.removeAttribute(t);else{switch(typeof n){case"undefined":case"function":case"symbol":return void e.removeAttribute(t);case"boolean":var r=t.toLowerCase().slice(0,5);if("data-"!==r&&"aria-"!==r)return void e.removeAttribute(t)}e.setAttribute(t,""+n)}var a}function tt(e,t,n){if(null===n)e.removeAttribute(t);else{switch(typeof n){case"undefined":case"function":case"symbol":case"boolean":return void e.removeAttribute(t)}e.setAttribute(t,""+n)}}function nt(e,t,n,r){if(null===r)e.removeAttribute(n);else{switch(typeof r){case"undefined":case"function":case"symbol":case"boolean":return void e.removeAttribute(n)}e.setAttributeNS(t,n,""+r)}}function rt(e){if(void 0===Ye)try{throw Error()}catch(e){var t=e.stack.trim().match(/\n( *(at )?)/);Ye=t&&t[1]||"",Ke=-1<e.stack.indexOf("\n    at")?" (<anonymous>)":-1<e.stack.indexOf("@")?"@unknown:0:0":""}return"\n"+Ye+e+Ke}var at=!1;function lt(e,t){if(!e||at)return"";at=!0;var n=Error.prepareStackTrace;Error.prepareStackTrace=void 0;try{var r={DetermineComponentFrameRoot:function(){try{if(t){var n=function(){throw Error()};if(Object.defineProperty(n.prototype,"props",{set:function(){throw Error()}}),"object"==typeof Reflect&&Reflect.construct){try{Reflect.construct(n,[])}catch(e){var r=e}Reflect.construct(e,[],n)}else{try{n.call()}catch(e){r=e}e.call(n.prototype)}}else{try{throw Error()}catch(e){r=e}(n=e())&&"function"==typeof n.catch&&n.catch(function(){})}}catch(e){if(e&&r&&"string"==typeof e.stack)return[e.stack,r.stack]}return[null,null]}};r.DetermineComponentFrameRoot.displayName="DetermineComponentFrameRoot";var a=Object.getOwnPropertyDescriptor(r.DetermineComponentFrameRoot,"name");a&&a.configurable&&Object.defineProperty(r.DetermineComponentFrameRoot,"name",{value:"DetermineComponentFrameRoot"});var l=r.DetermineComponentFrameRoot(),o=l[0],i=l[1];if(o&&i){var s=o.split("\n"),u=i.split("\n");for(a=r=0;r<s.length&&!s[r].includes("DetermineComponentFrameRoot");)r++;for(;a<u.length&&!u[a].includes("DetermineComponentFrameRoot");)a++;if(r===s.length||a===u.length)for(r=s.length-1,a=u.length-1;1<=r&&0<=a&&s[r]!==u[a];)a--;for(;1<=r&&0<=a;r--,a--)if(s[r]!==u[a]){if(1!==r||1!==a)do{if(r--,0>--a||s[r]!==u[a]){var c="\n"+s[r].replace(" at new "," at ");return e.displayName&&c.includes("<anonymous>")&&(c=c.replace("<anonymous>",e.displayName)),c}}while(1<=r&&0<=a);break}}}finally{at=!1,Error.prepareStackTrace=n}return(n=e?e.displayName||e.name:"")?rt(n):""}function ot(e){switch(e.tag){case 26:case 27:case 5:return rt(e.type);case 16:return rt("Lazy");case 13:return rt("Suspense");case 19:return rt("SuspenseList");case 0:case 15:return lt(e.type,!1);case 11:return lt(e.type.render,!1);case 1:return lt(e.type,!0);case 31:return rt("Activity");default:return""}}function it(e){try{var t="";do{t+=ot(e),e=e.return}while(e);return t}catch(e){return"\nError generating stack: "+e.message+"\n"+e.stack}}function st(e){switch(typeof e){case"bigint":case"boolean":case"number":case"string":case"undefined":case"object":return e;default:return""}}function ut(e){var t=e.type;return(e=e.nodeName)&&"input"===e.toLowerCase()&&("checkbox"===t||"radio"===t)}function ct(e){e._valueTracker||(e._valueTracker=function(e){var t=ut(e)?"checked":"value",n=Object.getOwnPropertyDescriptor(e.constructor.prototype,t),r=""+e[t];if(!e.hasOwnProperty(t)&&void 0!==n&&"function"==typeof n.get&&"function"==typeof n.set){var a=n.get,l=n.set;return Object.defineProperty(e,t,{configurable:!0,get:function(){return a.call(this)},set:function(e){r=""+e,l.call(this,e)}}),Object.defineProperty(e,t,{enumerable:n.enumerable}),{getValue:function(){return r},setValue:function(e){r=""+e},stopTracking:function(){e._valueTracker=null,delete e[t]}}}}(e))}function dt(e){if(!e)return!1;var t=e._valueTracker;if(!t)return!0;var n=t.getValue(),r="";return e&&(r=ut(e)?e.checked?"true":"false":e.value),(e=r)!==n&&(t.setValue(e),!0)}function ft(e){if(void 0===(e=e||("undefined"!=typeof document?document:void 0)))return null;try{return e.activeElement||e.body}catch(t){return e.body}}var pt=/[\n"\\]/g;function ht(e){return e.replace(pt,function(e){return"\\"+e.charCodeAt(0).toString(16)+" "})}function mt(e,t,n,r,a,l,o,i){e.name="",null!=o&&"function"!=typeof o&&"symbol"!=typeof o&&"boolean"!=typeof o?e.type=o:e.removeAttribute("type"),null!=t?"number"===o?(0===t&&""===e.value||e.value!=t)&&(e.value=""+st(t)):e.value!==""+st(t)&&(e.value=""+st(t)):"submit"!==o&&"reset"!==o||e.removeAttribute("value"),null!=t?yt(e,o,st(t)):null!=n?yt(e,o,st(n)):null!=r&&e.removeAttribute("value"),null==a&&null!=l&&(e.defaultChecked=!!l),null!=a&&(e.checked=a&&"function"!=typeof a&&"symbol"!=typeof a),null!=i&&"function"!=typeof i&&"symbol"!=typeof i&&"boolean"!=typeof i?e.name=""+st(i):e.removeAttribute("name")}function gt(e,t,n,r,a,l,o,i){if(null!=l&&"function"!=typeof l&&"symbol"!=typeof l&&"boolean"!=typeof l&&(e.type=l),null!=t||null!=n){if(("submit"===l||"reset"===l)&&null==t)return;n=null!=n?""+st(n):"",t=null!=t?""+st(t):n,i||t===e.value||(e.value=t),e.defaultValue=t}r="function"!=typeof(r=null!=r?r:a)&&"symbol"!=typeof r&&!!r,e.checked=i?e.checked:!!r,e.defaultChecked=!!r,null!=o&&"function"!=typeof o&&"symbol"!=typeof o&&"boolean"!=typeof o&&(e.name=o)}function yt(e,t,n){"number"===t&&ft(e.ownerDocument)===e||e.defaultValue===""+n||(e.defaultValue=""+n)}function vt(e,t,n,r){if(e=e.options,t){t={};for(var a=0;a<n.length;a++)t["$"+n[a]]=!0;for(n=0;n<e.length;n++)a=t.hasOwnProperty("$"+e[n].value),e[n].selected!==a&&(e[n].selected=a),a&&r&&(e[n].defaultSelected=!0)}else{for(n=""+st(n),t=null,a=0;a<e.length;a++){if(e[a].value===n)return e[a].selected=!0,void(r&&(e[a].defaultSelected=!0));null!==t||e[a].disabled||(t=e[a])}null!==t&&(t.selected=!0)}}function bt(e,t,n){null==t||((t=""+st(t))!==e.value&&(e.value=t),null!=n)?e.defaultValue=null!=n?""+st(n):"":e.defaultValue!==t&&(e.defaultValue=t)}function wt(e,t,n,r){if(null==t){if(null!=r){if(null!=n)throw Error(o(92));if(O(r)){if(1<r.length)throw Error(o(93));r=r[0]}n=r}null==n&&(n=""),t=n}n=st(t),e.defaultValue=n,(r=e.textContent)===n&&""!==r&&null!==r&&(e.value=r)}function kt(e,t){if(t){var n=e.firstChild;if(n&&n===e.lastChild&&3===n.nodeType)return void(n.nodeValue=t)}e.textContent=t}var St=new Set("animationIterationCount aspectRatio borderImageOutset borderImageSlice borderImageWidth boxFlex boxFlexGroup boxOrdinalGroup columnCount columns flex flexGrow flexPositive flexShrink flexNegative flexOrder gridArea gridRow gridRowEnd gridRowSpan gridRowStart gridColumn gridColumnEnd gridColumnSpan gridColumnStart fontWeight lineClamp lineHeight opacity order orphans scale tabSize widows zIndex zoom fillOpacity floodOpacity stopOpacity strokeDasharray strokeDashoffset strokeMiterlimit strokeOpacity strokeWidth MozAnimationIterationCount MozBoxFlex MozBoxFlexGroup MozLineClamp msAnimationIterationCount msFlex msZoom msFlexGrow msFlexNegative msFlexOrder msFlexPositive msFlexShrink msGridColumn msGridColumnSpan msGridRow msGridRowSpan WebkitAnimationIterationCount WebkitBoxFlex WebKitBoxFlexGroup WebkitBoxOrdinalGroup WebkitColumnCount WebkitColumns WebkitFlex WebkitFlexGrow WebkitFlexPositive WebkitFlexShrink WebkitLineClamp".split(" "));function xt(e,t,n){var r=0===t.indexOf("--");null==n||"boolean"==typeof n||""===n?r?e.setProperty(t,""):"float"===t?e.cssFloat="":e[t]="":r?e.setProperty(t,n):"number"!=typeof n||0===n||St.has(t)?"float"===t?e.cssFloat=n:e[t]=(""+n).trim():e[t]=n+"px"}function Ct(e,t,n){if(null!=t&&"object"!=typeof t)throw Error(o(62));if(e=e.style,null!=n){for(var r in n)!n.hasOwnProperty(r)||null!=t&&t.hasOwnProperty(r)||(0===r.indexOf("--")?e.setProperty(r,""):"float"===r?e.cssFloat="":e[r]="");for(var a in t)r=t[a],t.hasOwnProperty(a)&&n[a]!==r&&xt(e,a,r)}else for(var l in t)t.hasOwnProperty(l)&&xt(e,l,t[l])}function Et(e){if(-1===e.indexOf("-"))return!1;switch(e){case"annotation-xml":case"color-profile":case"font-face":case"font-face-src":case"font-face-uri":case"font-face-format":case"font-face-name":case"missing-glyph":return!1;default:return!0}}var Pt=new Map([["acceptCharset","accept-charset"],["htmlFor","for"],["httpEquiv","http-equiv"],["crossOrigin","crossorigin"],["accentHeight","accent-height"],["alignmentBaseline","alignment-baseline"],["arabicForm","arabic-form"],["baselineShift","baseline-shift"],["capHeight","cap-height"],["clipPath","clip-path"],["clipRule","clip-rule"],["colorInterpolation","color-interpolation"],["colorInterpolationFilters","color-interpolation-filters"],["colorProfile","color-profile"],["colorRendering","color-rendering"],["dominantBaseline","dominant-baseline"],["enableBackground","enable-background"],["fillOpacity","fill-opacity"],["fillRule","fill-rule"],["floodColor","flood-color"],["floodOpacity","flood-opacity"],["fontFamily","font-family"],["fontSize","font-size"],["fontSizeAdjust","font-size-adjust"],["fontStretch","font-stretch"],["fontStyle","font-style"],["fontVariant","font-variant"],["fontWeight","font-weight"],["glyphName","glyph-name"],["glyphOrientationHorizontal","glyph-orientation-horizontal"],["glyphOrientationVertical","glyph-orientation-vertical"],["horizAdvX","horiz-adv-x"],["horizOriginX","horiz-origin-x"],["imageRendering","image-rendering"],["letterSpacing","letter-spacing"],["lightingColor","lighting-color"],["markerEnd","marker-end"],["markerMid","marker-mid"],["markerStart","marker-start"],["overlinePosition","overline-position"],["overlineThickness","overline-thickness"],["paintOrder","paint-order"],["panose-1","panose-1"],["pointerEvents","pointer-events"],["renderingIntent","rendering-intent"],["shapeRendering","shape-rendering"],["stopColor","stop-color"],["stopOpacity","stop-opacity"],["strikethroughPosition","strikethrough-position"],["strikethroughThickness","strikethrough-thickness"],["strokeDasharray","stroke-dasharray"],["strokeDashoffset","stroke-dashoffset"],["strokeLinecap","stroke-linecap"],["strokeLinejoin","stroke-linejoin"],["strokeMiterlimit","stroke-miterlimit"],["strokeOpacity","stroke-opacity"],["strokeWidth","stroke-width"],["textAnchor","text-anchor"],["textDecoration","text-decoration"],["textRendering","text-rendering"],["transformOrigin","transform-origin"],["underlinePosition","underline-position"],["underlineThickness","underline-thickness"],["unicodeBidi","unicode-bidi"],["unicodeRange","unicode-range"],["unitsPerEm","units-per-em"],["vAlphabetic","v-alphabetic"],["vHanging","v-hanging"],["vIdeographic","v-ideographic"],["vMathematical","v-mathematical"],["vectorEffect","vector-effect"],["vertAdvY","vert-adv-y"],["vertOriginX","vert-origin-x"],["vertOriginY","vert-origin-y"],["wordSpacing","word-spacing"],["writingMode","writing-mode"],["xmlnsXlink","xmlns:xlink"],["xHeight","x-height"]]),zt=/^[\u0000-\u001F ]*j[\r\n\t]*a[\r\n\t]*v[\r\n\t]*a[\r\n\t]*s[\r\n\t]*c[\r\n\t]*r[\r\n\t]*i[\r\n\t]*p[\r\n\t]*t[\r\n\t]*:/i;function _t(e){return zt.test(""+e)?"javascript:throw new Error('React has blocked a javascript: URL as a security precaution.')":e}var Nt=null;function Tt(e){return(e=e.target||e.srcElement||window).correspondingUseElement&&(e=e.correspondingUseElement),3===e.nodeType?e.parentNode:e}var Lt=null,Ot=null;function jt(e){var t=Ue(e);if(t&&(e=t.stateNode)){var n=e[Oe]||null;e:switch(e=t.stateNode,t.type){case"input":if(mt(e,n.value,n.defaultValue,n.defaultValue,n.checked,n.defaultChecked,n.type,n.name),t=n.name,"radio"===n.type&&null!=t){for(n=e;n.parentNode;)n=n.parentNode;for(n=n.querySelectorAll('input[name="'+ht(""+t)+'"][type="radio"]'),t=0;t<n.length;t++){var r=n[t];if(r!==e&&r.form===e.form){var a=r[Oe]||null;if(!a)throw Error(o(90));mt(r,a.value,a.defaultValue,a.defaultValue,a.checked,a.defaultChecked,a.type,a.name)}}for(t=0;t<n.length;t++)(r=n[t]).form===e.form&&dt(r)}break e;case"textarea":bt(e,n.value,n.defaultValue);break e;case"select":null!=(t=n.value)&&vt(e,!!n.multiple,t,!1)}}}var Rt=!1;function At(e,t,n){if(Rt)return e(t,n);Rt=!0;try{return e(t)}finally{if(Rt=!1,(null!==Lt||null!==Ot)&&($u(),Lt&&(t=Lt,e=Ot,Ot=Lt=null,jt(t),e)))for(t=0;t<e.length;t++)jt(e[t])}}function Dt(e,t){var n=e.stateNode;if(null===n)return null;var r=n[Oe]||null;if(null===r)return null;n=r[t];e:switch(t){case"onClick":case"onClickCapture":case"onDoubleClick":case"onDoubleClickCapture":case"onMouseDown":case"onMouseDownCapture":case"onMouseMove":case"onMouseMoveCapture":case"onMouseUp":case"onMouseUpCapture":case"onMouseEnter":(r=!r.disabled)||(r=!("button"===(e=e.type)||"input"===e||"select"===e||"textarea"===e)),e=!r;break e;default:e=!1}if(e)return null;if(n&&"function"!=typeof n)throw Error(o(231,t,typeof n));return n}var It=!("undefined"==typeof window||void 0===window.document||void 0===window.document.createElement),Ft=!1;if(It)try{var Mt={};Object.defineProperty(Mt,"passive",{get:function(){Ft=!0}}),window.addEventListener("test",Mt,Mt),window.removeEventListener("test",Mt,Mt)}catch(e){Ft=!1}var $t=null,Ut=null,Ht=null;function Bt(){if(Ht)return Ht;var e,t,n=Ut,r=n.length,a="value"in $t?$t.value:$t.textContent,l=a.length;for(e=0;e<r&&n[e]===a[e];e++);var o=r-e;for(t=1;t<=o&&n[r-t]===a[l-t];t++);return Ht=a.slice(e,1<t?1-t:void 0)}function Vt(e){var t=e.keyCode;return"charCode"in e?0===(e=e.charCode)&&13===t&&(e=13):e=t,10===e&&(e=13),32<=e||13===e?e:0}function Wt(){return!0}function Qt(){return!1}function qt(e){function t(t,n,r,a,l){for(var o in this._reactName=t,this._targetInst=r,this.type=n,this.nativeEvent=a,this.target=l,this.currentTarget=null,e)e.hasOwnProperty(o)&&(t=e[o],this[o]=t?t(a):a[o]);return this.isDefaultPrevented=(null!=a.defaultPrevented?a.defaultPrevented:!1===a.returnValue)?Wt:Qt,this.isPropagationStopped=Qt,this}return d(t.prototype,{preventDefault:function(){this.defaultPrevented=!0;var e=this.nativeEvent;e&&(e.preventDefault?e.preventDefault():"unknown"!=typeof e.returnValue&&(e.returnValue=!1),this.isDefaultPrevented=Wt)},stopPropagation:function(){var e=this.nativeEvent;e&&(e.stopPropagation?e.stopPropagation():"unknown"!=typeof e.cancelBubble&&(e.cancelBubble=!0),this.isPropagationStopped=Wt)},persist:function(){},isPersistent:Wt}),t}var Gt,Yt,Kt,Xt={eventPhase:0,bubbles:0,cancelable:0,timeStamp:function(e){return e.timeStamp||Date.now()},defaultPrevented:0,isTrusted:0},Zt=qt(Xt),Jt=d({},Xt,{view:0,detail:0}),en=qt(Jt),tn=d({},Jt,{screenX:0,screenY:0,clientX:0,clientY:0,pageX:0,pageY:0,ctrlKey:0,shiftKey:0,altKey:0,metaKey:0,getModifierState:pn,button:0,buttons:0,relatedTarget:function(e){return void 0===e.relatedTarget?e.fromElement===e.srcElement?e.toElement:e.fromElement:e.relatedTarget},movementX:function(e){return"movementX"in e?e.movementX:(e!==Kt&&(Kt&&"mousemove"===e.type?(Gt=e.screenX-Kt.screenX,Yt=e.screenY-Kt.screenY):Yt=Gt=0,Kt=e),Gt)},movementY:function(e){return"movementY"in e?e.movementY:Yt}}),nn=qt(tn),rn=qt(d({},tn,{dataTransfer:0})),an=qt(d({},Jt,{relatedTarget:0})),ln=qt(d({},Xt,{animationName:0,elapsedTime:0,pseudoElement:0})),on=qt(d({},Xt,{clipboardData:function(e){return"clipboardData"in e?e.clipboardData:window.clipboardData}})),sn=qt(d({},Xt,{data:0})),un={Esc:"Escape",Spacebar:" ",Left:"ArrowLeft",Up:"ArrowUp",Right:"ArrowRight",Down:"ArrowDown",Del:"Delete",Win:"OS",Menu:"ContextMenu",Apps:"ContextMenu",Scroll:"ScrollLock",MozPrintableKey:"Unidentified"},cn={8:"Backspace",9:"Tab",12:"Clear",13:"Enter",16:"Shift",17:"Control",18:"Alt",19:"Pause",20:"CapsLock",27:"Escape",32:" ",33:"PageUp",34:"PageDown",35:"End",36:"Home",37:"ArrowLeft",38:"ArrowUp",39:"ArrowRight",40:"ArrowDown",45:"Insert",46:"Delete",112:"F1",113:"F2",114:"F3",115:"F4",116:"F5",117:"F6",118:"F7",119:"F8",120:"F9",121:"F10",122:"F11",123:"F12",144:"NumLock",145:"ScrollLock",224:"Meta"},dn={Alt:"altKey",Control:"ctrlKey",Meta:"metaKey",Shift:"shiftKey"};function fn(e){var t=this.nativeEvent;return t.getModifierState?t.getModifierState(e):!!(e=dn[e])&&!!t[e]}function pn(){return fn}var hn=qt(d({},Jt,{key:function(e){if(e.key){var t=un[e.key]||e.key;if("Unidentified"!==t)return t}return"keypress"===e.type?13===(e=Vt(e))?"Enter":String.fromCharCode(e):"keydown"===e.type||"keyup"===e.type?cn[e.keyCode]||"Unidentified":""},code:0,location:0,ctrlKey:0,shiftKey:0,altKey:0,metaKey:0,repeat:0,locale:0,getModifierState:pn,charCode:function(e){return"keypress"===e.type?Vt(e):0},keyCode:function(e){return"keydown"===e.type||"keyup"===e.type?e.keyCode:0},which:function(e){return"keypress"===e.type?Vt(e):"keydown"===e.type||"keyup"===e.type?e.keyCode:0}})),mn=qt(d({},tn,{pointerId:0,width:0,height:0,pressure:0,tangentialPressure:0,tiltX:0,tiltY:0,twist:0,pointerType:0,isPrimary:0})),gn=qt(d({},Jt,{touches:0,targetTouches:0,changedTouches:0,altKey:0,metaKey:0,ctrlKey:0,shiftKey:0,getModifierState:pn})),yn=qt(d({},Xt,{propertyName:0,elapsedTime:0,pseudoElement:0})),vn=qt(d({},tn,{deltaX:function(e){return"deltaX"in e?e.deltaX:"wheelDeltaX"in e?-e.wheelDeltaX:0},deltaY:function(e){return"deltaY"in e?e.deltaY:"wheelDeltaY"in e?-e.wheelDeltaY:"wheelDelta"in e?-e.wheelDelta:0},deltaZ:0,deltaMode:0})),bn=qt(d({},Xt,{newState:0,oldState:0})),wn=[9,13,27,32],kn=It&&"CompositionEvent"in window,Sn=null;It&&"documentMode"in document&&(Sn=document.documentMode);var xn=It&&"TextEvent"in window&&!Sn,Cn=It&&(!kn||Sn&&8<Sn&&11>=Sn),En=String.fromCharCode(32),Pn=!1;function zn(e,t){switch(e){case"keyup":return-1!==wn.indexOf(t.keyCode);case"keydown":return 229!==t.keyCode;case"keypress":case"mousedown":case"focusout":return!0;default:return!1}}function _n(e){return"object"==typeof(e=e.detail)&&"data"in e?e.data:null}var Nn=!1,Tn={color:!0,date:!0,datetime:!0,"datetime-local":!0,email:!0,month:!0,number:!0,password:!0,range:!0,search:!0,tel:!0,text:!0,time:!0,url:!0,week:!0};function Ln(e){var t=e&&e.nodeName&&e.nodeName.toLowerCase();return"input"===t?!!Tn[e.type]:"textarea"===t}function On(e,t,n,r){Lt?Ot?Ot.push(r):Ot=[r]:Lt=r,0<(t=Bc(t,"onChange")).length&&(n=new Zt("onChange","change",null,n,r),e.push({event:n,listeners:t}))}var jn=null,Rn=null;function An(e){Ac(e,0)}function Dn(e){if(dt(He(e)))return e}function In(e,t){if("change"===e)return t}var Fn=!1;if(It){var Mn;if(It){var $n="oninput"in document;if(!$n){var Un=document.createElement("div");Un.setAttribute("oninput","return;"),$n="function"==typeof Un.oninput}Mn=$n}else Mn=!1;Fn=Mn&&(!document.documentMode||9<document.documentMode)}function Hn(){jn&&(jn.detachEvent("onpropertychange",Bn),Rn=jn=null)}function Bn(e){if("value"===e.propertyName&&Dn(Rn)){var t=[];On(t,Rn,e,Tt(e)),At(An,t)}}function Vn(e,t,n){"focusin"===e?(Hn(),Rn=n,(jn=t).attachEvent("onpropertychange",Bn)):"focusout"===e&&Hn()}function Wn(e){if("selectionchange"===e||"keyup"===e||"keydown"===e)return Dn(Rn)}function Qn(e,t){if("click"===e)return Dn(t)}function qn(e,t){if("input"===e||"change"===e)return Dn(t)}var Gn="function"==typeof Object.is?Object.is:function(e,t){return e===t&&(0!==e||1/e==1/t)||e!=e&&t!=t};function Yn(e,t){if(Gn(e,t))return!0;if("object"!=typeof e||null===e||"object"!=typeof t||null===t)return!1;var n=Object.keys(e),r=Object.keys(t);if(n.length!==r.length)return!1;for(r=0;r<n.length;r++){var a=n[r];if(!Y.call(t,a)||!Gn(e[a],t[a]))return!1}return!0}function Kn(e){for(;e&&e.firstChild;)e=e.firstChild;return e}function Xn(e,t){var n,r=Kn(e);for(e=0;r;){if(3===r.nodeType){if(n=e+r.textContent.length,e<=t&&n>=t)return{node:r,offset:t-e};e=n}e:{for(;r;){if(r.nextSibling){r=r.nextSibling;break e}r=r.parentNode}r=void 0}r=Kn(r)}}function Zn(e,t){return!(!e||!t)&&(e===t||(!e||3!==e.nodeType)&&(t&&3===t.nodeType?Zn(e,t.parentNode):"contains"in e?e.contains(t):!!e.compareDocumentPosition&&!!(16&e.compareDocumentPosition(t))))}function Jn(e){for(var t=ft((e=null!=e&&null!=e.ownerDocument&&null!=e.ownerDocument.defaultView?e.ownerDocument.defaultView:window).document);t instanceof e.HTMLIFrameElement;){try{var n="string"==typeof t.contentWindow.location.href}catch(e){n=!1}if(!n)break;t=ft((e=t.contentWindow).document)}return t}function er(e){var t=e&&e.nodeName&&e.nodeName.toLowerCase();return t&&("input"===t&&("text"===e.type||"search"===e.type||"tel"===e.type||"url"===e.type||"password"===e.type)||"textarea"===t||"true"===e.contentEditable)}var tr=It&&"documentMode"in document&&11>=document.documentMode,nr=null,rr=null,ar=null,lr=!1;function or(e,t,n){var r=n.window===n?n.document:9===n.nodeType?n:n.ownerDocument;lr||null==nr||nr!==ft(r)||(r="selectionStart"in(r=nr)&&er(r)?{start:r.selectionStart,end:r.selectionEnd}:{anchorNode:(r=(r.ownerDocument&&r.ownerDocument.defaultView||window).getSelection()).anchorNode,anchorOffset:r.anchorOffset,focusNode:r.focusNode,focusOffset:r.focusOffset},ar&&Yn(ar,r)||(ar=r,0<(r=Bc(rr,"onSelect")).length&&(t=new Zt("onSelect","select",null,t,n),e.push({event:t,listeners:r}),t.target=nr)))}function ir(e,t){var n={};return n[e.toLowerCase()]=t.toLowerCase(),n["Webkit"+e]="webkit"+t,n["Moz"+e]="moz"+t,n}var sr={animationend:ir("Animation","AnimationEnd"),animationiteration:ir("Animation","AnimationIteration"),animationstart:ir("Animation","AnimationStart"),transitionrun:ir("Transition","TransitionRun"),transitionstart:ir("Transition","TransitionStart"),transitioncancel:ir("Transition","TransitionCancel"),transitionend:ir("Transition","TransitionEnd")},ur={},cr={};function dr(e){if(ur[e])return ur[e];if(!sr[e])return e;var t,n=sr[e];for(t in n)if(n.hasOwnProperty(t)&&t in cr)return ur[e]=n[t];return e}It&&(cr=document.createElement("div").style,"AnimationEvent"in window||(delete sr.animationend.animation,delete sr.animationiteration.animation,delete sr.animationstart.animation),"TransitionEvent"in window||delete sr.transitionend.transition);var fr=dr("animationend"),pr=dr("animationiteration"),hr=dr("animationstart"),mr=dr("transitionrun"),gr=dr("transitionstart"),yr=dr("transitioncancel"),vr=dr("transitionend"),br=new Map,wr="abort auxClick beforeToggle cancel canPlay canPlayThrough click close contextMenu copy cut drag dragEnd dragEnter dragExit dragLeave dragOver dragStart drop durationChange emptied encrypted ended error gotPointerCapture input invalid keyDown keyPress keyUp load loadedData loadedMetadata loadStart lostPointerCapture mouseDown mouseMove mouseOut mouseOver mouseUp paste pause play playing pointerCancel pointerDown pointerMove pointerOut pointerOver pointerUp progress rateChange reset resize seeked seeking stalled submit suspend timeUpdate touchCancel touchEnd touchStart volumeChange scroll toggle touchMove waiting wheel".split(" ");function kr(e,t){br.set(e,t),qe(t,[e])}wr.push("scrollEnd");var Sr=new WeakMap;function xr(e,t){if("object"==typeof e&&null!==e){var n=Sr.get(e);return void 0!==n?n:(t={value:e,source:t,stack:it(t)},Sr.set(e,t),t)}return{value:e,source:t,stack:it(t)}}var Cr=[],Er=0,Pr=0;function zr(){for(var e=Er,t=Pr=Er=0;t<e;){var n=Cr[t];Cr[t++]=null;var r=Cr[t];Cr[t++]=null;var a=Cr[t];Cr[t++]=null;var l=Cr[t];if(Cr[t++]=null,null!==r&&null!==a){var o=r.pending;null===o?a.next=a:(a.next=o.next,o.next=a),r.pending=a}0!==l&&Lr(n,a,l)}}function _r(e,t,n,r){Cr[Er++]=e,Cr[Er++]=t,Cr[Er++]=n,Cr[Er++]=r,Pr|=r,e.lanes|=r,null!==(e=e.alternate)&&(e.lanes|=r)}function Nr(e,t,n,r){return _r(e,t,n,r),Or(e)}function Tr(e,t){return _r(e,null,null,t),Or(e)}function Lr(e,t,n){e.lanes|=n;var r=e.alternate;null!==r&&(r.lanes|=n);for(var a=!1,l=e.return;null!==l;)l.childLanes|=n,null!==(r=l.alternate)&&(r.childLanes|=n),22===l.tag&&(null===(e=l.stateNode)||1&e._visibility||(a=!0)),e=l,l=l.return;return 3===e.tag?(l=e.stateNode,a&&null!==t&&(a=31-fe(n),null===(r=(e=l.hiddenUpdates)[a])?e[a]=[t]:r.push(t),t.lane=536870912|n),l):null}function Or(e){if(50<Lu)throw Lu=0,Ou=null,Error(o(185));for(var t=e.return;null!==t;)t=(e=t).return;return 3===e.tag?e.stateNode:null}var jr={};function Rr(e,t,n,r){this.tag=e,this.key=n,this.sibling=this.child=this.return=this.stateNode=this.type=this.elementType=null,this.index=0,this.refCleanup=this.ref=null,this.pendingProps=t,this.dependencies=this.memoizedState=this.updateQueue=this.memoizedProps=null,this.mode=r,this.subtreeFlags=this.flags=0,this.deletions=null,this.childLanes=this.lanes=0,this.alternate=null}function Ar(e,t,n,r){return new Rr(e,t,n,r)}function Dr(e){return!(!(e=e.prototype)||!e.isReactComponent)}function Ir(e,t){var n=e.alternate;return null===n?((n=Ar(e.tag,t,e.key,e.mode)).elementType=e.elementType,n.type=e.type,n.stateNode=e.stateNode,n.alternate=e,e.alternate=n):(n.pendingProps=t,n.type=e.type,n.flags=0,n.subtreeFlags=0,n.deletions=null),n.flags=65011712&e.flags,n.childLanes=e.childLanes,n.lanes=e.lanes,n.child=e.child,n.memoizedProps=e.memoizedProps,n.memoizedState=e.memoizedState,n.updateQueue=e.updateQueue,t=e.dependencies,n.dependencies=null===t?null:{lanes:t.lanes,firstContext:t.firstContext},n.sibling=e.sibling,n.index=e.index,n.ref=e.ref,n.refCleanup=e.refCleanup,n}function Fr(e,t){e.flags&=65011714;var n=e.alternate;return null===n?(e.childLanes=0,e.lanes=t,e.child=null,e.subtreeFlags=0,e.memoizedProps=null,e.memoizedState=null,e.updateQueue=null,e.dependencies=null,e.stateNode=null):(e.childLanes=n.childLanes,e.lanes=n.lanes,e.child=n.child,e.subtreeFlags=0,e.deletions=null,e.memoizedProps=n.memoizedProps,e.memoizedState=n.memoizedState,e.updateQueue=n.updateQueue,e.type=n.type,t=n.dependencies,e.dependencies=null===t?null:{lanes:t.lanes,firstContext:t.firstContext}),e}function Mr(e,t,n,r,a,l){var i=0;if(r=e,"function"==typeof e)Dr(e)&&(i=1);else if("string"==typeof e)i=function(e,t,n){if(1===n||null!=t.itemProp)return!1;switch(e){case"meta":case"title":return!0;case"style":if("string"!=typeof t.precedence||"string"!=typeof t.href||""===t.href)break;return!0;case"link":if("string"!=typeof t.rel||"string"!=typeof t.href||""===t.href||t.onLoad||t.onError)break;return"stylesheet"!==t.rel||(e=t.disabled,"string"==typeof t.precedence&&null==e);case"script":if(t.async&&"function"!=typeof t.async&&"symbol"!=typeof t.async&&!t.onLoad&&!t.onError&&t.src&&"string"==typeof t.src)return!0}return!1}(e,n,U.current)?26:"html"===e||"head"===e||"body"===e?27:5;else e:switch(e){case P:return(e=Ar(31,n,t,a)).elementType=P,e.lanes=l,e;case m:return $r(n.children,a,l,t);case g:i=8,a|=24;break;case y:return(e=Ar(12,n,t,2|a)).elementType=y,e.lanes=l,e;case S:return(e=Ar(13,n,t,a)).elementType=S,e.lanes=l,e;case x:return(e=Ar(19,n,t,a)).elementType=x,e.lanes=l,e;default:if("object"==typeof e&&null!==e)switch(e.$$typeof){case v:case w:i=10;break e;case b:i=9;break e;case k:i=11;break e;case C:i=14;break e;case E:i=16,r=null;break e}i=29,n=Error(o(130,null===e?"null":typeof e,"")),r=null}return(t=Ar(i,n,t,a)).elementType=e,t.type=r,t.lanes=l,t}function $r(e,t,n,r){return(e=Ar(7,e,r,t)).lanes=n,e}function Ur(e,t,n){return(e=Ar(6,e,null,t)).lanes=n,e}function Hr(e,t,n){return(t=Ar(4,null!==e.children?e.children:[],e.key,t)).lanes=n,t.stateNode={containerInfo:e.containerInfo,pendingChildren:null,implementation:e.implementation},t}var Br=[],Vr=0,Wr=null,Qr=0,qr=[],Gr=0,Yr=null,Kr=1,Xr="";function Zr(e,t){Br[Vr++]=Qr,Br[Vr++]=Wr,Wr=e,Qr=t}function Jr(e,t,n){qr[Gr++]=Kr,qr[Gr++]=Xr,qr[Gr++]=Yr,Yr=e;var r=Kr;e=Xr;var a=32-fe(r)-1;r&=~(1<<a),n+=1;var l=32-fe(t)+a;if(30<l){var o=a-a%5;l=(r&(1<<o)-1).toString(32),r>>=o,a-=o,Kr=1<<32-fe(t)+a|n<<a|r,Xr=l+e}else Kr=1<<l|n<<a|r,Xr=e}function ea(e){null!==e.return&&(Zr(e,1),Jr(e,1,0))}function ta(e){for(;e===Wr;)Wr=Br[--Vr],Br[Vr]=null,Qr=Br[--Vr],Br[Vr]=null;for(;e===Yr;)Yr=qr[--Gr],qr[Gr]=null,Xr=qr[--Gr],qr[Gr]=null,Kr=qr[--Gr],qr[Gr]=null}var na=null,ra=null,aa=!1,la=null,oa=!1,ia=Error(o(519));function sa(e){throw ha(xr(Error(o(418,"")),e)),ia}function ua(e){var t=e.stateNode,n=e.type,r=e.memoizedProps;switch(t[Le]=e,t[Oe]=r,n){case"dialog":Dc("cancel",t),Dc("close",t);break;case"iframe":case"object":case"embed":Dc("load",t);break;case"video":case"audio":for(n=0;n<jc.length;n++)Dc(jc[n],t);break;case"source":Dc("error",t);break;case"img":case"image":case"link":Dc("error",t),Dc("load",t);break;case"details":Dc("toggle",t);break;case"input":Dc("invalid",t),gt(t,r.value,r.defaultValue,r.checked,r.defaultChecked,r.type,r.name,!0),ct(t);break;case"select":Dc("invalid",t);break;case"textarea":Dc("invalid",t),wt(t,r.value,r.defaultValue,r.children),ct(t)}"string"!=typeof(n=r.children)&&"number"!=typeof n&&"bigint"!=typeof n||t.textContent===""+n||!0===r.suppressHydrationWarning||Yc(t.textContent,n)?(null!=r.popover&&(Dc("beforetoggle",t),Dc("toggle",t)),null!=r.onScroll&&Dc("scroll",t),null!=r.onScrollEnd&&Dc("scrollend",t),null!=r.onClick&&(t.onclick=Kc),t=!0):t=!1,t||sa(e)}function ca(e){for(na=e.return;na;)switch(na.tag){case 5:case 13:return void(oa=!1);case 27:case 3:return void(oa=!0);default:na=na.return}}function da(e){if(e!==na)return!1;if(!aa)return ca(e),aa=!0,!1;var t,n=e.tag;if((t=3!==n&&27!==n)&&((t=5===n)&&(t=!("form"!==(t=e.type)&&"button"!==t)||ld(e.type,e.memoizedProps)),t=!t),t&&ra&&sa(e),ca(e),13===n){if(!(e=null!==(e=e.memoizedState)?e.dehydrated:null))throw Error(o(317));e:{for(e=e.nextSibling,n=0;e;){if(8===e.nodeType)if("/$"===(t=e.data)){if(0===n){ra=gd(e.nextSibling);break e}n--}else"$"!==t&&"$!"!==t&&"$?"!==t||n++;e=e.nextSibling}ra=null}}else 27===n?(n=ra,fd(e.type)?(e=yd,yd=null,ra=e):ra=n):ra=na?gd(e.stateNode.nextSibling):null;return!0}function fa(){ra=na=null,aa=!1}function pa(){var e=la;return null!==e&&(null===vu?vu=e:vu.push.apply(vu,e),la=null),e}function ha(e){null===la?la=[e]:la.push(e)}var ma=F(null),ga=null,ya=null;function va(e,t,n){$(ma,t._currentValue),t._currentValue=n}function ba(e){e._currentValue=ma.current,M(ma)}function wa(e,t,n){for(;null!==e;){var r=e.alternate;if((e.childLanes&t)!==t?(e.childLanes|=t,null!==r&&(r.childLanes|=t)):null!==r&&(r.childLanes&t)!==t&&(r.childLanes|=t),e===n)break;e=e.return}}function ka(e,t,n,r){var a=e.child;for(null!==a&&(a.return=e);null!==a;){var l=a.dependencies;if(null!==l){var i=a.child;l=l.firstContext;e:for(;null!==l;){var s=l;l=a;for(var u=0;u<t.length;u++)if(s.context===t[u]){l.lanes|=n,null!==(s=l.alternate)&&(s.lanes|=n),wa(l.return,n,e),r||(i=null);break e}l=s.next}}else if(18===a.tag){if(null===(i=a.return))throw Error(o(341));i.lanes|=n,null!==(l=i.alternate)&&(l.lanes|=n),wa(i,n,e),i=null}else i=a.child;if(null!==i)i.return=a;else for(i=a;null!==i;){if(i===e){i=null;break}if(null!==(a=i.sibling)){a.return=i.return,i=a;break}i=i.return}a=i}}function Sa(e,t,n,r){e=null;for(var a=t,l=!1;null!==a;){if(!l)if(524288&a.flags)l=!0;else if(262144&a.flags)break;if(10===a.tag){var i=a.alternate;if(null===i)throw Error(o(387));if(null!==(i=i.memoizedProps)){var s=a.type;Gn(a.pendingProps.value,i.value)||(null!==e?e.push(s):e=[s])}}else if(a===V.current){if(null===(i=a.alternate))throw Error(o(387));i.memoizedState.memoizedState!==a.memoizedState.memoizedState&&(null!==e?e.push(qd):e=[qd])}a=a.return}null!==e&&ka(t,e,n,r),t.flags|=262144}function xa(e){for(e=e.firstContext;null!==e;){if(!Gn(e.context._currentValue,e.memoizedValue))return!0;e=e.next}return!1}function Ca(e){ga=e,ya=null,null!==(e=e.dependencies)&&(e.firstContext=null)}function Ea(e){return za(ga,e)}function Pa(e,t){return null===ga&&Ca(e),za(e,t)}function za(e,t){var n=t._currentValue;if(t={context:t,memoizedValue:n,next:null},null===ya){if(null===e)throw Error(o(308));ya=t,e.dependencies={lanes:0,firstContext:t},e.flags|=524288}else ya=ya.next=t;return n}var _a="undefined"!=typeof AbortController?AbortController:function(){var e=[],t=this.signal={aborted:!1,addEventListener:function(t,n){e.push(n)}};this.abort=function(){t.aborted=!0,e.forEach(function(e){return e()})}},Na=r.unstable_scheduleCallback,Ta=r.unstable_NormalPriority,La={$$typeof:w,Consumer:null,Provider:null,_currentValue:null,_currentValue2:null,_threadCount:0};function Oa(){return{controller:new _a,data:new Map,refCount:0}}function ja(e){e.refCount--,0===e.refCount&&Na(Ta,function(){e.controller.abort()})}var Ra=null,Aa=0,Da=0,Ia=null;function Fa(){if(0===--Aa&&null!==Ra){null!==Ia&&(Ia.status="fulfilled");var e=Ra;Ra=null,Da=0,Ia=null;for(var t=0;t<e.length;t++)(0,e[t])()}}var Ma=j.S;j.S=function(e,t){"object"==typeof t&&null!==t&&"function"==typeof t.then&&function(e,t){if(null===Ra){var n=Ra=[];Aa=0,Da=_c(),Ia={status:"pending",value:void 0,then:function(e){n.push(e)}}}Aa++,t.then(Fa,Fa)}(0,t),null!==Ma&&Ma(e,t)};var $a=F(null);function Ua(){var e=$a.current;return null!==e?e:nu.pooledCache}function Ha(e,t){$($a,null===t?$a.current:t.pool)}function Ba(){var e=Ua();return null===e?null:{parent:La._currentValue,pool:e}}var Va=Error(o(460)),Wa=Error(o(474)),Qa=Error(o(542)),qa={then:function(){}};function Ga(e){return"fulfilled"===(e=e.status)||"rejected"===e}function Ya(){}function Ka(e,t,n){switch(void 0===(n=e[n])?e.push(t):n!==t&&(t.then(Ya,Ya),t=n),t.status){case"fulfilled":return t.value;case"rejected":throw Ja(e=t.reason),e;default:if("string"==typeof t.status)t.then(Ya,Ya);else{if(null!==(e=nu)&&100<e.shellSuspendCounter)throw Error(o(482));(e=t).status="pending",e.then(function(e){if("pending"===t.status){var n=t;n.status="fulfilled",n.value=e}},function(e){if("pending"===t.status){var n=t;n.status="rejected",n.reason=e}})}switch(t.status){case"fulfilled":return t.value;case"rejected":throw Ja(e=t.reason),e}throw Xa=t,Va}}var Xa=null;function Za(){if(null===Xa)throw Error(o(459));var e=Xa;return Xa=null,e}function Ja(e){if(e===Va||e===Qa)throw Error(o(483))}var el=!1;function tl(e){e.updateQueue={baseState:e.memoizedState,firstBaseUpdate:null,lastBaseUpdate:null,shared:{pending:null,lanes:0,hiddenCallbacks:null},callbacks:null}}function nl(e,t){e=e.updateQueue,t.updateQueue===e&&(t.updateQueue={baseState:e.baseState,firstBaseUpdate:e.firstBaseUpdate,lastBaseUpdate:e.lastBaseUpdate,shared:e.shared,callbacks:null})}function rl(e){return{lane:e,tag:0,payload:null,callback:null,next:null}}function al(e,t,n){var r=e.updateQueue;if(null===r)return null;if(r=r.shared,2&tu){var a=r.pending;return null===a?t.next=t:(t.next=a.next,a.next=t),r.pending=t,t=Or(e),Lr(e,null,n),t}return _r(e,r,t,n),Or(e)}function ll(e,t,n){if(null!==(t=t.updateQueue)&&(t=t.shared,4194048&n)){var r=t.lanes;n|=r&=e.pendingLanes,t.lanes=n,Pe(e,n)}}function ol(e,t){var n=e.updateQueue,r=e.alternate;if(null!==r&&n===(r=r.updateQueue)){var a=null,l=null;if(null!==(n=n.firstBaseUpdate)){do{var o={lane:n.lane,tag:n.tag,payload:n.payload,callback:null,next:null};null===l?a=l=o:l=l.next=o,n=n.next}while(null!==n);null===l?a=l=t:l=l.next=t}else a=l=t;return n={baseState:r.baseState,firstBaseUpdate:a,lastBaseUpdate:l,shared:r.shared,callbacks:r.callbacks},void(e.updateQueue=n)}null===(e=n.lastBaseUpdate)?n.firstBaseUpdate=t:e.next=t,n.lastBaseUpdate=t}var il=!1;function sl(){if(il&&null!==Ia)throw Ia}function ul(e,t,n,r){il=!1;var a=e.updateQueue;el=!1;var l=a.firstBaseUpdate,o=a.lastBaseUpdate,i=a.shared.pending;if(null!==i){a.shared.pending=null;var s=i,u=s.next;s.next=null,null===o?l=u:o.next=u,o=s;var c=e.alternate;null!==c&&(i=(c=c.updateQueue).lastBaseUpdate)!==o&&(null===i?c.firstBaseUpdate=u:i.next=u,c.lastBaseUpdate=s)}if(null!==l){var f=a.baseState;for(o=0,c=u=s=null,i=l;;){var p=-536870913&i.lane,h=p!==i.lane;if(h?(au&p)===p:(r&p)===p){0!==p&&p===Da&&(il=!0),null!==c&&(c=c.next={lane:0,tag:i.tag,payload:i.payload,callback:null,next:null});e:{var m=e,g=i;p=t;var y=n;switch(g.tag){case 1:if("function"==typeof(m=g.payload)){f=m.call(y,f,p);break e}f=m;break e;case 3:m.flags=-65537&m.flags|128;case 0:if(null==(p="function"==typeof(m=g.payload)?m.call(y,f,p):m))break e;f=d({},f,p);break e;case 2:el=!0}}null!==(p=i.callback)&&(e.flags|=64,h&&(e.flags|=8192),null===(h=a.callbacks)?a.callbacks=[p]:h.push(p))}else h={lane:p,tag:i.tag,payload:i.payload,callback:i.callback,next:null},null===c?(u=c=h,s=f):c=c.next=h,o|=p;if(null===(i=i.next)){if(null===(i=a.shared.pending))break;i=(h=i).next,h.next=null,a.lastBaseUpdate=h,a.shared.pending=null}}null===c&&(s=f),a.baseState=s,a.firstBaseUpdate=u,a.lastBaseUpdate=c,null===l&&(a.shared.lanes=0),fu|=o,e.lanes=o,e.memoizedState=f}}function cl(e,t){if("function"!=typeof e)throw Error(o(191,e));e.call(t)}function dl(e,t){var n=e.callbacks;if(null!==n)for(e.callbacks=null,e=0;e<n.length;e++)cl(n[e],t)}var fl=F(null),pl=F(0);function hl(e,t){$(pl,e=cu),$(fl,t),cu=e|t.baseLanes}function ml(){$(pl,cu),$(fl,fl.current)}function gl(){cu=pl.current,M(fl),M(pl)}var yl=0,vl=null,bl=null,wl=null,kl=!1,Sl=!1,xl=!1,Cl=0,El=0,Pl=null,zl=0;function _l(){throw Error(o(321))}function Nl(e,t){if(null===t)return!1;for(var n=0;n<t.length&&n<e.length;n++)if(!Gn(e[n],t[n]))return!1;return!0}function Tl(e,t,n,r,a,l){return yl=l,vl=t,t.memoizedState=null,t.updateQueue=null,t.lanes=0,j.H=null===e||null===e.memoizedState?Wo:Qo,xl=!1,l=n(r,a),xl=!1,Sl&&(l=Ol(t,n,r,a)),Ll(e),l}function Ll(e){j.H=Vo;var t=null!==bl&&null!==bl.next;if(yl=0,wl=bl=vl=null,kl=!1,El=0,Pl=null,t)throw Error(o(300));null===e||Pi||null!==(e=e.dependencies)&&xa(e)&&(Pi=!0)}function Ol(e,t,n,r){vl=e;var a=0;do{if(Sl&&(Pl=null),El=0,Sl=!1,25<=a)throw Error(o(301));if(a+=1,wl=bl=null,null!=e.updateQueue){var l=e.updateQueue;l.lastEffect=null,l.events=null,l.stores=null,null!=l.memoCache&&(l.memoCache.index=0)}j.H=qo,l=t(n,r)}while(Sl);return l}function jl(){var e=j.H,t=e.useState()[0];return t="function"==typeof t.then?Ml(t):t,e=e.useState()[0],(null!==bl?bl.memoizedState:null)!==e&&(vl.flags|=1024),t}function Rl(){var e=0!==Cl;return Cl=0,e}function Al(e,t,n){t.updateQueue=e.updateQueue,t.flags&=-2053,e.lanes&=~n}function Dl(e){if(kl){for(e=e.memoizedState;null!==e;){var t=e.queue;null!==t&&(t.pending=null),e=e.next}kl=!1}yl=0,wl=bl=vl=null,Sl=!1,El=Cl=0,Pl=null}function Il(){var e={memoizedState:null,baseState:null,baseQueue:null,queue:null,next:null};return null===wl?vl.memoizedState=wl=e:wl=wl.next=e,wl}function Fl(){if(null===bl){var e=vl.alternate;e=null!==e?e.memoizedState:null}else e=bl.next;var t=null===wl?vl.memoizedState:wl.next;if(null!==t)wl=t,bl=e;else{if(null===e){if(null===vl.alternate)throw Error(o(467));throw Error(o(310))}e={memoizedState:(bl=e).memoizedState,baseState:bl.baseState,baseQueue:bl.baseQueue,queue:bl.queue,next:null},null===wl?vl.memoizedState=wl=e:wl=wl.next=e}return wl}function Ml(e){var t=El;return El+=1,null===Pl&&(Pl=[]),e=Ka(Pl,e,t),t=vl,null===(null===wl?t.memoizedState:wl.next)&&(t=t.alternate,j.H=null===t||null===t.memoizedState?Wo:Qo),e}function $l(e){if(null!==e&&"object"==typeof e){if("function"==typeof e.then)return Ml(e);if(e.$$typeof===w)return Ea(e)}throw Error(o(438,String(e)))}function Ul(e){var t=null,n=vl.updateQueue;if(null!==n&&(t=n.memoCache),null==t){var r=vl.alternate;null!==r&&null!==(r=r.updateQueue)&&null!=(r=r.memoCache)&&(t={data:r.data.map(function(e){return e.slice()}),index:0})}if(null==t&&(t={data:[],index:0}),null===n&&(n={lastEffect:null,events:null,stores:null,memoCache:null},vl.updateQueue=n),n.memoCache=t,void 0===(n=t.data[t.index]))for(n=t.data[t.index]=Array(e),r=0;r<e;r++)n[r]=z;return t.index++,n}function Hl(e,t){return"function"==typeof t?t(e):t}function Bl(e){return Vl(Fl(),bl,e)}function Vl(e,t,n){var r=e.queue;if(null===r)throw Error(o(311));r.lastRenderedReducer=n;var a=e.baseQueue,l=r.pending;if(null!==l){if(null!==a){var i=a.next;a.next=l.next,l.next=i}t.baseQueue=a=l,r.pending=null}if(l=e.baseState,null===a)e.memoizedState=l;else{var s=i=null,u=null,c=t=a.next,d=!1;do{var f=-536870913&c.lane;if(f!==c.lane?(au&f)===f:(yl&f)===f){var p=c.revertLane;if(0===p)null!==u&&(u=u.next={lane:0,revertLane:0,action:c.action,hasEagerState:c.hasEagerState,eagerState:c.eagerState,next:null}),f===Da&&(d=!0);else{if((yl&p)===p){c=c.next,p===Da&&(d=!0);continue}f={lane:0,revertLane:c.revertLane,action:c.action,hasEagerState:c.hasEagerState,eagerState:c.eagerState,next:null},null===u?(s=u=f,i=l):u=u.next=f,vl.lanes|=p,fu|=p}f=c.action,xl&&n(l,f),l=c.hasEagerState?c.eagerState:n(l,f)}else p={lane:f,revertLane:c.revertLane,action:c.action,hasEagerState:c.hasEagerState,eagerState:c.eagerState,next:null},null===u?(s=u=p,i=l):u=u.next=p,vl.lanes|=f,fu|=f;c=c.next}while(null!==c&&c!==t);if(null===u?i=l:u.next=s,!Gn(l,e.memoizedState)&&(Pi=!0,d&&null!==(n=Ia)))throw n;e.memoizedState=l,e.baseState=i,e.baseQueue=u,r.lastRenderedState=l}return null===a&&(r.lanes=0),[e.memoizedState,r.dispatch]}function Wl(e){var t=Fl(),n=t.queue;if(null===n)throw Error(o(311));n.lastRenderedReducer=e;var r=n.dispatch,a=n.pending,l=t.memoizedState;if(null!==a){n.pending=null;var i=a=a.next;do{l=e(l,i.action),i=i.next}while(i!==a);Gn(l,t.memoizedState)||(Pi=!0),t.memoizedState=l,null===t.baseQueue&&(t.baseState=l),n.lastRenderedState=l}return[l,r]}function Ql(e,t,n){var r=vl,a=Fl(),l=aa;if(l){if(void 0===n)throw Error(o(407));n=n()}else n=t();var i=!Gn((bl||a).memoizedState,n);if(i&&(a.memoizedState=n,Pi=!0),a=a.queue,go(2048,8,Yl.bind(null,r,a,e),[e]),a.getSnapshot!==t||i||null!==wl&&1&wl.memoizedState.tag){if(r.flags|=2048,po(9,{destroy:void 0,resource:void 0},Gl.bind(null,r,a,n,t),null),null===nu)throw Error(o(349));l||124&yl||ql(r,t,n)}return n}function ql(e,t,n){e.flags|=16384,e={getSnapshot:t,value:n},null===(t=vl.updateQueue)?(t={lastEffect:null,events:null,stores:null,memoCache:null},vl.updateQueue=t,t.stores=[e]):null===(n=t.stores)?t.stores=[e]:n.push(e)}function Gl(e,t,n,r){t.value=n,t.getSnapshot=r,Kl(t)&&Xl(e)}function Yl(e,t,n){return n(function(){Kl(t)&&Xl(e)})}function Kl(e){var t=e.getSnapshot;e=e.value;try{var n=t();return!Gn(e,n)}catch(e){return!0}}function Xl(e){var t=Tr(e,2);null!==t&&Au(t,0,2)}function Zl(e){var t=Il();if("function"==typeof e){var n=e;if(e=n(),xl){de(!0);try{n()}finally{de(!1)}}}return t.memoizedState=t.baseState=e,t.queue={pending:null,lanes:0,dispatch:null,lastRenderedReducer:Hl,lastRenderedState:e},t}function Jl(e,t,n,r){return e.baseState=n,Vl(e,bl,"function"==typeof r?r:Hl)}function eo(e,t,n,r,a){if(Uo(e))throw Error(o(485));if(null!==(e=t.action)){var l={payload:a,action:e,next:null,isTransition:!0,status:"pending",value:null,reason:null,listeners:[],then:function(e){l.listeners.push(e)}};null!==j.T?n(!0):l.isTransition=!1,r(l),null===(n=t.pending)?(l.next=t.pending=l,to(t,l)):(l.next=n.next,t.pending=n.next=l)}}function to(e,t){var n=t.action,r=t.payload,a=e.state;if(t.isTransition){var l=j.T,o={};j.T=o;try{var i=n(a,r),s=j.S;null!==s&&s(o,i),no(e,t,i)}catch(n){ao(e,t,n)}finally{j.T=l}}else try{no(e,t,l=n(a,r))}catch(n){ao(e,t,n)}}function no(e,t,n){null!==n&&"object"==typeof n&&"function"==typeof n.then?n.then(function(n){ro(e,t,n)},function(n){return ao(e,t,n)}):ro(e,t,n)}function ro(e,t,n){t.status="fulfilled",t.value=n,lo(t),e.state=n,null!==(t=e.pending)&&((n=t.next)===t?e.pending=null:(n=n.next,t.next=n,to(e,n)))}function ao(e,t,n){var r=e.pending;if(e.pending=null,null!==r){r=r.next;do{t.status="rejected",t.reason=n,lo(t),t=t.next}while(t!==r)}e.action=null}function lo(e){e=e.listeners;for(var t=0;t<e.length;t++)(0,e[t])()}function oo(e,t){return t}function io(e,t){if(aa){var n=nu.formState;if(null!==n){e:{var r=vl;if(aa){if(ra){t:{for(var a=ra,l=oa;8!==a.nodeType;){if(!l){a=null;break t}if(null===(a=gd(a.nextSibling))){a=null;break t}}a="F!"===(l=a.data)||"F"===l?a:null}if(a){ra=gd(a.nextSibling),r="F!"===a.data;break e}}sa(r)}r=!1}r&&(t=n[0])}}return(n=Il()).memoizedState=n.baseState=t,r={pending:null,lanes:0,dispatch:null,lastRenderedReducer:oo,lastRenderedState:t},n.queue=r,n=Fo.bind(null,vl,r),r.dispatch=n,r=Zl(!1),l=$o.bind(null,vl,!1,r.queue),a={state:t,dispatch:null,action:e,pending:null},(r=Il()).queue=a,n=eo.bind(null,vl,a,l,n),a.dispatch=n,r.memoizedState=e,[t,n,!1]}function so(e){return uo(Fl(),bl,e)}function uo(e,t,n){if(t=Vl(e,t,oo)[0],e=Bl(Hl)[0],"object"==typeof t&&null!==t&&"function"==typeof t.then)try{var r=Ml(t)}catch(e){if(e===Va)throw Qa;throw e}else r=t;var a=(t=Fl()).queue,l=a.dispatch;return n!==t.memoizedState&&(vl.flags|=2048,po(9,{destroy:void 0,resource:void 0},co.bind(null,a,n),null)),[r,l,e]}function co(e,t){e.action=t}function fo(e){var t=Fl(),n=bl;if(null!==n)return uo(t,n,e);Fl(),t=t.memoizedState;var r=(n=Fl()).queue.dispatch;return n.memoizedState=e,[t,r,!1]}function po(e,t,n,r){return e={tag:e,create:n,deps:r,inst:t,next:null},null===(t=vl.updateQueue)&&(t={lastEffect:null,events:null,stores:null,memoCache:null},vl.updateQueue=t),null===(n=t.lastEffect)?t.lastEffect=e.next=e:(r=n.next,n.next=e,e.next=r,t.lastEffect=e),e}function ho(){return Fl().memoizedState}function mo(e,t,n,r){var a=Il();r=void 0===r?null:r,vl.flags|=e,a.memoizedState=po(1|t,{destroy:void 0,resource:void 0},n,r)}function go(e,t,n,r){var a=Fl();r=void 0===r?null:r;var l=a.memoizedState.inst;null!==bl&&null!==r&&Nl(r,bl.memoizedState.deps)?a.memoizedState=po(t,l,n,r):(vl.flags|=e,a.memoizedState=po(1|t,l,n,r))}function yo(e,t){mo(8390656,8,e,t)}function vo(e,t){go(2048,8,e,t)}function bo(e,t){return go(4,2,e,t)}function wo(e,t){return go(4,4,e,t)}function ko(e,t){if("function"==typeof t){e=e();var n=t(e);return function(){"function"==typeof n?n():t(null)}}if(null!=t)return e=e(),t.current=e,function(){t.current=null}}function So(e,t,n){n=null!=n?n.concat([e]):null,go(4,4,ko.bind(null,t,e),n)}function xo(){}function Co(e,t){var n=Fl();t=void 0===t?null:t;var r=n.memoizedState;return null!==t&&Nl(t,r[1])?r[0]:(n.memoizedState=[e,t],e)}function Eo(e,t){var n=Fl();t=void 0===t?null:t;var r=n.memoizedState;if(null!==t&&Nl(t,r[1]))return r[0];if(r=e(),xl){de(!0);try{e()}finally{de(!1)}}return n.memoizedState=[r,t],r}function Po(e,t,n){return void 0===n||1073741824&yl?e.memoizedState=t:(e.memoizedState=n,e=Ru(),vl.lanes|=e,fu|=e,n)}function zo(e,t,n,r){return Gn(n,t)?n:null!==fl.current?(e=Po(e,n,r),Gn(e,t)||(Pi=!0),e):42&yl?(e=Ru(),vl.lanes|=e,fu|=e,t):(Pi=!0,e.memoizedState=n)}function _o(e,t,n,r,a){var l=R.p;R.p=0!==l&&8>l?l:8;var o,i,s,u=j.T,c={};j.T=c,$o(e,!1,t,n);try{var d=a(),f=j.S;null!==f&&f(c,d),null!==d&&"object"==typeof d&&"function"==typeof d.then?Mo(e,t,(o=r,i=[],s={status:"pending",value:null,reason:null,then:function(e){i.push(e)}},d.then(function(){s.status="fulfilled",s.value=o;for(var e=0;e<i.length;e++)(0,i[e])(o)},function(e){for(s.status="rejected",s.reason=e,e=0;e<i.length;e++)(0,i[e])(void 0)}),s),ju()):Mo(e,t,r,ju())}catch(n){Mo(e,t,{then:function(){},status:"rejected",reason:n},ju())}finally{R.p=l,j.T=u}}function No(){}function To(e,t,n,r){if(5!==e.tag)throw Error(o(476));var a=Lo(e).queue;_o(e,a,t,A,null===n?No:function(){return Oo(e),n(r)})}function Lo(e){var t=e.memoizedState;if(null!==t)return t;var n={};return(t={memoizedState:A,baseState:A,baseQueue:null,queue:{pending:null,lanes:0,dispatch:null,lastRenderedReducer:Hl,lastRenderedState:A},next:null}).next={memoizedState:n,baseState:n,baseQueue:null,queue:{pending:null,lanes:0,dispatch:null,lastRenderedReducer:Hl,lastRenderedState:n},next:null},e.memoizedState=t,null!==(e=e.alternate)&&(e.memoizedState=t),t}function Oo(e){Mo(e,Lo(e).next.queue,{},ju())}function jo(){return Ea(qd)}function Ro(){return Fl().memoizedState}function Ao(){return Fl().memoizedState}function Do(e){for(var t=e.return;null!==t;){switch(t.tag){case 24:case 3:var n=ju(),r=al(t,e=rl(n),n);return null!==r&&(Au(r,0,n),ll(r,t,n)),t={cache:Oa()},void(e.payload=t)}t=t.return}}function Io(e,t,n){var r=ju();n={lane:r,revertLane:0,action:n,hasEagerState:!1,eagerState:null,next:null},Uo(e)?Ho(t,n):null!==(n=Nr(e,t,n,r))&&(Au(n,0,r),Bo(n,t,r))}function Fo(e,t,n){Mo(e,t,n,ju())}function Mo(e,t,n,r){var a={lane:r,revertLane:0,action:n,hasEagerState:!1,eagerState:null,next:null};if(Uo(e))Ho(t,a);else{var l=e.alternate;if(0===e.lanes&&(null===l||0===l.lanes)&&null!==(l=t.lastRenderedReducer))try{var o=t.lastRenderedState,i=l(o,n);if(a.hasEagerState=!0,a.eagerState=i,Gn(i,o))return _r(e,t,a,0),null===nu&&zr(),!1}catch(e){}if(null!==(n=Nr(e,t,a,r)))return Au(n,0,r),Bo(n,t,r),!0}return!1}function $o(e,t,n,r){if(r={lane:2,revertLane:_c(),action:r,hasEagerState:!1,eagerState:null,next:null},Uo(e)){if(t)throw Error(o(479))}else null!==(t=Nr(e,n,r,2))&&Au(t,0,2)}function Uo(e){var t=e.alternate;return e===vl||null!==t&&t===vl}function Ho(e,t){Sl=kl=!0;var n=e.pending;null===n?t.next=t:(t.next=n.next,n.next=t),e.pending=t}function Bo(e,t,n){if(4194048&n){var r=t.lanes;n|=r&=e.pendingLanes,t.lanes=n,Pe(e,n)}}var Vo={readContext:Ea,use:$l,useCallback:_l,useContext:_l,useEffect:_l,useImperativeHandle:_l,useLayoutEffect:_l,useInsertionEffect:_l,useMemo:_l,useReducer:_l,useRef:_l,useState:_l,useDebugValue:_l,useDeferredValue:_l,useTransition:_l,useSyncExternalStore:_l,useId:_l,useHostTransitionStatus:_l,useFormState:_l,useActionState:_l,useOptimistic:_l,useMemoCache:_l,useCacheRefresh:_l},Wo={readContext:Ea,use:$l,useCallback:function(e,t){return Il().memoizedState=[e,void 0===t?null:t],e},useContext:Ea,useEffect:yo,useImperativeHandle:function(e,t,n){n=null!=n?n.concat([e]):null,mo(4194308,4,ko.bind(null,t,e),n)},useLayoutEffect:function(e,t){return mo(4194308,4,e,t)},useInsertionEffect:function(e,t){mo(4,2,e,t)},useMemo:function(e,t){var n=Il();t=void 0===t?null:t;var r=e();if(xl){de(!0);try{e()}finally{de(!1)}}return n.memoizedState=[r,t],r},useReducer:function(e,t,n){var r=Il();if(void 0!==n){var a=n(t);if(xl){de(!0);try{n(t)}finally{de(!1)}}}else a=t;return r.memoizedState=r.baseState=a,e={pending:null,lanes:0,dispatch:null,lastRenderedReducer:e,lastRenderedState:a},r.queue=e,e=e.dispatch=Io.bind(null,vl,e),[r.memoizedState,e]},useRef:function(e){return e={current:e},Il().memoizedState=e},useState:function(e){var t=(e=Zl(e)).queue,n=Fo.bind(null,vl,t);return t.dispatch=n,[e.memoizedState,n]},useDebugValue:xo,useDeferredValue:function(e,t){return Po(Il(),e,t)},useTransition:function(){var e=Zl(!1);return e=_o.bind(null,vl,e.queue,!0,!1),Il().memoizedState=e,[!1,e]},useSyncExternalStore:function(e,t,n){var r=vl,a=Il();if(aa){if(void 0===n)throw Error(o(407));n=n()}else{if(n=t(),null===nu)throw Error(o(349));124&au||ql(r,t,n)}a.memoizedState=n;var l={value:n,getSnapshot:t};return a.queue=l,yo(Yl.bind(null,r,l,e),[e]),r.flags|=2048,po(9,{destroy:void 0,resource:void 0},Gl.bind(null,r,l,n,t),null),n},useId:function(){var e=Il(),t=nu.identifierPrefix;if(aa){var n=Xr;t="«"+t+"R"+(n=(Kr&~(1<<32-fe(Kr)-1)).toString(32)+n),0<(n=Cl++)&&(t+="H"+n.toString(32)),t+="»"}else t="«"+t+"r"+(n=zl++).toString(32)+"»";return e.memoizedState=t},useHostTransitionStatus:jo,useFormState:io,useActionState:io,useOptimistic:function(e){var t=Il();t.memoizedState=t.baseState=e;var n={pending:null,lanes:0,dispatch:null,lastRenderedReducer:null,lastRenderedState:null};return t.queue=n,t=$o.bind(null,vl,!0,n),n.dispatch=t,[e,t]},useMemoCache:Ul,useCacheRefresh:function(){return Il().memoizedState=Do.bind(null,vl)}},Qo={readContext:Ea,use:$l,useCallback:Co,useContext:Ea,useEffect:vo,useImperativeHandle:So,useInsertionEffect:bo,useLayoutEffect:wo,useMemo:Eo,useReducer:Bl,useRef:ho,useState:function(){return Bl(Hl)},useDebugValue:xo,useDeferredValue:function(e,t){return zo(Fl(),bl.memoizedState,e,t)},useTransition:function(){var e=Bl(Hl)[0],t=Fl().memoizedState;return["boolean"==typeof e?e:Ml(e),t]},useSyncExternalStore:Ql,useId:Ro,useHostTransitionStatus:jo,useFormState:so,useActionState:so,useOptimistic:function(e,t){return Jl(Fl(),0,e,t)},useMemoCache:Ul,useCacheRefresh:Ao},qo={readContext:Ea,use:$l,useCallback:Co,useContext:Ea,useEffect:vo,useImperativeHandle:So,useInsertionEffect:bo,useLayoutEffect:wo,useMemo:Eo,useReducer:Wl,useRef:ho,useState:function(){return Wl(Hl)},useDebugValue:xo,useDeferredValue:function(e,t){var n=Fl();return null===bl?Po(n,e,t):zo(n,bl.memoizedState,e,t)},useTransition:function(){var e=Wl(Hl)[0],t=Fl().memoizedState;return["boolean"==typeof e?e:Ml(e),t]},useSyncExternalStore:Ql,useId:Ro,useHostTransitionStatus:jo,useFormState:fo,useActionState:fo,useOptimistic:function(e,t){var n=Fl();return null!==bl?Jl(n,0,e,t):(n.baseState=e,[e,n.queue.dispatch])},useMemoCache:Ul,useCacheRefresh:Ao},Go=null,Yo=0;function Ko(e){var t=Yo;return Yo+=1,null===Go&&(Go=[]),Ka(Go,e,t)}function Xo(e,t){t=t.props.ref,e.ref=void 0!==t?t:null}function Zo(e,t){if(t.$$typeof===f)throw Error(o(525));throw e=Object.prototype.toString.call(t),Error(o(31,"[object Object]"===e?"object with keys {"+Object.keys(t).join(", ")+"}":e))}function Jo(e){return(0,e._init)(e._payload)}function ei(e){function t(t,n){if(e){var r=t.deletions;null===r?(t.deletions=[n],t.flags|=16):r.push(n)}}function n(n,r){if(!e)return null;for(;null!==r;)t(n,r),r=r.sibling;return null}function r(e){for(var t=new Map;null!==e;)null!==e.key?t.set(e.key,e):t.set(e.index,e),e=e.sibling;return t}function a(e,t){return(e=Ir(e,t)).index=0,e.sibling=null,e}function l(t,n,r){return t.index=r,e?null!==(r=t.alternate)?(r=r.index)<n?(t.flags|=67108866,n):r:(t.flags|=67108866,n):(t.flags|=1048576,n)}function i(t){return e&&null===t.alternate&&(t.flags|=67108866),t}function s(e,t,n,r){return null===t||6!==t.tag?((t=Ur(n,e.mode,r)).return=e,t):((t=a(t,n)).return=e,t)}function u(e,t,n,r){var l=n.type;return l===m?d(e,t,n.props.children,r,n.key):null!==t&&(t.elementType===l||"object"==typeof l&&null!==l&&l.$$typeof===E&&Jo(l)===t.type)?(Xo(t=a(t,n.props),n),t.return=e,t):(Xo(t=Mr(n.type,n.key,n.props,null,e.mode,r),n),t.return=e,t)}function c(e,t,n,r){return null===t||4!==t.tag||t.stateNode.containerInfo!==n.containerInfo||t.stateNode.implementation!==n.implementation?((t=Hr(n,e.mode,r)).return=e,t):((t=a(t,n.children||[])).return=e,t)}function d(e,t,n,r,l){return null===t||7!==t.tag?((t=$r(n,e.mode,r,l)).return=e,t):((t=a(t,n)).return=e,t)}function f(e,t,n){if("string"==typeof t&&""!==t||"number"==typeof t||"bigint"==typeof t)return(t=Ur(""+t,e.mode,n)).return=e,t;if("object"==typeof t&&null!==t){switch(t.$$typeof){case p:return Xo(n=Mr(t.type,t.key,t.props,null,e.mode,n),t),n.return=e,n;case h:return(t=Hr(t,e.mode,n)).return=e,t;case E:return f(e,t=(0,t._init)(t._payload),n)}if(O(t)||N(t))return(t=$r(t,e.mode,n,null)).return=e,t;if("function"==typeof t.then)return f(e,Ko(t),n);if(t.$$typeof===w)return f(e,Pa(e,t),n);Zo(e,t)}return null}function g(e,t,n,r){var a=null!==t?t.key:null;if("string"==typeof n&&""!==n||"number"==typeof n||"bigint"==typeof n)return null!==a?null:s(e,t,""+n,r);if("object"==typeof n&&null!==n){switch(n.$$typeof){case p:return n.key===a?u(e,t,n,r):null;case h:return n.key===a?c(e,t,n,r):null;case E:return g(e,t,n=(a=n._init)(n._payload),r)}if(O(n)||N(n))return null!==a?null:d(e,t,n,r,null);if("function"==typeof n.then)return g(e,t,Ko(n),r);if(n.$$typeof===w)return g(e,t,Pa(e,n),r);Zo(e,n)}return null}function y(e,t,n,r,a){if("string"==typeof r&&""!==r||"number"==typeof r||"bigint"==typeof r)return s(t,e=e.get(n)||null,""+r,a);if("object"==typeof r&&null!==r){switch(r.$$typeof){case p:return u(t,e=e.get(null===r.key?n:r.key)||null,r,a);case h:return c(t,e=e.get(null===r.key?n:r.key)||null,r,a);case E:return y(e,t,n,r=(0,r._init)(r._payload),a)}if(O(r)||N(r))return d(t,e=e.get(n)||null,r,a,null);if("function"==typeof r.then)return y(e,t,n,Ko(r),a);if(r.$$typeof===w)return y(e,t,n,Pa(t,r),a);Zo(t,r)}return null}function v(s,u,c,d){if("object"==typeof c&&null!==c&&c.type===m&&null===c.key&&(c=c.props.children),"object"==typeof c&&null!==c){switch(c.$$typeof){case p:e:{for(var b=c.key;null!==u;){if(u.key===b){if((b=c.type)===m){if(7===u.tag){n(s,u.sibling),(d=a(u,c.props.children)).return=s,s=d;break e}}else if(u.elementType===b||"object"==typeof b&&null!==b&&b.$$typeof===E&&Jo(b)===u.type){n(s,u.sibling),Xo(d=a(u,c.props),c),d.return=s,s=d;break e}n(s,u);break}t(s,u),u=u.sibling}c.type===m?((d=$r(c.props.children,s.mode,d,c.key)).return=s,s=d):(Xo(d=Mr(c.type,c.key,c.props,null,s.mode,d),c),d.return=s,s=d)}return i(s);case h:e:{for(b=c.key;null!==u;){if(u.key===b){if(4===u.tag&&u.stateNode.containerInfo===c.containerInfo&&u.stateNode.implementation===c.implementation){n(s,u.sibling),(d=a(u,c.children||[])).return=s,s=d;break e}n(s,u);break}t(s,u),u=u.sibling}(d=Hr(c,s.mode,d)).return=s,s=d}return i(s);case E:return v(s,u,c=(b=c._init)(c._payload),d)}if(O(c))return function(a,o,i,s){for(var u=null,c=null,d=o,p=o=0,h=null;null!==d&&p<i.length;p++){d.index>p?(h=d,d=null):h=d.sibling;var m=g(a,d,i[p],s);if(null===m){null===d&&(d=h);break}e&&d&&null===m.alternate&&t(a,d),o=l(m,o,p),null===c?u=m:c.sibling=m,c=m,d=h}if(p===i.length)return n(a,d),aa&&Zr(a,p),u;if(null===d){for(;p<i.length;p++)null!==(d=f(a,i[p],s))&&(o=l(d,o,p),null===c?u=d:c.sibling=d,c=d);return aa&&Zr(a,p),u}for(d=r(d);p<i.length;p++)null!==(h=y(d,a,p,i[p],s))&&(e&&null!==h.alternate&&d.delete(null===h.key?p:h.key),o=l(h,o,p),null===c?u=h:c.sibling=h,c=h);return e&&d.forEach(function(e){return t(a,e)}),aa&&Zr(a,p),u}(s,u,c,d);if(N(c)){if("function"!=typeof(b=N(c)))throw Error(o(150));return function(a,i,s,u){if(null==s)throw Error(o(151));for(var c=null,d=null,p=i,h=i=0,m=null,v=s.next();null!==p&&!v.done;h++,v=s.next()){p.index>h?(m=p,p=null):m=p.sibling;var b=g(a,p,v.value,u);if(null===b){null===p&&(p=m);break}e&&p&&null===b.alternate&&t(a,p),i=l(b,i,h),null===d?c=b:d.sibling=b,d=b,p=m}if(v.done)return n(a,p),aa&&Zr(a,h),c;if(null===p){for(;!v.done;h++,v=s.next())null!==(v=f(a,v.value,u))&&(i=l(v,i,h),null===d?c=v:d.sibling=v,d=v);return aa&&Zr(a,h),c}for(p=r(p);!v.done;h++,v=s.next())null!==(v=y(p,a,h,v.value,u))&&(e&&null!==v.alternate&&p.delete(null===v.key?h:v.key),i=l(v,i,h),null===d?c=v:d.sibling=v,d=v);return e&&p.forEach(function(e){return t(a,e)}),aa&&Zr(a,h),c}(s,u,c=b.call(c),d)}if("function"==typeof c.then)return v(s,u,Ko(c),d);if(c.$$typeof===w)return v(s,u,Pa(s,c),d);Zo(s,c)}return"string"==typeof c&&""!==c||"number"==typeof c||"bigint"==typeof c?(c=""+c,null!==u&&6===u.tag?(n(s,u.sibling),(d=a(u,c)).return=s,s=d):(n(s,u),(d=Ur(c,s.mode,d)).return=s,s=d),i(s)):n(s,u)}return function(e,t,n,r){try{Yo=0;var a=v(e,t,n,r);return Go=null,a}catch(t){if(t===Va||t===Qa)throw t;var l=Ar(29,t,null,e.mode);return l.lanes=r,l.return=e,l}}}var ti=ei(!0),ni=ei(!1),ri=F(null),ai=null;function li(e){var t=e.alternate;$(ui,1&ui.current),$(ri,e),null===ai&&(null===t||null!==fl.current||null!==t.memoizedState)&&(ai=e)}function oi(e){if(22===e.tag){if($(ui,ui.current),$(ri,e),null===ai){var t=e.alternate;null!==t&&null!==t.memoizedState&&(ai=e)}}else ii()}function ii(){$(ui,ui.current),$(ri,ri.current)}function si(e){M(ri),ai===e&&(ai=null),M(ui)}var ui=F(0);function ci(e){for(var t=e;null!==t;){if(13===t.tag){var n=t.memoizedState;if(null!==n&&(null===(n=n.dehydrated)||"$?"===n.data||md(n)))return t}else if(19===t.tag&&void 0!==t.memoizedProps.revealOrder){if(128&t.flags)return t}else if(null!==t.child){t.child.return=t,t=t.child;continue}if(t===e)break;for(;null===t.sibling;){if(null===t.return||t.return===e)return null;t=t.return}t.sibling.return=t.return,t=t.sibling}return null}function di(e,t,n,r){n=null==(n=n(r,t=e.memoizedState))?t:d({},t,n),e.memoizedState=n,0===e.lanes&&(e.updateQueue.baseState=n)}var fi={enqueueSetState:function(e,t,n){e=e._reactInternals;var r=ju(),a=rl(r);a.payload=t,null!=n&&(a.callback=n),null!==(t=al(e,a,r))&&(Au(t,0,r),ll(t,e,r))},enqueueReplaceState:function(e,t,n){e=e._reactInternals;var r=ju(),a=rl(r);a.tag=1,a.payload=t,null!=n&&(a.callback=n),null!==(t=al(e,a,r))&&(Au(t,0,r),ll(t,e,r))},enqueueForceUpdate:function(e,t){e=e._reactInternals;var n=ju(),r=rl(n);r.tag=2,null!=t&&(r.callback=t),null!==(t=al(e,r,n))&&(Au(t,0,n),ll(t,e,n))}};function pi(e,t,n,r,a,l,o){return"function"==typeof(e=e.stateNode).shouldComponentUpdate?e.shouldComponentUpdate(r,l,o):!(t.prototype&&t.prototype.isPureReactComponent&&Yn(n,r)&&Yn(a,l))}function hi(e,t,n,r){e=t.state,"function"==typeof t.componentWillReceiveProps&&t.componentWillReceiveProps(n,r),"function"==typeof t.UNSAFE_componentWillReceiveProps&&t.UNSAFE_componentWillReceiveProps(n,r),t.state!==e&&fi.enqueueReplaceState(t,t.state,null)}function mi(e,t){var n=t;if("ref"in t)for(var r in n={},t)"ref"!==r&&(n[r]=t[r]);if(e=e.defaultProps)for(var a in n===t&&(n=d({},n)),e)void 0===n[a]&&(n[a]=e[a]);return n}var gi="function"==typeof reportError?reportError:function(e){if("object"==typeof window&&"function"==typeof window.ErrorEvent){var t=new window.ErrorEvent("error",{bubbles:!0,cancelable:!0,message:"object"==typeof e&&null!==e&&"string"==typeof e.message?String(e.message):String(e),error:e});if(!window.dispatchEvent(t))return}else if("object"==typeof process&&"function"==typeof process.emit)return void process.emit("uncaughtException",e);console.error(e)};function yi(e){gi(e)}function vi(e){console.error(e)}function bi(e){gi(e)}function wi(e,t){try{(0,e.onUncaughtError)(t.value,{componentStack:t.stack})}catch(e){setTimeout(function(){throw e})}}function ki(e,t,n){try{(0,e.onCaughtError)(n.value,{componentStack:n.stack,errorBoundary:1===t.tag?t.stateNode:null})}catch(e){setTimeout(function(){throw e})}}function Si(e,t,n){return(n=rl(n)).tag=3,n.payload={element:null},n.callback=function(){wi(e,t)},n}function xi(e){return(e=rl(e)).tag=3,e}function Ci(e,t,n,r){var a=n.type.getDerivedStateFromError;if("function"==typeof a){var l=r.value;e.payload=function(){return a(l)},e.callback=function(){ki(t,n,r)}}var o=n.stateNode;null!==o&&"function"==typeof o.componentDidCatch&&(e.callback=function(){ki(t,n,r),"function"!=typeof a&&(null===xu?xu=new Set([this]):xu.add(this));var e=r.stack;this.componentDidCatch(r.value,{componentStack:null!==e?e:""})})}var Ei=Error(o(461)),Pi=!1;function zi(e,t,n,r){t.child=null===e?ni(t,null,n,r):ti(t,e.child,n,r)}function _i(e,t,n,r,a){n=n.render;var l=t.ref;if("ref"in r){var o={};for(var i in r)"ref"!==i&&(o[i]=r[i])}else o=r;return Ca(t),r=Tl(e,t,n,o,l,a),i=Rl(),null===e||Pi?(aa&&i&&ea(t),t.flags|=1,zi(e,t,r,a),t.child):(Al(e,t,a),Gi(e,t,a))}function Ni(e,t,n,r,a){if(null===e){var l=n.type;return"function"!=typeof l||Dr(l)||void 0!==l.defaultProps||null!==n.compare?((e=Mr(n.type,null,r,t,t.mode,a)).ref=t.ref,e.return=t,t.child=e):(t.tag=15,t.type=l,Ti(e,t,l,r,a))}if(l=e.child,!Yi(e,a)){var o=l.memoizedProps;if((n=null!==(n=n.compare)?n:Yn)(o,r)&&e.ref===t.ref)return Gi(e,t,a)}return t.flags|=1,(e=Ir(l,r)).ref=t.ref,e.return=t,t.child=e}function Ti(e,t,n,r,a){if(null!==e){var l=e.memoizedProps;if(Yn(l,r)&&e.ref===t.ref){if(Pi=!1,t.pendingProps=r=l,!Yi(e,a))return t.lanes=e.lanes,Gi(e,t,a);131072&e.flags&&(Pi=!0)}}return Ri(e,t,n,r,a)}function Li(e,t,n){var r=t.pendingProps,a=r.children,l=null!==e?e.memoizedState:null;if("hidden"===r.mode){if(128&t.flags){if(r=null!==l?l.baseLanes|n:n,null!==e){for(a=t.child=e.child,l=0;null!==a;)l=l|a.lanes|a.childLanes,a=a.sibling;t.childLanes=l&~r}else t.childLanes=0,t.child=null;return Oi(e,t,r,n)}if(!(536870912&n))return t.lanes=t.childLanes=536870912,Oi(e,t,null!==l?l.baseLanes|n:n,n);t.memoizedState={baseLanes:0,cachePool:null},null!==e&&Ha(0,null!==l?l.cachePool:null),null!==l?hl(t,l):ml(),oi(t)}else null!==l?(Ha(0,l.cachePool),hl(t,l),ii(),t.memoizedState=null):(null!==e&&Ha(0,null),ml(),ii());return zi(e,t,a,n),t.child}function Oi(e,t,n,r){var a=Ua();return a=null===a?null:{parent:La._currentValue,pool:a},t.memoizedState={baseLanes:n,cachePool:a},null!==e&&Ha(0,null),ml(),oi(t),null!==e&&Sa(e,t,r,!0),null}function ji(e,t){var n=t.ref;if(null===n)null!==e&&null!==e.ref&&(t.flags|=4194816);else{if("function"!=typeof n&&"object"!=typeof n)throw Error(o(284));null!==e&&e.ref===n||(t.flags|=4194816)}}function Ri(e,t,n,r,a){return Ca(t),n=Tl(e,t,n,r,void 0,a),r=Rl(),null===e||Pi?(aa&&r&&ea(t),t.flags|=1,zi(e,t,n,a),t.child):(Al(e,t,a),Gi(e,t,a))}function Ai(e,t,n,r,a,l){return Ca(t),t.updateQueue=null,n=Ol(t,r,n,a),Ll(e),r=Rl(),null===e||Pi?(aa&&r&&ea(t),t.flags|=1,zi(e,t,n,l),t.child):(Al(e,t,l),Gi(e,t,l))}function Di(e,t,n,r,a){if(Ca(t),null===t.stateNode){var l=jr,o=n.contextType;"object"==typeof o&&null!==o&&(l=Ea(o)),l=new n(r,l),t.memoizedState=null!==l.state&&void 0!==l.state?l.state:null,l.updater=fi,t.stateNode=l,l._reactInternals=t,(l=t.stateNode).props=r,l.state=t.memoizedState,l.refs={},tl(t),o=n.contextType,l.context="object"==typeof o&&null!==o?Ea(o):jr,l.state=t.memoizedState,"function"==typeof(o=n.getDerivedStateFromProps)&&(di(t,n,o,r),l.state=t.memoizedState),"function"==typeof n.getDerivedStateFromProps||"function"==typeof l.getSnapshotBeforeUpdate||"function"!=typeof l.UNSAFE_componentWillMount&&"function"!=typeof l.componentWillMount||(o=l.state,"function"==typeof l.componentWillMount&&l.componentWillMount(),"function"==typeof l.UNSAFE_componentWillMount&&l.UNSAFE_componentWillMount(),o!==l.state&&fi.enqueueReplaceState(l,l.state,null),ul(t,r,l,a),sl(),l.state=t.memoizedState),"function"==typeof l.componentDidMount&&(t.flags|=4194308),r=!0}else if(null===e){l=t.stateNode;var i=t.memoizedProps,s=mi(n,i);l.props=s;var u=l.context,c=n.contextType;o=jr,"object"==typeof c&&null!==c&&(o=Ea(c));var d=n.getDerivedStateFromProps;c="function"==typeof d||"function"==typeof l.getSnapshotBeforeUpdate,i=t.pendingProps!==i,c||"function"!=typeof l.UNSAFE_componentWillReceiveProps&&"function"!=typeof l.componentWillReceiveProps||(i||u!==o)&&hi(t,l,r,o),el=!1;var f=t.memoizedState;l.state=f,ul(t,r,l,a),sl(),u=t.memoizedState,i||f!==u||el?("function"==typeof d&&(di(t,n,d,r),u=t.memoizedState),(s=el||pi(t,n,s,r,f,u,o))?(c||"function"!=typeof l.UNSAFE_componentWillMount&&"function"!=typeof l.componentWillMount||("function"==typeof l.componentWillMount&&l.componentWillMount(),"function"==typeof l.UNSAFE_componentWillMount&&l.UNSAFE_componentWillMount()),"function"==typeof l.componentDidMount&&(t.flags|=4194308)):("function"==typeof l.componentDidMount&&(t.flags|=4194308),t.memoizedProps=r,t.memoizedState=u),l.props=r,l.state=u,l.context=o,r=s):("function"==typeof l.componentDidMount&&(t.flags|=4194308),r=!1)}else{l=t.stateNode,nl(e,t),c=mi(n,o=t.memoizedProps),l.props=c,d=t.pendingProps,f=l.context,u=n.contextType,s=jr,"object"==typeof u&&null!==u&&(s=Ea(u)),(u="function"==typeof(i=n.getDerivedStateFromProps)||"function"==typeof l.getSnapshotBeforeUpdate)||"function"!=typeof l.UNSAFE_componentWillReceiveProps&&"function"!=typeof l.componentWillReceiveProps||(o!==d||f!==s)&&hi(t,l,r,s),el=!1,f=t.memoizedState,l.state=f,ul(t,r,l,a),sl();var p=t.memoizedState;o!==d||f!==p||el||null!==e&&null!==e.dependencies&&xa(e.dependencies)?("function"==typeof i&&(di(t,n,i,r),p=t.memoizedState),(c=el||pi(t,n,c,r,f,p,s)||null!==e&&null!==e.dependencies&&xa(e.dependencies))?(u||"function"!=typeof l.UNSAFE_componentWillUpdate&&"function"!=typeof l.componentWillUpdate||("function"==typeof l.componentWillUpdate&&l.componentWillUpdate(r,p,s),"function"==typeof l.UNSAFE_componentWillUpdate&&l.UNSAFE_componentWillUpdate(r,p,s)),"function"==typeof l.componentDidUpdate&&(t.flags|=4),"function"==typeof l.getSnapshotBeforeUpdate&&(t.flags|=1024)):("function"!=typeof l.componentDidUpdate||o===e.memoizedProps&&f===e.memoizedState||(t.flags|=4),"function"!=typeof l.getSnapshotBeforeUpdate||o===e.memoizedProps&&f===e.memoizedState||(t.flags|=1024),t.memoizedProps=r,t.memoizedState=p),l.props=r,l.state=p,l.context=s,r=c):("function"!=typeof l.componentDidUpdate||o===e.memoizedProps&&f===e.memoizedState||(t.flags|=4),"function"!=typeof l.getSnapshotBeforeUpdate||o===e.memoizedProps&&f===e.memoizedState||(t.flags|=1024),r=!1)}return l=r,ji(e,t),r=!!(128&t.flags),l||r?(l=t.stateNode,n=r&&"function"!=typeof n.getDerivedStateFromError?null:l.render(),t.flags|=1,null!==e&&r?(t.child=ti(t,e.child,null,a),t.child=ti(t,null,n,a)):zi(e,t,n,a),t.memoizedState=l.state,e=t.child):e=Gi(e,t,a),e}function Ii(e,t,n,r){return fa(),t.flags|=256,zi(e,t,n,r),t.child}var Fi={dehydrated:null,treeContext:null,retryLane:0,hydrationErrors:null};function Mi(e){return{baseLanes:e,cachePool:Ba()}}function $i(e,t,n){return e=null!==e?e.childLanes&~n:0,t&&(e|=mu),e}function Ui(e,t,n){var r,a=t.pendingProps,l=!1,i=!!(128&t.flags);if((r=i)||(r=(null===e||null!==e.memoizedState)&&!!(2&ui.current)),r&&(l=!0,t.flags&=-129),r=!!(32&t.flags),t.flags&=-33,null===e){if(aa){if(l?li(t):ii(),aa){var s,u=ra;if(s=u){e:{for(s=u,u=oa;8!==s.nodeType;){if(!u){u=null;break e}if(null===(s=gd(s.nextSibling))){u=null;break e}}u=s}null!==u?(t.memoizedState={dehydrated:u,treeContext:null!==Yr?{id:Kr,overflow:Xr}:null,retryLane:536870912,hydrationErrors:null},(s=Ar(18,null,null,0)).stateNode=u,s.return=t,t.child=s,na=t,ra=null,s=!0):s=!1}s||sa(t)}if(null!==(u=t.memoizedState)&&null!==(u=u.dehydrated))return md(u)?t.lanes=32:t.lanes=536870912,null;si(t)}return u=a.children,a=a.fallback,l?(ii(),u=Bi({mode:"hidden",children:u},l=t.mode),a=$r(a,l,n,null),u.return=t,a.return=t,u.sibling=a,t.child=u,(l=t.child).memoizedState=Mi(n),l.childLanes=$i(e,r,n),t.memoizedState=Fi,a):(li(t),Hi(t,u))}if(null!==(s=e.memoizedState)&&null!==(u=s.dehydrated)){if(i)256&t.flags?(li(t),t.flags&=-257,t=Vi(e,t,n)):null!==t.memoizedState?(ii(),t.child=e.child,t.flags|=128,t=null):(ii(),l=a.fallback,u=t.mode,a=Bi({mode:"visible",children:a.children},u),(l=$r(l,u,n,null)).flags|=2,a.return=t,l.return=t,a.sibling=l,t.child=a,ti(t,e.child,null,n),(a=t.child).memoizedState=Mi(n),a.childLanes=$i(e,r,n),t.memoizedState=Fi,t=l);else if(li(t),md(u)){if(r=u.nextSibling&&u.nextSibling.dataset)var c=r.dgst;r=c,(a=Error(o(419))).stack="",a.digest=r,ha({value:a,source:null,stack:null}),t=Vi(e,t,n)}else if(Pi||Sa(e,t,n,!1),r=0!==(n&e.childLanes),Pi||r){if(null!==(r=nu)&&0!==(a=0!==((a=42&(a=n&-n)?1:ze(a))&(r.suspendedLanes|n))?0:a)&&a!==s.retryLane)throw s.retryLane=a,Tr(e,a),Au(r,0,a),Ei;"$?"===u.data||Qu(),t=Vi(e,t,n)}else"$?"===u.data?(t.flags|=192,t.child=e.child,t=null):(e=s.treeContext,ra=gd(u.nextSibling),na=t,aa=!0,la=null,oa=!1,null!==e&&(qr[Gr++]=Kr,qr[Gr++]=Xr,qr[Gr++]=Yr,Kr=e.id,Xr=e.overflow,Yr=t),(t=Hi(t,a.children)).flags|=4096);return t}return l?(ii(),l=a.fallback,u=t.mode,c=(s=e.child).sibling,(a=Ir(s,{mode:"hidden",children:a.children})).subtreeFlags=65011712&s.subtreeFlags,null!==c?l=Ir(c,l):(l=$r(l,u,n,null)).flags|=2,l.return=t,a.return=t,a.sibling=l,t.child=a,a=l,l=t.child,null===(u=e.child.memoizedState)?u=Mi(n):(null!==(s=u.cachePool)?(c=La._currentValue,s=s.parent!==c?{parent:c,pool:c}:s):s=Ba(),u={baseLanes:u.baseLanes|n,cachePool:s}),l.memoizedState=u,l.childLanes=$i(e,r,n),t.memoizedState=Fi,a):(li(t),e=(n=e.child).sibling,(n=Ir(n,{mode:"visible",children:a.children})).return=t,n.sibling=null,null!==e&&(null===(r=t.deletions)?(t.deletions=[e],t.flags|=16):r.push(e)),t.child=n,t.memoizedState=null,n)}function Hi(e,t){return(t=Bi({mode:"visible",children:t},e.mode)).return=e,e.child=t}function Bi(e,t){return(e=Ar(22,e,null,t)).lanes=0,e.stateNode={_visibility:1,_pendingMarkers:null,_retryCache:null,_transitions:null},e}function Vi(e,t,n){return ti(t,e.child,null,n),(e=Hi(t,t.pendingProps.children)).flags|=2,t.memoizedState=null,e}function Wi(e,t,n){e.lanes|=t;var r=e.alternate;null!==r&&(r.lanes|=t),wa(e.return,t,n)}function Qi(e,t,n,r,a){var l=e.memoizedState;null===l?e.memoizedState={isBackwards:t,rendering:null,renderingStartTime:0,last:r,tail:n,tailMode:a}:(l.isBackwards=t,l.rendering=null,l.renderingStartTime=0,l.last=r,l.tail=n,l.tailMode=a)}function qi(e,t,n){var r=t.pendingProps,a=r.revealOrder,l=r.tail;if(zi(e,t,r.children,n),2&(r=ui.current))r=1&r|2,t.flags|=128;else{if(null!==e&&128&e.flags)e:for(e=t.child;null!==e;){if(13===e.tag)null!==e.memoizedState&&Wi(e,n,t);else if(19===e.tag)Wi(e,n,t);else if(null!==e.child){e.child.return=e,e=e.child;continue}if(e===t)break e;for(;null===e.sibling;){if(null===e.return||e.return===t)break e;e=e.return}e.sibling.return=e.return,e=e.sibling}r&=1}switch($(ui,r),a){case"forwards":for(n=t.child,a=null;null!==n;)null!==(e=n.alternate)&&null===ci(e)&&(a=n),n=n.sibling;null===(n=a)?(a=t.child,t.child=null):(a=n.sibling,n.sibling=null),Qi(t,!1,a,n,l);break;case"backwards":for(n=null,a=t.child,t.child=null;null!==a;){if(null!==(e=a.alternate)&&null===ci(e)){t.child=a;break}e=a.sibling,a.sibling=n,n=a,a=e}Qi(t,!0,n,null,l);break;case"together":Qi(t,!1,null,null,void 0);break;default:t.memoizedState=null}return t.child}function Gi(e,t,n){if(null!==e&&(t.dependencies=e.dependencies),fu|=t.lanes,0===(n&t.childLanes)){if(null===e)return null;if(Sa(e,t,n,!1),0===(n&t.childLanes))return null}if(null!==e&&t.child!==e.child)throw Error(o(153));if(null!==t.child){for(n=Ir(e=t.child,e.pendingProps),t.child=n,n.return=t;null!==e.sibling;)e=e.sibling,(n=n.sibling=Ir(e,e.pendingProps)).return=t;n.sibling=null}return t.child}function Yi(e,t){return 0!==(e.lanes&t)||!(null===(e=e.dependencies)||!xa(e))}function Ki(e,t,n){if(null!==e)if(e.memoizedProps!==t.pendingProps)Pi=!0;else{if(!(Yi(e,n)||128&t.flags))return Pi=!1,function(e,t,n){switch(t.tag){case 3:W(t,t.stateNode.containerInfo),va(0,La,e.memoizedState.cache),fa();break;case 27:case 5:q(t);break;case 4:W(t,t.stateNode.containerInfo);break;case 10:va(0,t.type,t.memoizedProps.value);break;case 13:var r=t.memoizedState;if(null!==r)return null!==r.dehydrated?(li(t),t.flags|=128,null):0!==(n&t.child.childLanes)?Ui(e,t,n):(li(t),null!==(e=Gi(e,t,n))?e.sibling:null);li(t);break;case 19:var a=!!(128&e.flags);if((r=0!==(n&t.childLanes))||(Sa(e,t,n,!1),r=0!==(n&t.childLanes)),a){if(r)return qi(e,t,n);t.flags|=128}if(null!==(a=t.memoizedState)&&(a.rendering=null,a.tail=null,a.lastEffect=null),$(ui,ui.current),r)break;return null;case 22:case 23:return t.lanes=0,Li(e,t,n);case 24:va(0,La,e.memoizedState.cache)}return Gi(e,t,n)}(e,t,n);Pi=!!(131072&e.flags)}else Pi=!1,aa&&1048576&t.flags&&Jr(t,Qr,t.index);switch(t.lanes=0,t.tag){case 16:e:{e=t.pendingProps;var r=t.elementType,a=r._init;if(r=a(r._payload),t.type=r,"function"!=typeof r){if(null!=r){if((a=r.$$typeof)===k){t.tag=11,t=_i(null,t,r,e,n);break e}if(a===C){t.tag=14,t=Ni(null,t,r,e,n);break e}}throw t=L(r)||r,Error(o(306,t,""))}Dr(r)?(e=mi(r,e),t.tag=1,t=Di(null,t,r,e,n)):(t.tag=0,t=Ri(null,t,r,e,n))}return t;case 0:return Ri(e,t,t.type,t.pendingProps,n);case 1:return Di(e,t,r=t.type,a=mi(r,t.pendingProps),n);case 3:e:{if(W(t,t.stateNode.containerInfo),null===e)throw Error(o(387));r=t.pendingProps;var l=t.memoizedState;a=l.element,nl(e,t),ul(t,r,null,n);var i=t.memoizedState;if(r=i.cache,va(0,La,r),r!==l.cache&&ka(t,[La],n,!0),sl(),r=i.element,l.isDehydrated){if(l={element:r,isDehydrated:!1,cache:i.cache},t.updateQueue.baseState=l,t.memoizedState=l,256&t.flags){t=Ii(e,t,r,n);break e}if(r!==a){ha(a=xr(Error(o(424)),t)),t=Ii(e,t,r,n);break e}for(e=9===(e=t.stateNode.containerInfo).nodeType?e.body:"HTML"===e.nodeName?e.ownerDocument.body:e,ra=gd(e.firstChild),na=t,aa=!0,la=null,oa=!0,n=ni(t,null,r,n),t.child=n;n;)n.flags=-3&n.flags|4096,n=n.sibling}else{if(fa(),r===a){t=Gi(e,t,n);break e}zi(e,t,r,n)}t=t.child}return t;case 26:return ji(e,t),null===e?(n=zd(t.type,null,t.pendingProps,null))?t.memoizedState=n:aa||(n=t.type,e=t.pendingProps,(r=nd(B.current).createElement(n))[Le]=t,r[Oe]=e,Jc(r,n,e),Ve(r),t.stateNode=r):t.memoizedState=zd(t.type,e.memoizedProps,t.pendingProps,e.memoizedState),null;case 27:return q(t),null===e&&aa&&(r=t.stateNode=bd(t.type,t.pendingProps,B.current),na=t,oa=!0,a=ra,fd(t.type)?(yd=a,ra=gd(r.firstChild)):ra=a),zi(e,t,t.pendingProps.children,n),ji(e,t),null===e&&(t.flags|=4194304),t.child;case 5:return null===e&&aa&&((a=r=ra)&&(null!==(r=function(e,t,n,r){for(;1===e.nodeType;){var a=n;if(e.nodeName.toLowerCase()!==t.toLowerCase()){if(!r&&("INPUT"!==e.nodeName||"hidden"!==e.type))break}else if(r){if(!e[Fe])switch(t){case"meta":if(!e.hasAttribute("itemprop"))break;return e;case"link":if("stylesheet"===(l=e.getAttribute("rel"))&&e.hasAttribute("data-precedence"))break;if(l!==a.rel||e.getAttribute("href")!==(null==a.href||""===a.href?null:a.href)||e.getAttribute("crossorigin")!==(null==a.crossOrigin?null:a.crossOrigin)||e.getAttribute("title")!==(null==a.title?null:a.title))break;return e;case"style":if(e.hasAttribute("data-precedence"))break;return e;case"script":if(((l=e.getAttribute("src"))!==(null==a.src?null:a.src)||e.getAttribute("type")!==(null==a.type?null:a.type)||e.getAttribute("crossorigin")!==(null==a.crossOrigin?null:a.crossOrigin))&&l&&e.hasAttribute("async")&&!e.hasAttribute("itemprop"))break;return e;default:return e}}else{if("input"!==t||"hidden"!==e.type)return e;var l=null==a.name?null:""+a.name;if("hidden"===a.type&&e.getAttribute("name")===l)return e}if(null===(e=gd(e.nextSibling)))break}return null}(r,t.type,t.pendingProps,oa))?(t.stateNode=r,na=t,ra=gd(r.firstChild),oa=!1,a=!0):a=!1),a||sa(t)),q(t),a=t.type,l=t.pendingProps,i=null!==e?e.memoizedProps:null,r=l.children,ld(a,l)?r=null:null!==i&&ld(a,i)&&(t.flags|=32),null!==t.memoizedState&&(a=Tl(e,t,jl,null,null,n),qd._currentValue=a),ji(e,t),zi(e,t,r,n),t.child;case 6:return null===e&&aa&&((e=n=ra)&&(null!==(n=function(e,t,n){if(""===t)return null;for(;3!==e.nodeType;){if((1!==e.nodeType||"INPUT"!==e.nodeName||"hidden"!==e.type)&&!n)return null;if(null===(e=gd(e.nextSibling)))return null}return e}(n,t.pendingProps,oa))?(t.stateNode=n,na=t,ra=null,e=!0):e=!1),e||sa(t)),null;case 13:return Ui(e,t,n);case 4:return W(t,t.stateNode.containerInfo),r=t.pendingProps,null===e?t.child=ti(t,null,r,n):zi(e,t,r,n),t.child;case 11:return _i(e,t,t.type,t.pendingProps,n);case 7:return zi(e,t,t.pendingProps,n),t.child;case 8:case 12:return zi(e,t,t.pendingProps.children,n),t.child;case 10:return r=t.pendingProps,va(0,t.type,r.value),zi(e,t,r.children,n),t.child;case 9:return a=t.type._context,r=t.pendingProps.children,Ca(t),r=r(a=Ea(a)),t.flags|=1,zi(e,t,r,n),t.child;case 14:return Ni(e,t,t.type,t.pendingProps,n);case 15:return Ti(e,t,t.type,t.pendingProps,n);case 19:return qi(e,t,n);case 31:return r=t.pendingProps,n=t.mode,r={mode:r.mode,children:r.children},null===e?((n=Bi(r,n)).ref=t.ref,t.child=n,n.return=t,t=n):((n=Ir(e.child,r)).ref=t.ref,t.child=n,n.return=t,t=n),t;case 22:return Li(e,t,n);case 24:return Ca(t),r=Ea(La),null===e?(null===(a=Ua())&&(a=nu,l=Oa(),a.pooledCache=l,l.refCount++,null!==l&&(a.pooledCacheLanes|=n),a=l),t.memoizedState={parent:r,cache:a},tl(t),va(0,La,a)):(0!==(e.lanes&n)&&(nl(e,t),ul(t,null,null,n),sl()),a=e.memoizedState,l=t.memoizedState,a.parent!==r?(a={parent:r,cache:r},t.memoizedState=a,0===t.lanes&&(t.memoizedState=t.updateQueue.baseState=a),va(0,La,r)):(r=l.cache,va(0,La,r),r!==a.cache&&ka(t,[La],n,!0))),zi(e,t,t.pendingProps.children,n),t.child;case 29:throw t.pendingProps}throw Error(o(156,t.tag))}function Xi(e){e.flags|=4}function Zi(e,t){if("stylesheet"!==t.type||4&t.state.loading)e.flags&=-16777217;else if(e.flags|=16777216,!$d(t)){if(null!==(t=ri.current)&&((4194048&au)===au?null!==ai:(62914560&au)!==au&&!(536870912&au)||t!==ai))throw Xa=qa,Wa;e.flags|=8192}}function Ji(e,t){null!==t&&(e.flags|=4),16384&e.flags&&(t=22!==e.tag?Se():536870912,e.lanes|=t,gu|=t)}function es(e,t){if(!aa)switch(e.tailMode){case"hidden":t=e.tail;for(var n=null;null!==t;)null!==t.alternate&&(n=t),t=t.sibling;null===n?e.tail=null:n.sibling=null;break;case"collapsed":n=e.tail;for(var r=null;null!==n;)null!==n.alternate&&(r=n),n=n.sibling;null===r?t||null===e.tail?e.tail=null:e.tail.sibling=null:r.sibling=null}}function ts(e){var t=null!==e.alternate&&e.alternate.child===e.child,n=0,r=0;if(t)for(var a=e.child;null!==a;)n|=a.lanes|a.childLanes,r|=65011712&a.subtreeFlags,r|=65011712&a.flags,a.return=e,a=a.sibling;else for(a=e.child;null!==a;)n|=a.lanes|a.childLanes,r|=a.subtreeFlags,r|=a.flags,a.return=e,a=a.sibling;return e.subtreeFlags|=r,e.childLanes=n,t}function ns(e,t,n){var r=t.pendingProps;switch(ta(t),t.tag){case 31:case 16:case 15:case 0:case 11:case 7:case 8:case 12:case 9:case 14:case 1:return ts(t),null;case 3:return n=t.stateNode,r=null,null!==e&&(r=e.memoizedState.cache),t.memoizedState.cache!==r&&(t.flags|=2048),ba(La),Q(),n.pendingContext&&(n.context=n.pendingContext,n.pendingContext=null),null!==e&&null!==e.child||(da(t)?Xi(t):null===e||e.memoizedState.isDehydrated&&!(256&t.flags)||(t.flags|=1024,pa())),ts(t),null;case 26:return n=t.memoizedState,null===e?(Xi(t),null!==n?(ts(t),Zi(t,n)):(ts(t),t.flags&=-16777217)):n?n!==e.memoizedState?(Xi(t),ts(t),Zi(t,n)):(ts(t),t.flags&=-16777217):(e.memoizedProps!==r&&Xi(t),ts(t),t.flags&=-16777217),null;case 27:G(t),n=B.current;var a=t.type;if(null!==e&&null!=t.stateNode)e.memoizedProps!==r&&Xi(t);else{if(!r){if(null===t.stateNode)throw Error(o(166));return ts(t),null}e=U.current,da(t)?ua(t):(e=bd(a,r,n),t.stateNode=e,Xi(t))}return ts(t),null;case 5:if(G(t),n=t.type,null!==e&&null!=t.stateNode)e.memoizedProps!==r&&Xi(t);else{if(!r){if(null===t.stateNode)throw Error(o(166));return ts(t),null}if(e=U.current,da(t))ua(t);else{switch(a=nd(B.current),e){case 1:e=a.createElementNS("http://www.w3.org/2000/svg",n);break;case 2:e=a.createElementNS("http://www.w3.org/1998/Math/MathML",n);break;default:switch(n){case"svg":e=a.createElementNS("http://www.w3.org/2000/svg",n);break;case"math":e=a.createElementNS("http://www.w3.org/1998/Math/MathML",n);break;case"script":(e=a.createElement("div")).innerHTML="<script><\/script>",e=e.removeChild(e.firstChild);break;case"select":e="string"==typeof r.is?a.createElement("select",{is:r.is}):a.createElement("select"),r.multiple?e.multiple=!0:r.size&&(e.size=r.size);break;default:e="string"==typeof r.is?a.createElement(n,{is:r.is}):a.createElement(n)}}e[Le]=t,e[Oe]=r;e:for(a=t.child;null!==a;){if(5===a.tag||6===a.tag)e.appendChild(a.stateNode);else if(4!==a.tag&&27!==a.tag&&null!==a.child){a.child.return=a,a=a.child;continue}if(a===t)break e;for(;null===a.sibling;){if(null===a.return||a.return===t)break e;a=a.return}a.sibling.return=a.return,a=a.sibling}t.stateNode=e;e:switch(Jc(e,n,r),n){case"button":case"input":case"select":case"textarea":e=!!r.autoFocus;break e;case"img":e=!0;break e;default:e=!1}e&&Xi(t)}}return ts(t),t.flags&=-16777217,null;case 6:if(e&&null!=t.stateNode)e.memoizedProps!==r&&Xi(t);else{if("string"!=typeof r&&null===t.stateNode)throw Error(o(166));if(e=B.current,da(t)){if(e=t.stateNode,n=t.memoizedProps,r=null,null!==(a=na))switch(a.tag){case 27:case 5:r=a.memoizedProps}e[Le]=t,(e=!!(e.nodeValue===n||null!==r&&!0===r.suppressHydrationWarning||Yc(e.nodeValue,n)))||sa(t)}else(e=nd(e).createTextNode(r))[Le]=t,t.stateNode=e}return ts(t),null;case 13:if(r=t.memoizedState,null===e||null!==e.memoizedState&&null!==e.memoizedState.dehydrated){if(a=da(t),null!==r&&null!==r.dehydrated){if(null===e){if(!a)throw Error(o(318));if(!(a=null!==(a=t.memoizedState)?a.dehydrated:null))throw Error(o(317));a[Le]=t}else fa(),!(128&t.flags)&&(t.memoizedState=null),t.flags|=4;ts(t),a=!1}else a=pa(),null!==e&&null!==e.memoizedState&&(e.memoizedState.hydrationErrors=a),a=!0;if(!a)return 256&t.flags?(si(t),t):(si(t),null)}if(si(t),128&t.flags)return t.lanes=n,t;if(n=null!==r,e=null!==e&&null!==e.memoizedState,n){a=null,null!==(r=t.child).alternate&&null!==r.alternate.memoizedState&&null!==r.alternate.memoizedState.cachePool&&(a=r.alternate.memoizedState.cachePool.pool);var l=null;null!==r.memoizedState&&null!==r.memoizedState.cachePool&&(l=r.memoizedState.cachePool.pool),l!==a&&(r.flags|=2048)}return n!==e&&n&&(t.child.flags|=8192),Ji(t,t.updateQueue),ts(t),null;case 4:return Q(),null===e&&Mc(t.stateNode.containerInfo),ts(t),null;case 10:return ba(t.type),ts(t),null;case 19:if(M(ui),null===(a=t.memoizedState))return ts(t),null;if(r=!!(128&t.flags),null===(l=a.rendering))if(r)es(a,!1);else{if(0!==du||null!==e&&128&e.flags)for(e=t.child;null!==e;){if(null!==(l=ci(e))){for(t.flags|=128,es(a,!1),e=l.updateQueue,t.updateQueue=e,Ji(t,e),t.subtreeFlags=0,e=n,n=t.child;null!==n;)Fr(n,e),n=n.sibling;return $(ui,1&ui.current|2),t.child}e=e.sibling}null!==a.tail&&ee()>ku&&(t.flags|=128,r=!0,es(a,!1),t.lanes=4194304)}else{if(!r)if(null!==(e=ci(l))){if(t.flags|=128,r=!0,e=e.updateQueue,t.updateQueue=e,Ji(t,e),es(a,!0),null===a.tail&&"hidden"===a.tailMode&&!l.alternate&&!aa)return ts(t),null}else 2*ee()-a.renderingStartTime>ku&&536870912!==n&&(t.flags|=128,r=!0,es(a,!1),t.lanes=4194304);a.isBackwards?(l.sibling=t.child,t.child=l):(null!==(e=a.last)?e.sibling=l:t.child=l,a.last=l)}return null!==a.tail?(t=a.tail,a.rendering=t,a.tail=t.sibling,a.renderingStartTime=ee(),t.sibling=null,e=ui.current,$(ui,r?1&e|2:1&e),t):(ts(t),null);case 22:case 23:return si(t),gl(),r=null!==t.memoizedState,null!==e?null!==e.memoizedState!==r&&(t.flags|=8192):r&&(t.flags|=8192),r?!!(536870912&n)&&!(128&t.flags)&&(ts(t),6&t.subtreeFlags&&(t.flags|=8192)):ts(t),null!==(n=t.updateQueue)&&Ji(t,n.retryQueue),n=null,null!==e&&null!==e.memoizedState&&null!==e.memoizedState.cachePool&&(n=e.memoizedState.cachePool.pool),r=null,null!==t.memoizedState&&null!==t.memoizedState.cachePool&&(r=t.memoizedState.cachePool.pool),r!==n&&(t.flags|=2048),null!==e&&M($a),null;case 24:return n=null,null!==e&&(n=e.memoizedState.cache),t.memoizedState.cache!==n&&(t.flags|=2048),ba(La),ts(t),null;case 25:case 30:return null}throw Error(o(156,t.tag))}function rs(e,t){switch(ta(t),t.tag){case 1:return 65536&(e=t.flags)?(t.flags=-65537&e|128,t):null;case 3:return ba(La),Q(),65536&(e=t.flags)&&!(128&e)?(t.flags=-65537&e|128,t):null;case 26:case 27:case 5:return G(t),null;case 13:if(si(t),null!==(e=t.memoizedState)&&null!==e.dehydrated){if(null===t.alternate)throw Error(o(340));fa()}return 65536&(e=t.flags)?(t.flags=-65537&e|128,t):null;case 19:return M(ui),null;case 4:return Q(),null;case 10:return ba(t.type),null;case 22:case 23:return si(t),gl(),null!==e&&M($a),65536&(e=t.flags)?(t.flags=-65537&e|128,t):null;case 24:return ba(La),null;default:return null}}function as(e,t){switch(ta(t),t.tag){case 3:ba(La),Q();break;case 26:case 27:case 5:G(t);break;case 4:Q();break;case 13:si(t);break;case 19:M(ui);break;case 10:ba(t.type);break;case 22:case 23:si(t),gl(),null!==e&&M($a);break;case 24:ba(La)}}function ls(e,t){try{var n=t.updateQueue,r=null!==n?n.lastEffect:null;if(null!==r){var a=r.next;n=a;do{if((n.tag&e)===e){r=void 0;var l=n.create,o=n.inst;r=l(),o.destroy=r}n=n.next}while(n!==a)}}catch(e){uc(t,t.return,e)}}function os(e,t,n){try{var r=t.updateQueue,a=null!==r?r.lastEffect:null;if(null!==a){var l=a.next;r=l;do{if((r.tag&e)===e){var o=r.inst,i=o.destroy;if(void 0!==i){o.destroy=void 0,a=t;var s=n,u=i;try{u()}catch(e){uc(a,s,e)}}}r=r.next}while(r!==l)}}catch(e){uc(t,t.return,e)}}function is(e){var t=e.updateQueue;if(null!==t){var n=e.stateNode;try{dl(t,n)}catch(t){uc(e,e.return,t)}}}function ss(e,t,n){n.props=mi(e.type,e.memoizedProps),n.state=e.memoizedState;try{n.componentWillUnmount()}catch(n){uc(e,t,n)}}function us(e,t){try{var n=e.ref;if(null!==n){switch(e.tag){case 26:case 27:case 5:var r=e.stateNode;break;default:r=e.stateNode}"function"==typeof n?e.refCleanup=n(r):n.current=r}}catch(n){uc(e,t,n)}}function cs(e,t){var n=e.ref,r=e.refCleanup;if(null!==n)if("function"==typeof r)try{r()}catch(n){uc(e,t,n)}finally{e.refCleanup=null,null!=(e=e.alternate)&&(e.refCleanup=null)}else if("function"==typeof n)try{n(null)}catch(n){uc(e,t,n)}else n.current=null}function ds(e){var t=e.type,n=e.memoizedProps,r=e.stateNode;try{e:switch(t){case"button":case"input":case"select":case"textarea":n.autoFocus&&r.focus();break e;case"img":n.src?r.src=n.src:n.srcSet&&(r.srcset=n.srcSet)}}catch(t){uc(e,e.return,t)}}function fs(e,t,n){try{var r=e.stateNode;!function(e,t,n,r){switch(t){case"div":case"span":case"svg":case"path":case"a":case"g":case"p":case"li":break;case"input":var a=null,l=null,i=null,s=null,u=null,c=null,d=null;for(h in n){var f=n[h];if(n.hasOwnProperty(h)&&null!=f)switch(h){case"checked":case"value":break;case"defaultValue":u=f;default:r.hasOwnProperty(h)||Xc(e,t,h,null,r,f)}}for(var p in r){var h=r[p];if(f=n[p],r.hasOwnProperty(p)&&(null!=h||null!=f))switch(p){case"type":l=h;break;case"name":a=h;break;case"checked":c=h;break;case"defaultChecked":d=h;break;case"value":i=h;break;case"defaultValue":s=h;break;case"children":case"dangerouslySetInnerHTML":if(null!=h)throw Error(o(137,t));break;default:h!==f&&Xc(e,t,p,h,r,f)}}return void mt(e,i,s,u,c,d,l,a);case"select":for(l in h=i=s=p=null,n)if(u=n[l],n.hasOwnProperty(l)&&null!=u)switch(l){case"value":break;case"multiple":h=u;default:r.hasOwnProperty(l)||Xc(e,t,l,null,r,u)}for(a in r)if(l=r[a],u=n[a],r.hasOwnProperty(a)&&(null!=l||null!=u))switch(a){case"value":p=l;break;case"defaultValue":s=l;break;case"multiple":i=l;default:l!==u&&Xc(e,t,a,l,r,u)}return t=s,n=i,r=h,void(null!=p?vt(e,!!n,p,!1):!!r!=!!n&&(null!=t?vt(e,!!n,t,!0):vt(e,!!n,n?[]:"",!1)));case"textarea":for(s in h=p=null,n)if(a=n[s],n.hasOwnProperty(s)&&null!=a&&!r.hasOwnProperty(s))switch(s){case"value":case"children":break;default:Xc(e,t,s,null,r,a)}for(i in r)if(a=r[i],l=n[i],r.hasOwnProperty(i)&&(null!=a||null!=l))switch(i){case"value":p=a;break;case"defaultValue":h=a;break;case"children":break;case"dangerouslySetInnerHTML":if(null!=a)throw Error(o(91));break;default:a!==l&&Xc(e,t,i,a,r,l)}return void bt(e,p,h);case"option":for(var m in n)p=n[m],n.hasOwnProperty(m)&&null!=p&&!r.hasOwnProperty(m)&&("selected"===m?e.selected=!1:Xc(e,t,m,null,r,p));for(u in r)p=r[u],h=n[u],!r.hasOwnProperty(u)||p===h||null==p&&null==h||("selected"===u?e.selected=p&&"function"!=typeof p&&"symbol"!=typeof p:Xc(e,t,u,p,r,h));return;case"img":case"link":case"area":case"base":case"br":case"col":case"embed":case"hr":case"keygen":case"meta":case"param":case"source":case"track":case"wbr":case"menuitem":for(var g in n)p=n[g],n.hasOwnProperty(g)&&null!=p&&!r.hasOwnProperty(g)&&Xc(e,t,g,null,r,p);for(c in r)if(p=r[c],h=n[c],r.hasOwnProperty(c)&&p!==h&&(null!=p||null!=h))switch(c){case"children":case"dangerouslySetInnerHTML":if(null!=p)throw Error(o(137,t));break;default:Xc(e,t,c,p,r,h)}return;default:if(Et(t)){for(var y in n)p=n[y],n.hasOwnProperty(y)&&void 0!==p&&!r.hasOwnProperty(y)&&Zc(e,t,y,void 0,r,p);for(d in r)p=r[d],h=n[d],!r.hasOwnProperty(d)||p===h||void 0===p&&void 0===h||Zc(e,t,d,p,r,h);return}}for(var v in n)p=n[v],n.hasOwnProperty(v)&&null!=p&&!r.hasOwnProperty(v)&&Xc(e,t,v,null,r,p);for(f in r)p=r[f],h=n[f],!r.hasOwnProperty(f)||p===h||null==p&&null==h||Xc(e,t,f,p,r,h)}(r,e.type,n,t),r[Oe]=t}catch(t){uc(e,e.return,t)}}function ps(e){return 5===e.tag||3===e.tag||26===e.tag||27===e.tag&&fd(e.type)||4===e.tag}function hs(e){e:for(;;){for(;null===e.sibling;){if(null===e.return||ps(e.return))return null;e=e.return}for(e.sibling.return=e.return,e=e.sibling;5!==e.tag&&6!==e.tag&&18!==e.tag;){if(27===e.tag&&fd(e.type))continue e;if(2&e.flags)continue e;if(null===e.child||4===e.tag)continue e;e.child.return=e,e=e.child}if(!(2&e.flags))return e.stateNode}}function ms(e,t,n){var r=e.tag;if(5===r||6===r)e=e.stateNode,t?(9===n.nodeType?n.body:"HTML"===n.nodeName?n.ownerDocument.body:n).insertBefore(e,t):((t=9===n.nodeType?n.body:"HTML"===n.nodeName?n.ownerDocument.body:n).appendChild(e),null!=(n=n._reactRootContainer)||null!==t.onclick||(t.onclick=Kc));else if(4!==r&&(27===r&&fd(e.type)&&(n=e.stateNode,t=null),null!==(e=e.child)))for(ms(e,t,n),e=e.sibling;null!==e;)ms(e,t,n),e=e.sibling}function gs(e,t,n){var r=e.tag;if(5===r||6===r)e=e.stateNode,t?n.insertBefore(e,t):n.appendChild(e);else if(4!==r&&(27===r&&fd(e.type)&&(n=e.stateNode),null!==(e=e.child)))for(gs(e,t,n),e=e.sibling;null!==e;)gs(e,t,n),e=e.sibling}function ys(e){var t=e.stateNode,n=e.memoizedProps;try{for(var r=e.type,a=t.attributes;a.length;)t.removeAttributeNode(a[0]);Jc(t,r,n),t[Le]=e,t[Oe]=n}catch(t){uc(e,e.return,t)}}var vs=!1,bs=!1,ws=!1,ks="function"==typeof WeakSet?WeakSet:Set,Ss=null;function xs(e,t,n){var r=n.flags;switch(n.tag){case 0:case 11:case 15:Ds(e,n),4&r&&ls(5,n);break;case 1:if(Ds(e,n),4&r)if(e=n.stateNode,null===t)try{e.componentDidMount()}catch(e){uc(n,n.return,e)}else{var a=mi(n.type,t.memoizedProps);t=t.memoizedState;try{e.componentDidUpdate(a,t,e.__reactInternalSnapshotBeforeUpdate)}catch(e){uc(n,n.return,e)}}64&r&&is(n),512&r&&us(n,n.return);break;case 3:if(Ds(e,n),64&r&&null!==(e=n.updateQueue)){if(t=null,null!==n.child)switch(n.child.tag){case 27:case 5:case 1:t=n.child.stateNode}try{dl(e,t)}catch(e){uc(n,n.return,e)}}break;case 27:null===t&&4&r&&ys(n);case 26:case 5:Ds(e,n),null===t&&4&r&&ds(n),512&r&&us(n,n.return);break;case 12:Ds(e,n);break;case 13:Ds(e,n),4&r&&Ns(e,n),64&r&&null!==(e=n.memoizedState)&&null!==(e=e.dehydrated)&&function(e,t){var n=e.ownerDocument;if("$?"!==e.data||"complete"===n.readyState)t();else{var r=function(){t(),n.removeEventListener("DOMContentLoaded",r)};n.addEventListener("DOMContentLoaded",r),e._reactRetry=r}}(e,n=pc.bind(null,n));break;case 22:if(!(r=null!==n.memoizedState||vs)){t=null!==t&&null!==t.memoizedState||bs,a=vs;var l=bs;vs=r,(bs=t)&&!l?Fs(e,n,!!(8772&n.subtreeFlags)):Ds(e,n),vs=a,bs=l}break;case 30:break;default:Ds(e,n)}}function Cs(e){var t=e.alternate;null!==t&&(e.alternate=null,Cs(t)),e.child=null,e.deletions=null,e.sibling=null,5===e.tag&&null!==(t=e.stateNode)&&Me(t),e.stateNode=null,e.return=null,e.dependencies=null,e.memoizedProps=null,e.memoizedState=null,e.pendingProps=null,e.stateNode=null,e.updateQueue=null}var Es=null,Ps=!1;function zs(e,t,n){for(n=n.child;null!==n;)_s(e,t,n),n=n.sibling}function _s(e,t,n){if(ce&&"function"==typeof ce.onCommitFiberUnmount)try{ce.onCommitFiberUnmount(ue,n)}catch(e){}switch(n.tag){case 26:bs||cs(n,t),zs(e,t,n),n.memoizedState?n.memoizedState.count--:n.stateNode&&(n=n.stateNode).parentNode.removeChild(n);break;case 27:bs||cs(n,t);var r=Es,a=Ps;fd(n.type)&&(Es=n.stateNode,Ps=!1),zs(e,t,n),wd(n.stateNode),Es=r,Ps=a;break;case 5:bs||cs(n,t);case 6:if(r=Es,a=Ps,Es=null,zs(e,t,n),Ps=a,null!==(Es=r))if(Ps)try{(9===Es.nodeType?Es.body:"HTML"===Es.nodeName?Es.ownerDocument.body:Es).removeChild(n.stateNode)}catch(e){uc(n,t,e)}else try{Es.removeChild(n.stateNode)}catch(e){uc(n,t,e)}break;case 18:null!==Es&&(Ps?(pd(9===(e=Es).nodeType?e.body:"HTML"===e.nodeName?e.ownerDocument.body:e,n.stateNode),Ef(e)):pd(Es,n.stateNode));break;case 4:r=Es,a=Ps,Es=n.stateNode.containerInfo,Ps=!0,zs(e,t,n),Es=r,Ps=a;break;case 0:case 11:case 14:case 15:bs||os(2,n,t),bs||os(4,n,t),zs(e,t,n);break;case 1:bs||(cs(n,t),"function"==typeof(r=n.stateNode).componentWillUnmount&&ss(n,t,r)),zs(e,t,n);break;case 21:zs(e,t,n);break;case 22:bs=(r=bs)||null!==n.memoizedState,zs(e,t,n),bs=r;break;default:zs(e,t,n)}}function Ns(e,t){if(null===t.memoizedState&&null!==(e=t.alternate)&&null!==(e=e.memoizedState)&&null!==(e=e.dehydrated))try{Ef(e)}catch(e){uc(t,t.return,e)}}function Ts(e,t){var n=function(e){switch(e.tag){case 13:case 19:var t=e.stateNode;return null===t&&(t=e.stateNode=new ks),t;case 22:return null===(t=(e=e.stateNode)._retryCache)&&(t=e._retryCache=new ks),t;default:throw Error(o(435,e.tag))}}(e);t.forEach(function(t){var r=hc.bind(null,e,t);n.has(t)||(n.add(t),t.then(r,r))})}function Ls(e,t){var n=t.deletions;if(null!==n)for(var r=0;r<n.length;r++){var a=n[r],l=e,i=t,s=i;e:for(;null!==s;){switch(s.tag){case 27:if(fd(s.type)){Es=s.stateNode,Ps=!1;break e}break;case 5:Es=s.stateNode,Ps=!1;break e;case 3:case 4:Es=s.stateNode.containerInfo,Ps=!0;break e}s=s.return}if(null===Es)throw Error(o(160));_s(l,i,a),Es=null,Ps=!1,null!==(l=a.alternate)&&(l.return=null),a.return=null}if(13878&t.subtreeFlags)for(t=t.child;null!==t;)js(t,e),t=t.sibling}var Os=null;function js(e,t){var n=e.alternate,r=e.flags;switch(e.tag){case 0:case 11:case 14:case 15:Ls(t,e),Rs(e),4&r&&(os(3,e,e.return),ls(3,e),os(5,e,e.return));break;case 1:Ls(t,e),Rs(e),512&r&&(bs||null===n||cs(n,n.return)),64&r&&vs&&null!==(e=e.updateQueue)&&null!==(r=e.callbacks)&&(n=e.shared.hiddenCallbacks,e.shared.hiddenCallbacks=null===n?r:n.concat(r));break;case 26:var a=Os;if(Ls(t,e),Rs(e),512&r&&(bs||null===n||cs(n,n.return)),4&r){var l=null!==n?n.memoizedState:null;if(r=e.memoizedState,null===n)if(null===r)if(null===e.stateNode){e:{r=e.type,n=e.memoizedProps,a=a.ownerDocument||a;t:switch(r){case"title":(!(l=a.getElementsByTagName("title")[0])||l[Fe]||l[Le]||"http://www.w3.org/2000/svg"===l.namespaceURI||l.hasAttribute("itemprop"))&&(l=a.createElement(r),a.head.insertBefore(l,a.querySelector("head > title"))),Jc(l,r,n),l[Le]=e,Ve(l),r=l;break e;case"link":var i=Fd("link","href",a).get(r+(n.href||""));if(i)for(var s=0;s<i.length;s++)if((l=i[s]).getAttribute("href")===(null==n.href||""===n.href?null:n.href)&&l.getAttribute("rel")===(null==n.rel?null:n.rel)&&l.getAttribute("title")===(null==n.title?null:n.title)&&l.getAttribute("crossorigin")===(null==n.crossOrigin?null:n.crossOrigin)){i.splice(s,1);break t}Jc(l=a.createElement(r),r,n),a.head.appendChild(l);break;case"meta":if(i=Fd("meta","content",a).get(r+(n.content||"")))for(s=0;s<i.length;s++)if((l=i[s]).getAttribute("content")===(null==n.content?null:""+n.content)&&l.getAttribute("name")===(null==n.name?null:n.name)&&l.getAttribute("property")===(null==n.property?null:n.property)&&l.getAttribute("http-equiv")===(null==n.httpEquiv?null:n.httpEquiv)&&l.getAttribute("charset")===(null==n.charSet?null:n.charSet)){i.splice(s,1);break t}Jc(l=a.createElement(r),r,n),a.head.appendChild(l);break;default:throw Error(o(468,r))}l[Le]=e,Ve(l),r=l}e.stateNode=r}else Md(a,e.type,e.stateNode);else e.stateNode=jd(a,r,e.memoizedProps);else l!==r?(null===l?null!==n.stateNode&&(n=n.stateNode).parentNode.removeChild(n):l.count--,null===r?Md(a,e.type,e.stateNode):jd(a,r,e.memoizedProps)):null===r&&null!==e.stateNode&&fs(e,e.memoizedProps,n.memoizedProps)}break;case 27:Ls(t,e),Rs(e),512&r&&(bs||null===n||cs(n,n.return)),null!==n&&4&r&&fs(e,e.memoizedProps,n.memoizedProps);break;case 5:if(Ls(t,e),Rs(e),512&r&&(bs||null===n||cs(n,n.return)),32&e.flags){a=e.stateNode;try{kt(a,"")}catch(t){uc(e,e.return,t)}}4&r&&null!=e.stateNode&&fs(e,a=e.memoizedProps,null!==n?n.memoizedProps:a),1024&r&&(ws=!0);break;case 6:if(Ls(t,e),Rs(e),4&r){if(null===e.stateNode)throw Error(o(162));r=e.memoizedProps,n=e.stateNode;try{n.nodeValue=r}catch(t){uc(e,e.return,t)}}break;case 3:if(Id=null,a=Os,Os=xd(t.containerInfo),Ls(t,e),Os=a,Rs(e),4&r&&null!==n&&n.memoizedState.isDehydrated)try{Ef(t.containerInfo)}catch(t){uc(e,e.return,t)}ws&&(ws=!1,As(e));break;case 4:r=Os,Os=xd(e.stateNode.containerInfo),Ls(t,e),Rs(e),Os=r;break;case 12:default:Ls(t,e),Rs(e);break;case 13:Ls(t,e),Rs(e),8192&e.child.flags&&null!==e.memoizedState!=(null!==n&&null!==n.memoizedState)&&(wu=ee()),4&r&&null!==(r=e.updateQueue)&&(e.updateQueue=null,Ts(e,r));break;case 22:a=null!==e.memoizedState;var u=null!==n&&null!==n.memoizedState,c=vs,d=bs;if(vs=c||a,bs=d||u,Ls(t,e),bs=d,vs=c,Rs(e),8192&r)e:for(t=e.stateNode,t._visibility=a?-2&t._visibility:1|t._visibility,a&&(null===n||u||vs||bs||Is(e)),n=null,t=e;;){if(5===t.tag||26===t.tag){if(null===n){u=n=t;try{if(l=u.stateNode,a)"function"==typeof(i=l.style).setProperty?i.setProperty("display","none","important"):i.display="none";else{s=u.stateNode;var f=u.memoizedProps.style,p=null!=f&&f.hasOwnProperty("display")?f.display:null;s.style.display=null==p||"boolean"==typeof p?"":(""+p).trim()}}catch(e){uc(u,u.return,e)}}}else if(6===t.tag){if(null===n){u=t;try{u.stateNode.nodeValue=a?"":u.memoizedProps}catch(e){uc(u,u.return,e)}}}else if((22!==t.tag&&23!==t.tag||null===t.memoizedState||t===e)&&null!==t.child){t.child.return=t,t=t.child;continue}if(t===e)break e;for(;null===t.sibling;){if(null===t.return||t.return===e)break e;n===t&&(n=null),t=t.return}n===t&&(n=null),t.sibling.return=t.return,t=t.sibling}4&r&&null!==(r=e.updateQueue)&&null!==(n=r.retryQueue)&&(r.retryQueue=null,Ts(e,n));break;case 19:Ls(t,e),Rs(e),4&r&&null!==(r=e.updateQueue)&&(e.updateQueue=null,Ts(e,r));case 30:case 21:}}function Rs(e){var t=e.flags;if(2&t){try{for(var n,r=e.return;null!==r;){if(ps(r)){n=r;break}r=r.return}if(null==n)throw Error(o(160));switch(n.tag){case 27:var a=n.stateNode;gs(e,hs(e),a);break;case 5:var l=n.stateNode;32&n.flags&&(kt(l,""),n.flags&=-33),gs(e,hs(e),l);break;case 3:case 4:var i=n.stateNode.containerInfo;ms(e,hs(e),i);break;default:throw Error(o(161))}}catch(t){uc(e,e.return,t)}e.flags&=-3}4096&t&&(e.flags&=-4097)}function As(e){if(1024&e.subtreeFlags)for(e=e.child;null!==e;){var t=e;As(t),5===t.tag&&1024&t.flags&&t.stateNode.reset(),e=e.sibling}}function Ds(e,t){if(8772&t.subtreeFlags)for(t=t.child;null!==t;)xs(e,t.alternate,t),t=t.sibling}function Is(e){for(e=e.child;null!==e;){var t=e;switch(t.tag){case 0:case 11:case 14:case 15:os(4,t,t.return),Is(t);break;case 1:cs(t,t.return);var n=t.stateNode;"function"==typeof n.componentWillUnmount&&ss(t,t.return,n),Is(t);break;case 27:wd(t.stateNode);case 26:case 5:cs(t,t.return),Is(t);break;case 22:null===t.memoizedState&&Is(t);break;default:Is(t)}e=e.sibling}}function Fs(e,t,n){for(n=n&&!!(8772&t.subtreeFlags),t=t.child;null!==t;){var r=t.alternate,a=e,l=t,o=l.flags;switch(l.tag){case 0:case 11:case 15:Fs(a,l,n),ls(4,l);break;case 1:if(Fs(a,l,n),"function"==typeof(a=(r=l).stateNode).componentDidMount)try{a.componentDidMount()}catch(e){uc(r,r.return,e)}if(null!==(a=(r=l).updateQueue)){var i=r.stateNode;try{var s=a.shared.hiddenCallbacks;if(null!==s)for(a.shared.hiddenCallbacks=null,a=0;a<s.length;a++)cl(s[a],i)}catch(e){uc(r,r.return,e)}}n&&64&o&&is(l),us(l,l.return);break;case 27:ys(l);case 26:case 5:Fs(a,l,n),n&&null===r&&4&o&&ds(l),us(l,l.return);break;case 12:Fs(a,l,n);break;case 13:Fs(a,l,n),n&&4&o&&Ns(a,l);break;case 22:null===l.memoizedState&&Fs(a,l,n),us(l,l.return);break;case 30:break;default:Fs(a,l,n)}t=t.sibling}}function Ms(e,t){var n=null;null!==e&&null!==e.memoizedState&&null!==e.memoizedState.cachePool&&(n=e.memoizedState.cachePool.pool),e=null,null!==t.memoizedState&&null!==t.memoizedState.cachePool&&(e=t.memoizedState.cachePool.pool),e!==n&&(null!=e&&e.refCount++,null!=n&&ja(n))}function $s(e,t){e=null,null!==t.alternate&&(e=t.alternate.memoizedState.cache),(t=t.memoizedState.cache)!==e&&(t.refCount++,null!=e&&ja(e))}function Us(e,t,n,r){if(10256&t.subtreeFlags)for(t=t.child;null!==t;)Hs(e,t,n,r),t=t.sibling}function Hs(e,t,n,r){var a=t.flags;switch(t.tag){case 0:case 11:case 15:Us(e,t,n,r),2048&a&&ls(9,t);break;case 1:case 13:default:Us(e,t,n,r);break;case 3:Us(e,t,n,r),2048&a&&(e=null,null!==t.alternate&&(e=t.alternate.memoizedState.cache),(t=t.memoizedState.cache)!==e&&(t.refCount++,null!=e&&ja(e)));break;case 12:if(2048&a){Us(e,t,n,r),e=t.stateNode;try{var l=t.memoizedProps,o=l.id,i=l.onPostCommit;"function"==typeof i&&i(o,null===t.alternate?"mount":"update",e.passiveEffectDuration,-0)}catch(e){uc(t,t.return,e)}}else Us(e,t,n,r);break;case 23:break;case 22:l=t.stateNode,o=t.alternate,null!==t.memoizedState?2&l._visibility?Us(e,t,n,r):Vs(e,t):2&l._visibility?Us(e,t,n,r):(l._visibility|=2,Bs(e,t,n,r,!!(10256&t.subtreeFlags))),2048&a&&Ms(o,t);break;case 24:Us(e,t,n,r),2048&a&&$s(t.alternate,t)}}function Bs(e,t,n,r,a){for(a=a&&!!(10256&t.subtreeFlags),t=t.child;null!==t;){var l=e,o=t,i=n,s=r,u=o.flags;switch(o.tag){case 0:case 11:case 15:Bs(l,o,i,s,a),ls(8,o);break;case 23:break;case 22:var c=o.stateNode;null!==o.memoizedState?2&c._visibility?Bs(l,o,i,s,a):Vs(l,o):(c._visibility|=2,Bs(l,o,i,s,a)),a&&2048&u&&Ms(o.alternate,o);break;case 24:Bs(l,o,i,s,a),a&&2048&u&&$s(o.alternate,o);break;default:Bs(l,o,i,s,a)}t=t.sibling}}function Vs(e,t){if(10256&t.subtreeFlags)for(t=t.child;null!==t;){var n=e,r=t,a=r.flags;switch(r.tag){case 22:Vs(n,r),2048&a&&Ms(r.alternate,r);break;case 24:Vs(n,r),2048&a&&$s(r.alternate,r);break;default:Vs(n,r)}t=t.sibling}}var Ws=8192;function Qs(e){if(e.subtreeFlags&Ws)for(e=e.child;null!==e;)qs(e),e=e.sibling}function qs(e){switch(e.tag){case 26:Qs(e),e.flags&Ws&&null!==e.memoizedState&&function(e,t,n){if(null===Ud)throw Error(o(475));var r=Ud;if(!("stylesheet"!==t.type||"string"==typeof n.media&&!1===matchMedia(n.media).matches||4&t.state.loading)){if(null===t.instance){var a=_d(n.href),l=e.querySelector(Nd(a));if(l)return null!==(e=l._p)&&"object"==typeof e&&"function"==typeof e.then&&(r.count++,r=Bd.bind(r),e.then(r,r)),t.state.loading|=4,t.instance=l,void Ve(l);l=e.ownerDocument||e,n=Td(n),(a=kd.get(a))&&Ad(n,a),Ve(l=l.createElement("link"));var i=l;i._p=new Promise(function(e,t){i.onload=e,i.onerror=t}),Jc(l,"link",n),t.instance=l}null===r.stylesheets&&(r.stylesheets=new Map),r.stylesheets.set(t,e),(e=t.state.preload)&&!(3&t.state.loading)&&(r.count++,t=Bd.bind(r),e.addEventListener("load",t),e.addEventListener("error",t))}}(Os,e.memoizedState,e.memoizedProps);break;case 5:default:Qs(e);break;case 3:case 4:var t=Os;Os=xd(e.stateNode.containerInfo),Qs(e),Os=t;break;case 22:null===e.memoizedState&&(null!==(t=e.alternate)&&null!==t.memoizedState?(t=Ws,Ws=16777216,Qs(e),Ws=t):Qs(e))}}function Gs(e){var t=e.alternate;if(null!==t&&null!==(e=t.child)){t.child=null;do{t=e.sibling,e.sibling=null,e=t}while(null!==e)}}function Ys(e){var t=e.deletions;if(16&e.flags){if(null!==t)for(var n=0;n<t.length;n++){var r=t[n];Ss=r,Zs(r,e)}Gs(e)}if(10256&e.subtreeFlags)for(e=e.child;null!==e;)Ks(e),e=e.sibling}function Ks(e){switch(e.tag){case 0:case 11:case 15:Ys(e),2048&e.flags&&os(9,e,e.return);break;case 3:case 12:default:Ys(e);break;case 22:var t=e.stateNode;null!==e.memoizedState&&2&t._visibility&&(null===e.return||13!==e.return.tag)?(t._visibility&=-3,Xs(e)):Ys(e)}}function Xs(e){var t=e.deletions;if(16&e.flags){if(null!==t)for(var n=0;n<t.length;n++){var r=t[n];Ss=r,Zs(r,e)}Gs(e)}for(e=e.child;null!==e;){switch((t=e).tag){case 0:case 11:case 15:os(8,t,t.return),Xs(t);break;case 22:2&(n=t.stateNode)._visibility&&(n._visibility&=-3,Xs(t));break;default:Xs(t)}e=e.sibling}}function Zs(e,t){for(;null!==Ss;){var n=Ss;switch(n.tag){case 0:case 11:case 15:os(8,n,t);break;case 23:case 22:if(null!==n.memoizedState&&null!==n.memoizedState.cachePool){var r=n.memoizedState.cachePool.pool;null!=r&&r.refCount++}break;case 24:ja(n.memoizedState.cache)}if(null!==(r=n.child))r.return=n,Ss=r;else e:for(n=e;null!==Ss;){var a=(r=Ss).sibling,l=r.return;if(Cs(r),r===n){Ss=null;break e}if(null!==a){a.return=l,Ss=a;break e}Ss=l}}}var Js={getCacheForType:function(e){var t=Ea(La),n=t.data.get(e);return void 0===n&&(n=e(),t.data.set(e,n)),n}},eu="function"==typeof WeakMap?WeakMap:Map,tu=0,nu=null,ru=null,au=0,lu=0,ou=null,iu=!1,su=!1,uu=!1,cu=0,du=0,fu=0,pu=0,hu=0,mu=0,gu=0,yu=null,vu=null,bu=!1,wu=0,ku=1/0,Su=null,xu=null,Cu=0,Eu=null,Pu=null,zu=0,_u=0,Nu=null,Tu=null,Lu=0,Ou=null;function ju(){return 2&tu&&0!==au?au&-au:null!==j.T?0!==Da?Da:_c():Ne()}function Ru(){0===mu&&(mu=536870912&au&&!aa?536870912:ke());var e=ri.current;return null!==e&&(e.flags|=32),mu}function Au(e,t,n){(e!==nu||2!==lu&&9!==lu)&&null===e.cancelPendingCommit||(Hu(e,0),Mu(e,au,mu,!1)),Ce(e,n),2&tu&&e===nu||(e===nu&&(!(2&tu)&&(pu|=n),4===du&&Mu(e,au,mu,!1)),kc(e))}function Du(e,t,n){if(6&tu)throw Error(o(327));for(var r=!n&&!(124&t)&&0===(t&e.expiredLanes)||be(e,t),a=r?function(e,t){var n=tu;tu|=2;var r=Vu(),a=Wu();nu!==e||au!==t?(Su=null,ku=ee()+500,Hu(e,t)):su=be(e,t);e:for(;;)try{if(0!==lu&&null!==ru){t=ru;var l=ou;t:switch(lu){case 1:lu=0,ou=null,Zu(e,t,l,1);break;case 2:case 9:if(Ga(l)){lu=0,ou=null,Xu(t);break}t=function(){2!==lu&&9!==lu||nu!==e||(lu=7),kc(e)},l.then(t,t);break e;case 3:lu=7;break e;case 4:lu=5;break e;case 7:Ga(l)?(lu=0,ou=null,Xu(t)):(lu=0,ou=null,Zu(e,t,l,7));break;case 5:var i=null;switch(ru.tag){case 26:i=ru.memoizedState;case 5:case 27:var s=ru;if(!i||$d(i)){lu=0,ou=null;var u=s.sibling;if(null!==u)ru=u;else{var c=s.return;null!==c?(ru=c,Ju(c)):ru=null}break t}}lu=0,ou=null,Zu(e,t,l,5);break;case 6:lu=0,ou=null,Zu(e,t,l,6);break;case 8:Uu(),du=6;break e;default:throw Error(o(462))}}Yu();break}catch(t){Bu(e,t)}return ya=ga=null,j.H=r,j.A=a,tu=n,null!==ru?0:(nu=null,au=0,zr(),du)}(e,t):qu(e,t,!0),l=r;;){if(0===a){su&&!r&&Mu(e,t,0,!1);break}if(n=e.current.alternate,!l||Fu(n)){if(2===a){if(l=t,e.errorRecoveryDisabledLanes&l)var i=0;else i=0!=(i=-536870913&e.pendingLanes)?i:536870912&i?536870912:0;if(0!==i){t=i;e:{var s=e;a=yu;var u=s.current.memoizedState.isDehydrated;if(u&&(Hu(s,i).flags|=256),2!==(i=qu(s,i,!1))){if(uu&&!u){s.errorRecoveryDisabledLanes|=l,pu|=l,a=4;break e}l=vu,vu=a,null!==l&&(null===vu?vu=l:vu.push.apply(vu,l))}a=i}if(l=!1,2!==a)continue}}if(1===a){Hu(e,0),Mu(e,t,0,!0);break}e:{switch(r=e,l=a){case 0:case 1:throw Error(o(345));case 4:if((4194048&t)!==t)break;case 6:Mu(r,t,mu,!iu);break e;case 2:vu=null;break;case 3:case 5:break;default:throw Error(o(329))}if((62914560&t)===t&&10<(a=wu+300-ee())){if(Mu(r,t,mu,!iu),0!==ve(r,0,!0))break e;r.timeoutHandle=id(Iu.bind(null,r,n,vu,Su,bu,t,mu,pu,gu,iu,l,2,-0,0),a)}else Iu(r,n,vu,Su,bu,t,mu,pu,gu,iu,l,0,-0,0)}break}a=qu(e,t,!1),l=!1}kc(e)}function Iu(e,t,n,r,a,l,i,s,u,c,d,f,p,h){if(e.timeoutHandle=-1,(8192&(f=t.subtreeFlags)||!(16785408&~f))&&(Ud={stylesheets:null,count:0,unsuspend:Hd},qs(t),null!==(f=function(){if(null===Ud)throw Error(o(475));var e=Ud;return e.stylesheets&&0===e.count&&Wd(e,e.stylesheets),0<e.count?function(t){var n=setTimeout(function(){if(e.stylesheets&&Wd(e,e.stylesheets),e.unsuspend){var t=e.unsuspend;e.unsuspend=null,t()}},6e4);return e.unsuspend=t,function(){e.unsuspend=null,clearTimeout(n)}}:null}())))return e.cancelPendingCommit=f(tc.bind(null,e,t,l,n,r,a,i,s,u,d,1,p,h)),void Mu(e,l,i,!c);tc(e,t,l,n,r,a,i,s,u)}function Fu(e){for(var t=e;;){var n=t.tag;if((0===n||11===n||15===n)&&16384&t.flags&&null!==(n=t.updateQueue)&&null!==(n=n.stores))for(var r=0;r<n.length;r++){var a=n[r],l=a.getSnapshot;a=a.value;try{if(!Gn(l(),a))return!1}catch(e){return!1}}if(n=t.child,16384&t.subtreeFlags&&null!==n)n.return=t,t=n;else{if(t===e)break;for(;null===t.sibling;){if(null===t.return||t.return===e)return!0;t=t.return}t.sibling.return=t.return,t=t.sibling}}return!0}function Mu(e,t,n,r){t&=~hu,t&=~pu,e.suspendedLanes|=t,e.pingedLanes&=~t,r&&(e.warmLanes|=t),r=e.expirationTimes;for(var a=t;0<a;){var l=31-fe(a),o=1<<l;r[l]=-1,a&=~o}0!==n&&Ee(e,n,t)}function $u(){return!!(6&tu)||(Sc(0,!1),!1)}function Uu(){if(null!==ru){if(0===lu)var e=ru.return;else ya=ga=null,Dl(e=ru),Go=null,Yo=0,e=ru;for(;null!==e;)as(e.alternate,e),e=e.return;ru=null}}function Hu(e,t){var n=e.timeoutHandle;-1!==n&&(e.timeoutHandle=-1,sd(n)),null!==(n=e.cancelPendingCommit)&&(e.cancelPendingCommit=null,n()),Uu(),nu=e,ru=n=Ir(e.current,null),au=t,lu=0,ou=null,iu=!1,su=be(e,t),uu=!1,gu=mu=hu=pu=fu=du=0,vu=yu=null,bu=!1,8&t&&(t|=32&t);var r=e.entangledLanes;if(0!==r)for(e=e.entanglements,r&=t;0<r;){var a=31-fe(r),l=1<<a;t|=e[a],r&=~l}return cu=t,zr(),n}function Bu(e,t){vl=null,j.H=Vo,t===Va||t===Qa?(t=Za(),lu=3):t===Wa?(t=Za(),lu=4):lu=t===Ei?8:null!==t&&"object"==typeof t&&"function"==typeof t.then?6:1,ou=t,null===ru&&(du=1,wi(e,xr(t,e.current)))}function Vu(){var e=j.H;return j.H=Vo,null===e?Vo:e}function Wu(){var e=j.A;return j.A=Js,e}function Qu(){du=4,iu||(4194048&au)!==au&&null!==ri.current||(su=!0),!(134217727&fu)&&!(134217727&pu)||null===nu||Mu(nu,au,mu,!1)}function qu(e,t,n){var r=tu;tu|=2;var a=Vu(),l=Wu();nu===e&&au===t||(Su=null,Hu(e,t)),t=!1;var o=du;e:for(;;)try{if(0!==lu&&null!==ru){var i=ru,s=ou;switch(lu){case 8:Uu(),o=6;break e;case 3:case 2:case 9:case 6:null===ri.current&&(t=!0);var u=lu;if(lu=0,ou=null,Zu(e,i,s,u),n&&su){o=0;break e}break;default:u=lu,lu=0,ou=null,Zu(e,i,s,u)}}Gu(),o=du;break}catch(t){Bu(e,t)}return t&&e.shellSuspendCounter++,ya=ga=null,tu=r,j.H=a,j.A=l,null===ru&&(nu=null,au=0,zr()),o}function Gu(){for(;null!==ru;)Ku(ru)}function Yu(){for(;null!==ru&&!Z();)Ku(ru)}function Ku(e){var t=Ki(e.alternate,e,cu);e.memoizedProps=e.pendingProps,null===t?Ju(e):ru=t}function Xu(e){var t=e,n=t.alternate;switch(t.tag){case 15:case 0:t=Ai(n,t,t.pendingProps,t.type,void 0,au);break;case 11:t=Ai(n,t,t.pendingProps,t.type.render,t.ref,au);break;case 5:Dl(t);default:as(n,t),t=Ki(n,t=ru=Fr(t,cu),cu)}e.memoizedProps=e.pendingProps,null===t?Ju(e):ru=t}function Zu(e,t,n,r){ya=ga=null,Dl(t),Go=null,Yo=0;var a=t.return;try{if(function(e,t,n,r,a){if(n.flags|=32768,null!==r&&"object"==typeof r&&"function"==typeof r.then){if(null!==(t=n.alternate)&&Sa(t,n,a,!0),null!==(n=ri.current)){switch(n.tag){case 13:return null===ai?Qu():null===n.alternate&&0===du&&(du=3),n.flags&=-257,n.flags|=65536,n.lanes=a,r===qa?n.flags|=16384:(null===(t=n.updateQueue)?n.updateQueue=new Set([r]):t.add(r),cc(e,r,a)),!1;case 22:return n.flags|=65536,r===qa?n.flags|=16384:(null===(t=n.updateQueue)?(t={transitions:null,markerInstances:null,retryQueue:new Set([r])},n.updateQueue=t):null===(n=t.retryQueue)?t.retryQueue=new Set([r]):n.add(r),cc(e,r,a)),!1}throw Error(o(435,n.tag))}return cc(e,r,a),Qu(),!1}if(aa)return null!==(t=ri.current)?(!(65536&t.flags)&&(t.flags|=256),t.flags|=65536,t.lanes=a,r!==ia&&ha(xr(e=Error(o(422),{cause:r}),n))):(r!==ia&&ha(xr(t=Error(o(423),{cause:r}),n)),(e=e.current.alternate).flags|=65536,a&=-a,e.lanes|=a,r=xr(r,n),ol(e,a=Si(e.stateNode,r,a)),4!==du&&(du=2)),!1;var l=Error(o(520),{cause:r});if(l=xr(l,n),null===yu?yu=[l]:yu.push(l),4!==du&&(du=2),null===t)return!0;r=xr(r,n),n=t;do{switch(n.tag){case 3:return n.flags|=65536,e=a&-a,n.lanes|=e,ol(n,e=Si(n.stateNode,r,e)),!1;case 1:if(t=n.type,l=n.stateNode,!(128&n.flags||"function"!=typeof t.getDerivedStateFromError&&(null===l||"function"!=typeof l.componentDidCatch||null!==xu&&xu.has(l))))return n.flags|=65536,a&=-a,n.lanes|=a,Ci(a=xi(a),e,n,r),ol(n,a),!1}n=n.return}while(null!==n);return!1}(e,a,t,n,au))return du=1,wi(e,xr(n,e.current)),void(ru=null)}catch(t){if(null!==a)throw ru=a,t;return du=1,wi(e,xr(n,e.current)),void(ru=null)}32768&t.flags?(aa||1===r?e=!0:su||536870912&au?e=!1:(iu=e=!0,(2===r||9===r||3===r||6===r)&&null!==(r=ri.current)&&13===r.tag&&(r.flags|=16384)),ec(t,e)):Ju(t)}function Ju(e){var t=e;do{if(32768&t.flags)return void ec(t,iu);e=t.return;var n=ns(t.alternate,t,cu);if(null!==n)return void(ru=n);if(null!==(t=t.sibling))return void(ru=t);ru=t=e}while(null!==t);0===du&&(du=5)}function ec(e,t){do{var n=rs(e.alternate,e);if(null!==n)return n.flags&=32767,void(ru=n);if(null!==(n=e.return)&&(n.flags|=32768,n.subtreeFlags=0,n.deletions=null),!t&&null!==(e=e.sibling))return void(ru=e);ru=e=n}while(null!==e);du=6,ru=null}function tc(e,t,n,r,a,l,i,s,u){e.cancelPendingCommit=null;do{oc()}while(0!==Cu);if(6&tu)throw Error(o(327));if(null!==t){if(t===e.current)throw Error(o(177));if(l=t.lanes|t.childLanes,function(e,t,n,r,a,l){var o=e.pendingLanes;e.pendingLanes=n,e.suspendedLanes=0,e.pingedLanes=0,e.warmLanes=0,e.expiredLanes&=n,e.entangledLanes&=n,e.errorRecoveryDisabledLanes&=n,e.shellSuspendCounter=0;var i=e.entanglements,s=e.expirationTimes,u=e.hiddenUpdates;for(n=o&~n;0<n;){var c=31-fe(n),d=1<<c;i[c]=0,s[c]=-1;var f=u[c];if(null!==f)for(u[c]=null,c=0;c<f.length;c++){var p=f[c];null!==p&&(p.lane&=-536870913)}n&=~d}0!==r&&Ee(e,r,0),0!==l&&0===a&&0!==e.tag&&(e.suspendedLanes|=l&~(o&~t))}(e,n,l|=Pr,i,s,u),e===nu&&(ru=nu=null,au=0),Pu=t,Eu=e,zu=n,_u=l,Nu=a,Tu=r,10256&t.subtreeFlags||10256&t.flags?(e.callbackNode=null,e.callbackPriority=0,K(ae,function(){return ic(),null})):(e.callbackNode=null,e.callbackPriority=0),r=!!(13878&t.flags),13878&t.subtreeFlags||r){r=j.T,j.T=null,a=R.p,R.p=2,i=tu,tu|=4;try{!function(e,t){if(e=e.containerInfo,ed=Jd,er(e=Jn(e))){if("selectionStart"in e)var n={start:e.selectionStart,end:e.selectionEnd};else e:{var r=(n=(n=e.ownerDocument)&&n.defaultView||window).getSelection&&n.getSelection();if(r&&0!==r.rangeCount){n=r.anchorNode;var a=r.anchorOffset,l=r.focusNode;r=r.focusOffset;try{n.nodeType,l.nodeType}catch(e){n=null;break e}var i=0,s=-1,u=-1,c=0,d=0,f=e,p=null;t:for(;;){for(var h;f!==n||0!==a&&3!==f.nodeType||(s=i+a),f!==l||0!==r&&3!==f.nodeType||(u=i+r),3===f.nodeType&&(i+=f.nodeValue.length),null!==(h=f.firstChild);)p=f,f=h;for(;;){if(f===e)break t;if(p===n&&++c===a&&(s=i),p===l&&++d===r&&(u=i),null!==(h=f.nextSibling))break;p=(f=p).parentNode}f=h}n=-1===s||-1===u?null:{start:s,end:u}}else n=null}n=n||{start:0,end:0}}else n=null;for(td={focusedElem:e,selectionRange:n},Jd=!1,Ss=t;null!==Ss;)if(e=(t=Ss).child,1024&t.subtreeFlags&&null!==e)e.return=t,Ss=e;else for(;null!==Ss;){switch(l=(t=Ss).alternate,e=t.flags,t.tag){case 0:case 11:case 15:case 5:case 26:case 27:case 6:case 4:case 17:break;case 1:if(1024&e&&null!==l){e=void 0,n=t,a=l.memoizedProps,l=l.memoizedState,r=n.stateNode;try{var m=mi(n.type,a,(n.elementType,n.type));e=r.getSnapshotBeforeUpdate(m,l),r.__reactInternalSnapshotBeforeUpdate=e}catch(e){uc(n,n.return,e)}}break;case 3:if(1024&e)if(9===(n=(e=t.stateNode.containerInfo).nodeType))hd(e);else if(1===n)switch(e.nodeName){case"HEAD":case"HTML":case"BODY":hd(e);break;default:e.textContent=""}break;default:if(1024&e)throw Error(o(163))}if(null!==(e=t.sibling)){e.return=t.return,Ss=e;break}Ss=t.return}}(e,t)}finally{tu=i,R.p=a,j.T=r}}Cu=1,nc(),rc(),ac()}}function nc(){if(1===Cu){Cu=0;var e=Eu,t=Pu,n=!!(13878&t.flags);if(13878&t.subtreeFlags||n){n=j.T,j.T=null;var r=R.p;R.p=2;var a=tu;tu|=4;try{js(t,e);var l=td,o=Jn(e.containerInfo),i=l.focusedElem,s=l.selectionRange;if(o!==i&&i&&i.ownerDocument&&Zn(i.ownerDocument.documentElement,i)){if(null!==s&&er(i)){var u=s.start,c=s.end;if(void 0===c&&(c=u),"selectionStart"in i)i.selectionStart=u,i.selectionEnd=Math.min(c,i.value.length);else{var d=i.ownerDocument||document,f=d&&d.defaultView||window;if(f.getSelection){var p=f.getSelection(),h=i.textContent.length,m=Math.min(s.start,h),g=void 0===s.end?m:Math.min(s.end,h);!p.extend&&m>g&&(o=g,g=m,m=o);var y=Xn(i,m),v=Xn(i,g);if(y&&v&&(1!==p.rangeCount||p.anchorNode!==y.node||p.anchorOffset!==y.offset||p.focusNode!==v.node||p.focusOffset!==v.offset)){var b=d.createRange();b.setStart(y.node,y.offset),p.removeAllRanges(),m>g?(p.addRange(b),p.extend(v.node,v.offset)):(b.setEnd(v.node,v.offset),p.addRange(b))}}}}for(d=[],p=i;p=p.parentNode;)1===p.nodeType&&d.push({element:p,left:p.scrollLeft,top:p.scrollTop});for("function"==typeof i.focus&&i.focus(),i=0;i<d.length;i++){var w=d[i];w.element.scrollLeft=w.left,w.element.scrollTop=w.top}}Jd=!!ed,td=ed=null}finally{tu=a,R.p=r,j.T=n}}e.current=t,Cu=2}}function rc(){if(2===Cu){Cu=0;var e=Eu,t=Pu,n=!!(8772&t.flags);if(8772&t.subtreeFlags||n){n=j.T,j.T=null;var r=R.p;R.p=2;var a=tu;tu|=4;try{xs(e,t.alternate,t)}finally{tu=a,R.p=r,j.T=n}}Cu=3}}function ac(){if(4===Cu||3===Cu){Cu=0,J();var e=Eu,t=Pu,n=zu,r=Tu;10256&t.subtreeFlags||10256&t.flags?Cu=5:(Cu=0,Pu=Eu=null,lc(e,e.pendingLanes));var a=e.pendingLanes;if(0===a&&(xu=null),_e(n),t=t.stateNode,ce&&"function"==typeof ce.onCommitFiberRoot)try{ce.onCommitFiberRoot(ue,t,void 0,!(128&~t.current.flags))}catch(e){}if(null!==r){t=j.T,a=R.p,R.p=2,j.T=null;try{for(var l=e.onRecoverableError,o=0;o<r.length;o++){var i=r[o];l(i.value,{componentStack:i.stack})}}finally{j.T=t,R.p=a}}3&zu&&oc(),kc(e),a=e.pendingLanes,4194090&n&&42&a?e===Ou?Lu++:(Lu=0,Ou=e):Lu=0,Sc(0,!1)}}function lc(e,t){0===(e.pooledCacheLanes&=t)&&null!=(t=e.pooledCache)&&(e.pooledCache=null,ja(t))}function oc(e){return nc(),rc(),ac(),ic()}function ic(){if(5!==Cu)return!1;var e=Eu,t=_u;_u=0;var n=_e(zu),r=j.T,a=R.p;try{R.p=32>n?32:n,j.T=null,n=Nu,Nu=null;var l=Eu,i=zu;if(Cu=0,Pu=Eu=null,zu=0,6&tu)throw Error(o(331));var s=tu;if(tu|=4,Ks(l.current),Hs(l,l.current,i,n),tu=s,Sc(0,!1),ce&&"function"==typeof ce.onPostCommitFiberRoot)try{ce.onPostCommitFiberRoot(ue,l)}catch(e){}return!0}finally{R.p=a,j.T=r,lc(e,t)}}function sc(e,t,n){t=xr(n,t),null!==(e=al(e,t=Si(e.stateNode,t,2),2))&&(Ce(e,2),kc(e))}function uc(e,t,n){if(3===e.tag)sc(e,e,n);else for(;null!==t;){if(3===t.tag){sc(t,e,n);break}if(1===t.tag){var r=t.stateNode;if("function"==typeof t.type.getDerivedStateFromError||"function"==typeof r.componentDidCatch&&(null===xu||!xu.has(r))){e=xr(n,e),null!==(r=al(t,n=xi(2),2))&&(Ci(n,r,t,e),Ce(r,2),kc(r));break}}t=t.return}}function cc(e,t,n){var r=e.pingCache;if(null===r){r=e.pingCache=new eu;var a=new Set;r.set(t,a)}else void 0===(a=r.get(t))&&(a=new Set,r.set(t,a));a.has(n)||(uu=!0,a.add(n),e=dc.bind(null,e,t,n),t.then(e,e))}function dc(e,t,n){var r=e.pingCache;null!==r&&r.delete(t),e.pingedLanes|=e.suspendedLanes&n,e.warmLanes&=~n,nu===e&&(au&n)===n&&(4===du||3===du&&(62914560&au)===au&&300>ee()-wu?!(2&tu)&&Hu(e,0):hu|=n,gu===au&&(gu=0)),kc(e)}function fc(e,t){0===t&&(t=Se()),null!==(e=Tr(e,t))&&(Ce(e,t),kc(e))}function pc(e){var t=e.memoizedState,n=0;null!==t&&(n=t.retryLane),fc(e,n)}function hc(e,t){var n=0;switch(e.tag){case 13:var r=e.stateNode,a=e.memoizedState;null!==a&&(n=a.retryLane);break;case 19:r=e.stateNode;break;case 22:r=e.stateNode._retryCache;break;default:throw Error(o(314))}null!==r&&r.delete(t),fc(e,n)}var mc=null,gc=null,yc=!1,vc=!1,bc=!1,wc=0;function kc(e){e!==gc&&null===e.next&&(null===gc?mc=gc=e:gc=gc.next=e),vc=!0,yc||(yc=!0,cd(function(){6&tu?K(ne,xc):Cc()}))}function Sc(e,t){if(!bc&&vc){bc=!0;do{for(var n=!1,r=mc;null!==r;){if(!t)if(0!==e){var a=r.pendingLanes;if(0===a)var l=0;else{var o=r.suspendedLanes,i=r.pingedLanes;l=(1<<31-fe(42|e)+1)-1,l=201326741&(l&=a&~(o&~i))?201326741&l|1:l?2|l:0}0!==l&&(n=!0,zc(r,l))}else l=au,!(3&(l=ve(r,r===nu?l:0,null!==r.cancelPendingCommit||-1!==r.timeoutHandle)))||be(r,l)||(n=!0,zc(r,l));r=r.next}}while(n);bc=!1}}function xc(){Cc()}function Cc(){vc=yc=!1;var e,t=0;0!==wc&&(((e=window.event)&&"popstate"===e.type?e!==od&&(od=e,!0):(od=null,!1))&&(t=wc),wc=0);for(var n=ee(),r=null,a=mc;null!==a;){var l=a.next,o=Ec(a,n);0===o?(a.next=null,null===r?mc=l:r.next=l,null===l&&(gc=r)):(r=a,(0!==t||3&o)&&(vc=!0)),a=l}Sc(t,!1)}function Ec(e,t){for(var n=e.suspendedLanes,r=e.pingedLanes,a=e.expirationTimes,l=-62914561&e.pendingLanes;0<l;){var o=31-fe(l),i=1<<o,s=a[o];-1===s?0!==(i&n)&&0===(i&r)||(a[o]=we(i,t)):s<=t&&(e.expiredLanes|=i),l&=~i}if(n=au,n=ve(e,e===(t=nu)?n:0,null!==e.cancelPendingCommit||-1!==e.timeoutHandle),r=e.callbackNode,0===n||e===t&&(2===lu||9===lu)||null!==e.cancelPendingCommit)return null!==r&&null!==r&&X(r),e.callbackNode=null,e.callbackPriority=0;if(!(3&n)||be(e,n)){if((t=n&-n)===e.callbackPriority)return t;switch(null!==r&&X(r),_e(n)){case 2:case 8:n=re;break;case 32:default:n=ae;break;case 268435456:n=oe}return r=Pc.bind(null,e),n=K(n,r),e.callbackPriority=t,e.callbackNode=n,t}return null!==r&&null!==r&&X(r),e.callbackPriority=2,e.callbackNode=null,2}function Pc(e,t){if(0!==Cu&&5!==Cu)return e.callbackNode=null,e.callbackPriority=0,null;var n=e.callbackNode;if(oc()&&e.callbackNode!==n)return null;var r=au;return 0===(r=ve(e,e===nu?r:0,null!==e.cancelPendingCommit||-1!==e.timeoutHandle))?null:(Du(e,r,t),Ec(e,ee()),null!=e.callbackNode&&e.callbackNode===n?Pc.bind(null,e):null)}function zc(e,t){if(oc())return null;Du(e,t,!0)}function _c(){return 0===wc&&(wc=ke()),wc}function Nc(e){return null==e||"symbol"==typeof e||"boolean"==typeof e?null:"function"==typeof e?e:_t(""+e)}function Tc(e,t){var n=t.ownerDocument.createElement("input");return n.name=t.name,n.value=t.value,e.id&&n.setAttribute("form",e.id),t.parentNode.insertBefore(n,t),e=new FormData(e),n.parentNode.removeChild(n),e}for(var Lc=0;Lc<wr.length;Lc++){var Oc=wr[Lc];kr(Oc.toLowerCase(),"on"+(Oc[0].toUpperCase()+Oc.slice(1)))}kr(fr,"onAnimationEnd"),kr(pr,"onAnimationIteration"),kr(hr,"onAnimationStart"),kr("dblclick","onDoubleClick"),kr("focusin","onFocus"),kr("focusout","onBlur"),kr(mr,"onTransitionRun"),kr(gr,"onTransitionStart"),kr(yr,"onTransitionCancel"),kr(vr,"onTransitionEnd"),Ge("onMouseEnter",["mouseout","mouseover"]),Ge("onMouseLeave",["mouseout","mouseover"]),Ge("onPointerEnter",["pointerout","pointerover"]),Ge("onPointerLeave",["pointerout","pointerover"]),qe("onChange","change click focusin focusout input keydown keyup selectionchange".split(" ")),qe("onSelect","focusout contextmenu dragend focusin keydown keyup mousedown mouseup selectionchange".split(" ")),qe("onBeforeInput",["compositionend","keypress","textInput","paste"]),qe("onCompositionEnd","compositionend focusout keydown keypress keyup mousedown".split(" ")),qe("onCompositionStart","compositionstart focusout keydown keypress keyup mousedown".split(" ")),qe("onCompositionUpdate","compositionupdate focusout keydown keypress keyup mousedown".split(" "));var jc="abort canplay canplaythrough durationchange emptied encrypted ended error loadeddata loadedmetadata loadstart pause play playing progress ratechange resize seeked seeking stalled suspend timeupdate volumechange waiting".split(" "),Rc=new Set("beforetoggle cancel close invalid load scroll scrollend toggle".split(" ").concat(jc));function Ac(e,t){t=!!(4&t);for(var n=0;n<e.length;n++){var r=e[n],a=r.event;r=r.listeners;e:{var l=void 0;if(t)for(var o=r.length-1;0<=o;o--){var i=r[o],s=i.instance,u=i.currentTarget;if(i=i.listener,s!==l&&a.isPropagationStopped())break e;l=i,a.currentTarget=u;try{l(a)}catch(e){gi(e)}a.currentTarget=null,l=s}else for(o=0;o<r.length;o++){if(s=(i=r[o]).instance,u=i.currentTarget,i=i.listener,s!==l&&a.isPropagationStopped())break e;l=i,a.currentTarget=u;try{l(a)}catch(e){gi(e)}a.currentTarget=null,l=s}}}}function Dc(e,t){var n=t[Re];void 0===n&&(n=t[Re]=new Set);var r=e+"__bubble";n.has(r)||($c(t,e,2,!1),n.add(r))}function Ic(e,t,n){var r=0;t&&(r|=4),$c(n,e,r,t)}var Fc="_reactListening"+Math.random().toString(36).slice(2);function Mc(e){if(!e[Fc]){e[Fc]=!0,We.forEach(function(t){"selectionchange"!==t&&(Rc.has(t)||Ic(t,!1,e),Ic(t,!0,e))});var t=9===e.nodeType?e:e.ownerDocument;null===t||t[Fc]||(t[Fc]=!0,Ic("selectionchange",!1,t))}}function $c(e,t,n,r){switch(of(t)){case 2:var a=ef;break;case 8:a=tf;break;default:a=nf}n=a.bind(null,t,n,e),a=void 0,!Ft||"touchstart"!==t&&"touchmove"!==t&&"wheel"!==t||(a=!0),r?void 0!==a?e.addEventListener(t,n,{capture:!0,passive:a}):e.addEventListener(t,n,!0):void 0!==a?e.addEventListener(t,n,{passive:a}):e.addEventListener(t,n,!1)}function Uc(e,t,n,r,a){var l=r;if(!(1&t||2&t||null===r))e:for(;;){if(null===r)return;var o=r.tag;if(3===o||4===o){var s=r.stateNode.containerInfo;if(s===a)break;if(4===o)for(o=r.return;null!==o;){var u=o.tag;if((3===u||4===u)&&o.stateNode.containerInfo===a)return;o=o.return}for(;null!==s;){if(null===(o=$e(s)))return;if(5===(u=o.tag)||6===u||26===u||27===u){r=l=o;continue e}s=s.parentNode}}r=r.return}At(function(){var r=l,a=Tt(n),o=[];e:{var s=br.get(e);if(void 0!==s){var u=Zt,c=e;switch(e){case"keypress":if(0===Vt(n))break e;case"keydown":case"keyup":u=hn;break;case"focusin":c="focus",u=an;break;case"focusout":c="blur",u=an;break;case"beforeblur":case"afterblur":u=an;break;case"click":if(2===n.button)break e;case"auxclick":case"dblclick":case"mousedown":case"mousemove":case"mouseup":case"mouseout":case"mouseover":case"contextmenu":u=nn;break;case"drag":case"dragend":case"dragenter":case"dragexit":case"dragleave":case"dragover":case"dragstart":case"drop":u=rn;break;case"touchcancel":case"touchend":case"touchmove":case"touchstart":u=gn;break;case fr:case pr:case hr:u=ln;break;case vr:u=yn;break;case"scroll":case"scrollend":u=en;break;case"wheel":u=vn;break;case"copy":case"cut":case"paste":u=on;break;case"gotpointercapture":case"lostpointercapture":case"pointercancel":case"pointerdown":case"pointermove":case"pointerout":case"pointerover":case"pointerup":u=mn;break;case"toggle":case"beforetoggle":u=bn}var d=!!(4&t),f=!d&&("scroll"===e||"scrollend"===e),p=d?null!==s?s+"Capture":null:s;d=[];for(var h,m=r;null!==m;){var g=m;if(h=g.stateNode,5!==(g=g.tag)&&26!==g&&27!==g||null===h||null===p||null!=(g=Dt(m,p))&&d.push(Hc(m,g,h)),f)break;m=m.return}0<d.length&&(s=new u(s,c,null,n,a),o.push({event:s,listeners:d}))}}if(!(7&t)){if(u="mouseout"===e||"pointerout"===e,(!(s="mouseover"===e||"pointerover"===e)||n===Nt||!(c=n.relatedTarget||n.fromElement)||!$e(c)&&!c[je])&&(u||s)&&(s=a.window===a?a:(s=a.ownerDocument)?s.defaultView||s.parentWindow:window,u?(u=r,null!==(c=(c=n.relatedTarget||n.toElement)?$e(c):null)&&(f=i(c),d=c.tag,c!==f||5!==d&&27!==d&&6!==d)&&(c=null)):(u=null,c=r),u!==c)){if(d=nn,g="onMouseLeave",p="onMouseEnter",m="mouse","pointerout"!==e&&"pointerover"!==e||(d=mn,g="onPointerLeave",p="onPointerEnter",m="pointer"),f=null==u?s:He(u),h=null==c?s:He(c),(s=new d(g,m+"leave",u,n,a)).target=f,s.relatedTarget=h,g=null,$e(a)===r&&((d=new d(p,m+"enter",c,n,a)).target=h,d.relatedTarget=f,g=d),f=g,u&&c)e:{for(p=c,m=0,h=d=u;h;h=Vc(h))m++;for(h=0,g=p;g;g=Vc(g))h++;for(;0<m-h;)d=Vc(d),m--;for(;0<h-m;)p=Vc(p),h--;for(;m--;){if(d===p||null!==p&&d===p.alternate)break e;d=Vc(d),p=Vc(p)}d=null}else d=null;null!==u&&Wc(o,s,u,d,!1),null!==c&&null!==f&&Wc(o,f,c,d,!0)}if("select"===(u=(s=r?He(r):window).nodeName&&s.nodeName.toLowerCase())||"input"===u&&"file"===s.type)var y=In;else if(Ln(s))if(Fn)y=qn;else{y=Wn;var v=Vn}else!(u=s.nodeName)||"input"!==u.toLowerCase()||"checkbox"!==s.type&&"radio"!==s.type?r&&Et(r.elementType)&&(y=In):y=Qn;switch(y&&(y=y(e,r))?On(o,y,n,a):(v&&v(e,s,r),"focusout"===e&&r&&"number"===s.type&&null!=r.memoizedProps.value&&yt(s,"number",s.value)),v=r?He(r):window,e){case"focusin":(Ln(v)||"true"===v.contentEditable)&&(nr=v,rr=r,ar=null);break;case"focusout":ar=rr=nr=null;break;case"mousedown":lr=!0;break;case"contextmenu":case"mouseup":case"dragend":lr=!1,or(o,n,a);break;case"selectionchange":if(tr)break;case"keydown":case"keyup":or(o,n,a)}var b;if(kn)e:{switch(e){case"compositionstart":var w="onCompositionStart";break e;case"compositionend":w="onCompositionEnd";break e;case"compositionupdate":w="onCompositionUpdate";break e}w=void 0}else Nn?zn(e,n)&&(w="onCompositionEnd"):"keydown"===e&&229===n.keyCode&&(w="onCompositionStart");w&&(Cn&&"ko"!==n.locale&&(Nn||"onCompositionStart"!==w?"onCompositionEnd"===w&&Nn&&(b=Bt()):(Ut="value"in($t=a)?$t.value:$t.textContent,Nn=!0)),0<(v=Bc(r,w)).length&&(w=new sn(w,e,null,n,a),o.push({event:w,listeners:v}),(b||null!==(b=_n(n)))&&(w.data=b))),(b=xn?function(e,t){switch(e){case"compositionend":return _n(t);case"keypress":return 32!==t.which?null:(Pn=!0,En);case"textInput":return(e=t.data)===En&&Pn?null:e;default:return null}}(e,n):function(e,t){if(Nn)return"compositionend"===e||!kn&&zn(e,t)?(e=Bt(),Ht=Ut=$t=null,Nn=!1,e):null;switch(e){case"paste":default:return null;case"keypress":if(!(t.ctrlKey||t.altKey||t.metaKey)||t.ctrlKey&&t.altKey){if(t.char&&1<t.char.length)return t.char;if(t.which)return String.fromCharCode(t.which)}return null;case"compositionend":return Cn&&"ko"!==t.locale?null:t.data}}(e,n))&&0<(w=Bc(r,"onBeforeInput")).length&&(v=new sn("onBeforeInput","beforeinput",null,n,a),o.push({event:v,listeners:w}),v.data=b),function(e,t,n,r,a){if("submit"===t&&n&&n.stateNode===a){var l=Nc((a[Oe]||null).action),o=r.submitter;o&&null!==(t=(t=o[Oe]||null)?Nc(t.formAction):o.getAttribute("formAction"))&&(l=t,o=null);var i=new Zt("action","action",null,r,a);e.push({event:i,listeners:[{instance:null,listener:function(){if(r.defaultPrevented){if(0!==wc){var e=o?Tc(a,o):new FormData(a);To(n,{pending:!0,data:e,method:a.method,action:l},null,e)}}else"function"==typeof l&&(i.preventDefault(),e=o?Tc(a,o):new FormData(a),To(n,{pending:!0,data:e,method:a.method,action:l},l,e))},currentTarget:a}]})}}(o,e,r,n,a)}Ac(o,t)})}function Hc(e,t,n){return{instance:e,listener:t,currentTarget:n}}function Bc(e,t){for(var n=t+"Capture",r=[];null!==e;){var a=e,l=a.stateNode;if(5!==(a=a.tag)&&26!==a&&27!==a||null===l||(null!=(a=Dt(e,n))&&r.unshift(Hc(e,a,l)),null!=(a=Dt(e,t))&&r.push(Hc(e,a,l))),3===e.tag)return r;e=e.return}return[]}function Vc(e){if(null===e)return null;do{e=e.return}while(e&&5!==e.tag&&27!==e.tag);return e||null}function Wc(e,t,n,r,a){for(var l=t._reactName,o=[];null!==n&&n!==r;){var i=n,s=i.alternate,u=i.stateNode;if(i=i.tag,null!==s&&s===r)break;5!==i&&26!==i&&27!==i||null===u||(s=u,a?null!=(u=Dt(n,l))&&o.unshift(Hc(n,u,s)):a||null!=(u=Dt(n,l))&&o.push(Hc(n,u,s))),n=n.return}0!==o.length&&e.push({event:t,listeners:o})}var Qc=/\r\n?/g,qc=/\u0000|\uFFFD/g;function Gc(e){return("string"==typeof e?e:""+e).replace(Qc,"\n").replace(qc,"")}function Yc(e,t){return t=Gc(t),Gc(e)===t}function Kc(){}function Xc(e,t,n,r,a,l){switch(n){case"children":"string"==typeof r?"body"===t||"textarea"===t&&""===r||kt(e,r):("number"==typeof r||"bigint"==typeof r)&&"body"!==t&&kt(e,""+r);break;case"className":tt(e,"class",r);break;case"tabIndex":tt(e,"tabindex",r);break;case"dir":case"role":case"viewBox":case"width":case"height":tt(e,n,r);break;case"style":Ct(e,r,l);break;case"data":if("object"!==t){tt(e,"data",r);break}case"src":case"href":if(""===r&&("a"!==t||"href"!==n)){e.removeAttribute(n);break}if(null==r||"function"==typeof r||"symbol"==typeof r||"boolean"==typeof r){e.removeAttribute(n);break}r=_t(""+r),e.setAttribute(n,r);break;case"action":case"formAction":if("function"==typeof r){e.setAttribute(n,"javascript:throw new Error('A React form was unexpectedly submitted. If you called form.submit() manually, consider using form.requestSubmit() instead. If you\\'re trying to use event.stopPropagation() in a submit event handler, consider also calling event.preventDefault().')");break}if("function"==typeof l&&("formAction"===n?("input"!==t&&Xc(e,t,"name",a.name,a,null),Xc(e,t,"formEncType",a.formEncType,a,null),Xc(e,t,"formMethod",a.formMethod,a,null),Xc(e,t,"formTarget",a.formTarget,a,null)):(Xc(e,t,"encType",a.encType,a,null),Xc(e,t,"method",a.method,a,null),Xc(e,t,"target",a.target,a,null))),null==r||"symbol"==typeof r||"boolean"==typeof r){e.removeAttribute(n);break}r=_t(""+r),e.setAttribute(n,r);break;case"onClick":null!=r&&(e.onclick=Kc);break;case"onScroll":null!=r&&Dc("scroll",e);break;case"onScrollEnd":null!=r&&Dc("scrollend",e);break;case"dangerouslySetInnerHTML":if(null!=r){if("object"!=typeof r||!("__html"in r))throw Error(o(61));if(null!=(n=r.__html)){if(null!=a.children)throw Error(o(60));e.innerHTML=n}}break;case"multiple":e.multiple=r&&"function"!=typeof r&&"symbol"!=typeof r;break;case"muted":e.muted=r&&"function"!=typeof r&&"symbol"!=typeof r;break;case"suppressContentEditableWarning":case"suppressHydrationWarning":case"defaultValue":case"defaultChecked":case"innerHTML":case"ref":case"autoFocus":break;case"xlinkHref":if(null==r||"function"==typeof r||"boolean"==typeof r||"symbol"==typeof r){e.removeAttribute("xlink:href");break}n=_t(""+r),e.setAttributeNS("http://www.w3.org/1999/xlink","xlink:href",n);break;case"contentEditable":case"spellCheck":case"draggable":case"value":case"autoReverse":case"externalResourcesRequired":case"focusable":case"preserveAlpha":null!=r&&"function"!=typeof r&&"symbol"!=typeof r?e.setAttribute(n,""+r):e.removeAttribute(n);break;case"inert":case"allowFullScreen":case"async":case"autoPlay":case"controls":case"default":case"defer":case"disabled":case"disablePictureInPicture":case"disableRemotePlayback":case"formNoValidate":case"hidden":case"loop":case"noModule":case"noValidate":case"open":case"playsInline":case"readOnly":case"required":case"reversed":case"scoped":case"seamless":case"itemScope":r&&"function"!=typeof r&&"symbol"!=typeof r?e.setAttribute(n,""):e.removeAttribute(n);break;case"capture":case"download":!0===r?e.setAttribute(n,""):!1!==r&&null!=r&&"function"!=typeof r&&"symbol"!=typeof r?e.setAttribute(n,r):e.removeAttribute(n);break;case"cols":case"rows":case"size":case"span":null!=r&&"function"!=typeof r&&"symbol"!=typeof r&&!isNaN(r)&&1<=r?e.setAttribute(n,r):e.removeAttribute(n);break;case"rowSpan":case"start":null==r||"function"==typeof r||"symbol"==typeof r||isNaN(r)?e.removeAttribute(n):e.setAttribute(n,r);break;case"popover":Dc("beforetoggle",e),Dc("toggle",e),et(e,"popover",r);break;case"xlinkActuate":nt(e,"http://www.w3.org/1999/xlink","xlink:actuate",r);break;case"xlinkArcrole":nt(e,"http://www.w3.org/1999/xlink","xlink:arcrole",r);break;case"xlinkRole":nt(e,"http://www.w3.org/1999/xlink","xlink:role",r);break;case"xlinkShow":nt(e,"http://www.w3.org/1999/xlink","xlink:show",r);break;case"xlinkTitle":nt(e,"http://www.w3.org/1999/xlink","xlink:title",r);break;case"xlinkType":nt(e,"http://www.w3.org/1999/xlink","xlink:type",r);break;case"xmlBase":nt(e,"http://www.w3.org/XML/1998/namespace","xml:base",r);break;case"xmlLang":nt(e,"http://www.w3.org/XML/1998/namespace","xml:lang",r);break;case"xmlSpace":nt(e,"http://www.w3.org/XML/1998/namespace","xml:space",r);break;case"is":et(e,"is",r);break;case"innerText":case"textContent":break;default:(!(2<n.length)||"o"!==n[0]&&"O"!==n[0]||"n"!==n[1]&&"N"!==n[1])&&et(e,n=Pt.get(n)||n,r)}}function Zc(e,t,n,r,a,l){switch(n){case"style":Ct(e,r,l);break;case"dangerouslySetInnerHTML":if(null!=r){if("object"!=typeof r||!("__html"in r))throw Error(o(61));if(null!=(n=r.__html)){if(null!=a.children)throw Error(o(60));e.innerHTML=n}}break;case"children":"string"==typeof r?kt(e,r):("number"==typeof r||"bigint"==typeof r)&&kt(e,""+r);break;case"onScroll":null!=r&&Dc("scroll",e);break;case"onScrollEnd":null!=r&&Dc("scrollend",e);break;case"onClick":null!=r&&(e.onclick=Kc);break;case"suppressContentEditableWarning":case"suppressHydrationWarning":case"innerHTML":case"ref":case"innerText":case"textContent":break;default:Qe.hasOwnProperty(n)||("o"!==n[0]||"n"!==n[1]||(a=n.endsWith("Capture"),t=n.slice(2,a?n.length-7:void 0),"function"==typeof(l=null!=(l=e[Oe]||null)?l[n]:null)&&e.removeEventListener(t,l,a),"function"!=typeof r)?n in e?e[n]=r:!0===r?e.setAttribute(n,""):et(e,n,r):("function"!=typeof l&&null!==l&&(n in e?e[n]=null:e.hasAttribute(n)&&e.removeAttribute(n)),e.addEventListener(t,r,a)))}}function Jc(e,t,n){switch(t){case"div":case"span":case"svg":case"path":case"a":case"g":case"p":case"li":break;case"img":Dc("error",e),Dc("load",e);var r,a=!1,l=!1;for(r in n)if(n.hasOwnProperty(r)){var i=n[r];if(null!=i)switch(r){case"src":a=!0;break;case"srcSet":l=!0;break;case"children":case"dangerouslySetInnerHTML":throw Error(o(137,t));default:Xc(e,t,r,i,n,null)}}return l&&Xc(e,t,"srcSet",n.srcSet,n,null),void(a&&Xc(e,t,"src",n.src,n,null));case"input":Dc("invalid",e);var s=r=i=l=null,u=null,c=null;for(a in n)if(n.hasOwnProperty(a)){var d=n[a];if(null!=d)switch(a){case"name":l=d;break;case"type":i=d;break;case"checked":u=d;break;case"defaultChecked":c=d;break;case"value":r=d;break;case"defaultValue":s=d;break;case"children":case"dangerouslySetInnerHTML":if(null!=d)throw Error(o(137,t));break;default:Xc(e,t,a,d,n,null)}}return gt(e,r,s,u,c,i,l,!1),void ct(e);case"select":for(l in Dc("invalid",e),a=i=r=null,n)if(n.hasOwnProperty(l)&&null!=(s=n[l]))switch(l){case"value":r=s;break;case"defaultValue":i=s;break;case"multiple":a=s;default:Xc(e,t,l,s,n,null)}return t=r,n=i,e.multiple=!!a,void(null!=t?vt(e,!!a,t,!1):null!=n&&vt(e,!!a,n,!0));case"textarea":for(i in Dc("invalid",e),r=l=a=null,n)if(n.hasOwnProperty(i)&&null!=(s=n[i]))switch(i){case"value":a=s;break;case"defaultValue":l=s;break;case"children":r=s;break;case"dangerouslySetInnerHTML":if(null!=s)throw Error(o(91));break;default:Xc(e,t,i,s,n,null)}return wt(e,a,l,r),void ct(e);case"option":for(u in n)n.hasOwnProperty(u)&&null!=(a=n[u])&&("selected"===u?e.selected=a&&"function"!=typeof a&&"symbol"!=typeof a:Xc(e,t,u,a,n,null));return;case"dialog":Dc("beforetoggle",e),Dc("toggle",e),Dc("cancel",e),Dc("close",e);break;case"iframe":case"object":Dc("load",e);break;case"video":case"audio":for(a=0;a<jc.length;a++)Dc(jc[a],e);break;case"image":Dc("error",e),Dc("load",e);break;case"details":Dc("toggle",e);break;case"embed":case"source":case"link":Dc("error",e),Dc("load",e);case"area":case"base":case"br":case"col":case"hr":case"keygen":case"meta":case"param":case"track":case"wbr":case"menuitem":for(c in n)if(n.hasOwnProperty(c)&&null!=(a=n[c]))switch(c){case"children":case"dangerouslySetInnerHTML":throw Error(o(137,t));default:Xc(e,t,c,a,n,null)}return;default:if(Et(t)){for(d in n)n.hasOwnProperty(d)&&void 0!==(a=n[d])&&Zc(e,t,d,a,n,void 0);return}}for(s in n)n.hasOwnProperty(s)&&null!=(a=n[s])&&Xc(e,t,s,a,n,null)}var ed=null,td=null;function nd(e){return 9===e.nodeType?e:e.ownerDocument}function rd(e){switch(e){case"http://www.w3.org/2000/svg":return 1;case"http://www.w3.org/1998/Math/MathML":return 2;default:return 0}}function ad(e,t){if(0===e)switch(t){case"svg":return 1;case"math":return 2;default:return 0}return 1===e&&"foreignObject"===t?0:e}function ld(e,t){return"textarea"===e||"noscript"===e||"string"==typeof t.children||"number"==typeof t.children||"bigint"==typeof t.children||"object"==typeof t.dangerouslySetInnerHTML&&null!==t.dangerouslySetInnerHTML&&null!=t.dangerouslySetInnerHTML.__html}var od=null,id="function"==typeof setTimeout?setTimeout:void 0,sd="function"==typeof clearTimeout?clearTimeout:void 0,ud="function"==typeof Promise?Promise:void 0,cd="function"==typeof queueMicrotask?queueMicrotask:void 0!==ud?function(e){return ud.resolve(null).then(e).catch(dd)}:id;function dd(e){setTimeout(function(){throw e})}function fd(e){return"head"===e}function pd(e,t){var n=t,r=0,a=0;do{var l=n.nextSibling;if(e.removeChild(n),l&&8===l.nodeType)if("/$"===(n=l.data)){if(0<r&&8>r){n=r;var o=e.ownerDocument;if(1&n&&wd(o.documentElement),2&n&&wd(o.body),4&n)for(wd(n=o.head),o=n.firstChild;o;){var i=o.nextSibling,s=o.nodeName;o[Fe]||"SCRIPT"===s||"STYLE"===s||"LINK"===s&&"stylesheet"===o.rel.toLowerCase()||n.removeChild(o),o=i}}if(0===a)return e.removeChild(l),void Ef(t);a--}else"$"===n||"$?"===n||"$!"===n?a++:r=n.charCodeAt(0)-48;else r=0;n=l}while(n);Ef(t)}function hd(e){var t=e.firstChild;for(t&&10===t.nodeType&&(t=t.nextSibling);t;){var n=t;switch(t=t.nextSibling,n.nodeName){case"HTML":case"HEAD":case"BODY":hd(n),Me(n);continue;case"SCRIPT":case"STYLE":continue;case"LINK":if("stylesheet"===n.rel.toLowerCase())continue}e.removeChild(n)}}function md(e){return"$!"===e.data||"$?"===e.data&&"complete"===e.ownerDocument.readyState}function gd(e){for(;null!=e;e=e.nextSibling){var t=e.nodeType;if(1===t||3===t)break;if(8===t){if("$"===(t=e.data)||"$!"===t||"$?"===t||"F!"===t||"F"===t)break;if("/$"===t)return null}}return e}var yd=null;function vd(e){e=e.previousSibling;for(var t=0;e;){if(8===e.nodeType){var n=e.data;if("$"===n||"$!"===n||"$?"===n){if(0===t)return e;t--}else"/$"===n&&t++}e=e.previousSibling}return null}function bd(e,t,n){switch(t=nd(n),e){case"html":if(!(e=t.documentElement))throw Error(o(452));return e;case"head":if(!(e=t.head))throw Error(o(453));return e;case"body":if(!(e=t.body))throw Error(o(454));return e;default:throw Error(o(451))}}function wd(e){for(var t=e.attributes;t.length;)e.removeAttributeNode(t[0]);Me(e)}var kd=new Map,Sd=new Set;function xd(e){return"function"==typeof e.getRootNode?e.getRootNode():9===e.nodeType?e:e.ownerDocument}var Cd=R.d;R.d={f:function(){var e=Cd.f(),t=$u();return e||t},r:function(e){var t=Ue(e);null!==t&&5===t.tag&&"form"===t.type?Oo(t):Cd.r(e)},D:function(e){Cd.D(e),Pd("dns-prefetch",e,null)},C:function(e,t){Cd.C(e,t),Pd("preconnect",e,t)},L:function(e,t,n){Cd.L(e,t,n);var r=Ed;if(r&&e&&t){var a='link[rel="preload"][as="'+ht(t)+'"]';"image"===t&&n&&n.imageSrcSet?(a+='[imagesrcset="'+ht(n.imageSrcSet)+'"]',"string"==typeof n.imageSizes&&(a+='[imagesizes="'+ht(n.imageSizes)+'"]')):a+='[href="'+ht(e)+'"]';var l=a;switch(t){case"style":l=_d(e);break;case"script":l=Ld(e)}kd.has(l)||(e=d({rel:"preload",href:"image"===t&&n&&n.imageSrcSet?void 0:e,as:t},n),kd.set(l,e),null!==r.querySelector(a)||"style"===t&&r.querySelector(Nd(l))||"script"===t&&r.querySelector(Od(l))||(Jc(t=r.createElement("link"),"link",e),Ve(t),r.head.appendChild(t)))}},m:function(e,t){Cd.m(e,t);var n=Ed;if(n&&e){var r=t&&"string"==typeof t.as?t.as:"script",a='link[rel="modulepreload"][as="'+ht(r)+'"][href="'+ht(e)+'"]',l=a;switch(r){case"audioworklet":case"paintworklet":case"serviceworker":case"sharedworker":case"worker":case"script":l=Ld(e)}if(!kd.has(l)&&(e=d({rel:"modulepreload",href:e},t),kd.set(l,e),null===n.querySelector(a))){switch(r){case"audioworklet":case"paintworklet":case"serviceworker":case"sharedworker":case"worker":case"script":if(n.querySelector(Od(l)))return}Jc(r=n.createElement("link"),"link",e),Ve(r),n.head.appendChild(r)}}},X:function(e,t){Cd.X(e,t);var n=Ed;if(n&&e){var r=Be(n).hoistableScripts,a=Ld(e),l=r.get(a);l||((l=n.querySelector(Od(a)))||(e=d({src:e,async:!0},t),(t=kd.get(a))&&Dd(e,t),Ve(l=n.createElement("script")),Jc(l,"link",e),n.head.appendChild(l)),l={type:"script",instance:l,count:1,state:null},r.set(a,l))}},S:function(e,t,n){Cd.S(e,t,n);var r=Ed;if(r&&e){var a=Be(r).hoistableStyles,l=_d(e);t=t||"default";var o=a.get(l);if(!o){var i={loading:0,preload:null};if(o=r.querySelector(Nd(l)))i.loading=5;else{e=d({rel:"stylesheet",href:e,"data-precedence":t},n),(n=kd.get(l))&&Ad(e,n);var s=o=r.createElement("link");Ve(s),Jc(s,"link",e),s._p=new Promise(function(e,t){s.onload=e,s.onerror=t}),s.addEventListener("load",function(){i.loading|=1}),s.addEventListener("error",function(){i.loading|=2}),i.loading|=4,Rd(o,t,r)}o={type:"stylesheet",instance:o,count:1,state:i},a.set(l,o)}}},M:function(e,t){Cd.M(e,t);var n=Ed;if(n&&e){var r=Be(n).hoistableScripts,a=Ld(e),l=r.get(a);l||((l=n.querySelector(Od(a)))||(e=d({src:e,async:!0,type:"module"},t),(t=kd.get(a))&&Dd(e,t),Ve(l=n.createElement("script")),Jc(l,"link",e),n.head.appendChild(l)),l={type:"script",instance:l,count:1,state:null},r.set(a,l))}}};var Ed="undefined"==typeof document?null:document;function Pd(e,t,n){var r=Ed;if(r&&"string"==typeof t&&t){var a=ht(t);a='link[rel="'+e+'"][href="'+a+'"]',"string"==typeof n&&(a+='[crossorigin="'+n+'"]'),Sd.has(a)||(Sd.add(a),e={rel:e,crossOrigin:n,href:t},null===r.querySelector(a)&&(Jc(t=r.createElement("link"),"link",e),Ve(t),r.head.appendChild(t)))}}function zd(e,t,n,r){var a,l,i,s,u=(u=B.current)?xd(u):null;if(!u)throw Error(o(446));switch(e){case"meta":case"title":return null;case"style":return"string"==typeof n.precedence&&"string"==typeof n.href?(t=_d(n.href),(r=(n=Be(u).hoistableStyles).get(t))||(r={type:"style",instance:null,count:0,state:null},n.set(t,r)),r):{type:"void",instance:null,count:0,state:null};case"link":if("stylesheet"===n.rel&&"string"==typeof n.href&&"string"==typeof n.precedence){e=_d(n.href);var c=Be(u).hoistableStyles,d=c.get(e);if(d||(u=u.ownerDocument||u,d={type:"stylesheet",instance:null,count:0,state:{loading:0,preload:null}},c.set(e,d),(c=u.querySelector(Nd(e)))&&!c._p&&(d.instance=c,d.state.loading=5),kd.has(e)||(n={rel:"preload",as:"style",href:n.href,crossOrigin:n.crossOrigin,integrity:n.integrity,media:n.media,hrefLang:n.hrefLang,referrerPolicy:n.referrerPolicy},kd.set(e,n),c||(a=u,l=e,i=n,s=d.state,a.querySelector('link[rel="preload"][as="style"]['+l+"]")?s.loading=1:(l=a.createElement("link"),s.preload=l,l.addEventListener("load",function(){return s.loading|=1}),l.addEventListener("error",function(){return s.loading|=2}),Jc(l,"link",i),Ve(l),a.head.appendChild(l))))),t&&null===r)throw Error(o(528,""));return d}if(t&&null!==r)throw Error(o(529,""));return null;case"script":return t=n.async,"string"==typeof(n=n.src)&&t&&"function"!=typeof t&&"symbol"!=typeof t?(t=Ld(n),(r=(n=Be(u).hoistableScripts).get(t))||(r={type:"script",instance:null,count:0,state:null},n.set(t,r)),r):{type:"void",instance:null,count:0,state:null};default:throw Error(o(444,e))}}function _d(e){return'href="'+ht(e)+'"'}function Nd(e){return'link[rel="stylesheet"]['+e+"]"}function Td(e){return d({},e,{"data-precedence":e.precedence,precedence:null})}function Ld(e){return'[src="'+ht(e)+'"]'}function Od(e){return"script[async]"+e}function jd(e,t,n){if(t.count++,null===t.instance)switch(t.type){case"style":var r=e.querySelector('style[data-href~="'+ht(n.href)+'"]');if(r)return t.instance=r,Ve(r),r;var a=d({},n,{"data-href":n.href,"data-precedence":n.precedence,href:null,precedence:null});return Ve(r=(e.ownerDocument||e).createElement("style")),Jc(r,"style",a),Rd(r,n.precedence,e),t.instance=r;case"stylesheet":a=_d(n.href);var l=e.querySelector(Nd(a));if(l)return t.state.loading|=4,t.instance=l,Ve(l),l;r=Td(n),(a=kd.get(a))&&Ad(r,a),Ve(l=(e.ownerDocument||e).createElement("link"));var i=l;return i._p=new Promise(function(e,t){i.onload=e,i.onerror=t}),Jc(l,"link",r),t.state.loading|=4,Rd(l,n.precedence,e),t.instance=l;case"script":return l=Ld(n.src),(a=e.querySelector(Od(l)))?(t.instance=a,Ve(a),a):(r=n,(a=kd.get(l))&&Dd(r=d({},n),a),Ve(a=(e=e.ownerDocument||e).createElement("script")),Jc(a,"link",r),e.head.appendChild(a),t.instance=a);case"void":return null;default:throw Error(o(443,t.type))}else"stylesheet"===t.type&&!(4&t.state.loading)&&(r=t.instance,t.state.loading|=4,Rd(r,n.precedence,e));return t.instance}function Rd(e,t,n){for(var r=n.querySelectorAll('link[rel="stylesheet"][data-precedence],style[data-precedence]'),a=r.length?r[r.length-1]:null,l=a,o=0;o<r.length;o++){var i=r[o];if(i.dataset.precedence===t)l=i;else if(l!==a)break}l?l.parentNode.insertBefore(e,l.nextSibling):(t=9===n.nodeType?n.head:n).insertBefore(e,t.firstChild)}function Ad(e,t){null==e.crossOrigin&&(e.crossOrigin=t.crossOrigin),null==e.referrerPolicy&&(e.referrerPolicy=t.referrerPolicy),null==e.title&&(e.title=t.title)}function Dd(e,t){null==e.crossOrigin&&(e.crossOrigin=t.crossOrigin),null==e.referrerPolicy&&(e.referrerPolicy=t.referrerPolicy),null==e.integrity&&(e.integrity=t.integrity)}var Id=null;function Fd(e,t,n){if(null===Id){var r=new Map,a=Id=new Map;a.set(n,r)}else(r=(a=Id).get(n))||(r=new Map,a.set(n,r));if(r.has(e))return r;for(r.set(e,null),n=n.getElementsByTagName(e),a=0;a<n.length;a++){var l=n[a];if(!(l[Fe]||l[Le]||"link"===e&&"stylesheet"===l.getAttribute("rel"))&&"http://www.w3.org/2000/svg"!==l.namespaceURI){var o=l.getAttribute(t)||"";o=e+o;var i=r.get(o);i?i.push(l):r.set(o,[l])}}return r}function Md(e,t,n){(e=e.ownerDocument||e).head.insertBefore(n,"title"===t?e.querySelector("head > title"):null)}function $d(e){return!!("stylesheet"!==e.type||3&e.state.loading)}var Ud=null;function Hd(){}function Bd(){if(this.count--,0===this.count)if(this.stylesheets)Wd(this,this.stylesheets);else if(this.unsuspend){var e=this.unsuspend;this.unsuspend=null,e()}}var Vd=null;function Wd(e,t){e.stylesheets=null,null!==e.unsuspend&&(e.count++,Vd=new Map,t.forEach(Qd,e),Vd=null,Bd.call(e))}function Qd(e,t){if(!(4&t.state.loading)){var n=Vd.get(e);if(n)var r=n.get(null);else{n=new Map,Vd.set(e,n);for(var a=e.querySelectorAll("link[data-precedence],style[data-precedence]"),l=0;l<a.length;l++){var o=a[l];"LINK"!==o.nodeName&&"not all"===o.getAttribute("media")||(n.set(o.dataset.precedence,o),r=o)}r&&n.set(null,r)}o=(a=t.instance).getAttribute("data-precedence"),(l=n.get(o)||r)===r&&n.set(null,a),n.set(o,a),this.count++,r=Bd.bind(this),a.addEventListener("load",r),a.addEventListener("error",r),l?l.parentNode.insertBefore(a,l.nextSibling):(e=9===e.nodeType?e.head:e).insertBefore(a,e.firstChild),t.state.loading|=4}}var qd={$$typeof:w,Provider:null,Consumer:null,_currentValue:A,_currentValue2:A,_threadCount:0};function Gd(e,t,n,r,a,l,o,i){this.tag=1,this.containerInfo=e,this.pingCache=this.current=this.pendingChildren=null,this.timeoutHandle=-1,this.callbackNode=this.next=this.pendingContext=this.context=this.cancelPendingCommit=null,this.callbackPriority=0,this.expirationTimes=xe(-1),this.entangledLanes=this.shellSuspendCounter=this.errorRecoveryDisabledLanes=this.expiredLanes=this.warmLanes=this.pingedLanes=this.suspendedLanes=this.pendingLanes=0,this.entanglements=xe(0),this.hiddenUpdates=xe(null),this.identifierPrefix=r,this.onUncaughtError=a,this.onCaughtError=l,this.onRecoverableError=o,this.pooledCache=null,this.pooledCacheLanes=0,this.formState=i,this.incompleteTransitions=new Map}function Yd(e,t,n,r,a,l){a=function(e){return e?e=jr:jr}(a),null===r.context?r.context=a:r.pendingContext=a,(r=rl(t)).payload={element:n},null!==(l=void 0===l?null:l)&&(r.callback=l),null!==(n=al(e,r,t))&&(Au(n,0,t),ll(n,e,t))}function Kd(e,t){if(null!==(e=e.memoizedState)&&null!==e.dehydrated){var n=e.retryLane;e.retryLane=0!==n&&n<t?n:t}}function Xd(e,t){Kd(e,t),(e=e.alternate)&&Kd(e,t)}function Zd(e){if(13===e.tag){var t=Tr(e,67108864);null!==t&&Au(t,0,67108864),Xd(e,67108864)}}var Jd=!0;function ef(e,t,n,r){var a=j.T;j.T=null;var l=R.p;try{R.p=2,nf(e,t,n,r)}finally{R.p=l,j.T=a}}function tf(e,t,n,r){var a=j.T;j.T=null;var l=R.p;try{R.p=8,nf(e,t,n,r)}finally{R.p=l,j.T=a}}function nf(e,t,n,r){if(Jd){var a=rf(r);if(null===a)Uc(e,t,r,af,n),gf(e,r);else if(function(e,t,n,r,a){switch(t){case"focusin":return uf=yf(uf,e,t,n,r,a),!0;case"dragenter":return cf=yf(cf,e,t,n,r,a),!0;case"mouseover":return df=yf(df,e,t,n,r,a),!0;case"pointerover":var l=a.pointerId;return ff.set(l,yf(ff.get(l)||null,e,t,n,r,a)),!0;case"gotpointercapture":return l=a.pointerId,pf.set(l,yf(pf.get(l)||null,e,t,n,r,a)),!0}return!1}(a,e,t,n,r))r.stopPropagation();else if(gf(e,r),4&t&&-1<mf.indexOf(e)){for(;null!==a;){var l=Ue(a);if(null!==l)switch(l.tag){case 3:if((l=l.stateNode).current.memoizedState.isDehydrated){var o=ye(l.pendingLanes);if(0!==o){var i=l;for(i.pendingLanes|=2,i.entangledLanes|=2;o;){var s=1<<31-fe(o);i.entanglements[1]|=s,o&=~s}kc(l),!(6&tu)&&(ku=ee()+500,Sc(0,!1))}}break;case 13:null!==(i=Tr(l,2))&&Au(i,0,2),$u(),Xd(l,2)}if(null===(l=rf(r))&&Uc(e,t,r,af,n),l===a)break;a=l}null!==a&&r.stopPropagation()}else Uc(e,t,r,null,n)}}function rf(e){return lf(e=Tt(e))}var af=null;function lf(e){if(af=null,null!==(e=$e(e))){var t=i(e);if(null===t)e=null;else{var n=t.tag;if(13===n){if(null!==(e=s(t)))return e;e=null}else if(3===n){if(t.stateNode.current.memoizedState.isDehydrated)return 3===t.tag?t.stateNode.containerInfo:null;e=null}else t!==e&&(e=null)}}return af=e,null}function of(e){switch(e){case"beforetoggle":case"cancel":case"click":case"close":case"contextmenu":case"copy":case"cut":case"auxclick":case"dblclick":case"dragend":case"dragstart":case"drop":case"focusin":case"focusout":case"input":case"invalid":case"keydown":case"keypress":case"keyup":case"mousedown":case"mouseup":case"paste":case"pause":case"play":case"pointercancel":case"pointerdown":case"pointerup":case"ratechange":case"reset":case"resize":case"seeked":case"submit":case"toggle":case"touchcancel":case"touchend":case"touchstart":case"volumechange":case"change":case"selectionchange":case"textInput":case"compositionstart":case"compositionend":case"compositionupdate":case"beforeblur":case"afterblur":case"beforeinput":case"blur":case"fullscreenchange":case"focus":case"hashchange":case"popstate":case"select":case"selectstart":return 2;case"drag":case"dragenter":case"dragexit":case"dragleave":case"dragover":case"mousemove":case"mouseout":case"mouseover":case"pointermove":case"pointerout":case"pointerover":case"scroll":case"touchmove":case"wheel":case"mouseenter":case"mouseleave":case"pointerenter":case"pointerleave":return 8;case"message":switch(te()){case ne:return 2;case re:return 8;case ae:case le:return 32;case oe:return 268435456;default:return 32}default:return 32}}var sf=!1,uf=null,cf=null,df=null,ff=new Map,pf=new Map,hf=[],mf="mousedown mouseup touchcancel touchend touchstart auxclick dblclick pointercancel pointerdown pointerup dragend dragstart drop compositionend compositionstart keydown keypress keyup input textInput copy cut paste click change contextmenu reset".split(" ");function gf(e,t){switch(e){case"focusin":case"focusout":uf=null;break;case"dragenter":case"dragleave":cf=null;break;case"mouseover":case"mouseout":df=null;break;case"pointerover":case"pointerout":ff.delete(t.pointerId);break;case"gotpointercapture":case"lostpointercapture":pf.delete(t.pointerId)}}function yf(e,t,n,r,a,l){return null===e||e.nativeEvent!==l?(e={blockedOn:t,domEventName:n,eventSystemFlags:r,nativeEvent:l,targetContainers:[a]},null!==t&&null!==(t=Ue(t))&&Zd(t),e):(e.eventSystemFlags|=r,t=e.targetContainers,null!==a&&-1===t.indexOf(a)&&t.push(a),e)}function vf(e){var t=$e(e.target);if(null!==t){var n=i(t);if(null!==n)if(13===(t=n.tag)){if(null!==(t=s(n)))return e.blockedOn=t,void function(e){var t=R.p;try{return R.p=e,function(){if(13===n.tag){var e=ju();e=ze(e);var t=Tr(n,e);null!==t&&Au(t,0,e),Xd(n,e)}}()}finally{R.p=t}}(e.priority)}else if(3===t&&n.stateNode.current.memoizedState.isDehydrated)return void(e.blockedOn=3===n.tag?n.stateNode.containerInfo:null)}e.blockedOn=null}function bf(e){if(null!==e.blockedOn)return!1;for(var t=e.targetContainers;0<t.length;){var n=rf(e.nativeEvent);if(null!==n)return null!==(t=Ue(n))&&Zd(t),e.blockedOn=n,!1;var r=new(n=e.nativeEvent).constructor(n.type,n);Nt=r,n.target.dispatchEvent(r),Nt=null,t.shift()}return!0}function wf(e,t,n){bf(e)&&n.delete(t)}function kf(){sf=!1,null!==uf&&bf(uf)&&(uf=null),null!==cf&&bf(cf)&&(cf=null),null!==df&&bf(df)&&(df=null),ff.forEach(wf),pf.forEach(wf)}function Sf(e,t){e.blockedOn===t&&(e.blockedOn=null,sf||(sf=!0,r.unstable_scheduleCallback(r.unstable_NormalPriority,kf)))}var xf=null;function Cf(e){xf!==e&&(xf=e,r.unstable_scheduleCallback(r.unstable_NormalPriority,function(){xf===e&&(xf=null);for(var t=0;t<e.length;t+=3){var n=e[t],r=e[t+1],a=e[t+2];if("function"!=typeof r){if(null===lf(r||n))continue;break}var l=Ue(n);null!==l&&(e.splice(t,3),t-=3,To(l,{pending:!0,data:a,method:n.method,action:r},r,a))}}))}function Ef(e){function t(t){return Sf(t,e)}null!==uf&&Sf(uf,e),null!==cf&&Sf(cf,e),null!==df&&Sf(df,e),ff.forEach(t),pf.forEach(t);for(var n=0;n<hf.length;n++){var r=hf[n];r.blockedOn===e&&(r.blockedOn=null)}for(;0<hf.length&&null===(n=hf[0]).blockedOn;)vf(n),null===n.blockedOn&&hf.shift();if(null!=(n=(e.ownerDocument||e).$$reactFormReplay))for(r=0;r<n.length;r+=3){var a=n[r],l=n[r+1],o=a[Oe]||null;if("function"==typeof l)o||Cf(n);else if(o){var i=null;if(l&&l.hasAttribute("formAction")){if(a=l,o=l[Oe]||null)i=o.formAction;else if(null!==lf(a))continue}else i=o.action;"function"==typeof i?n[r+1]=i:(n.splice(r,3),r-=3),Cf(n)}}}function Pf(e){this._internalRoot=e}function zf(e){this._internalRoot=e}zf.prototype.render=Pf.prototype.render=function(e){var t=this._internalRoot;if(null===t)throw Error(o(409));Yd(t.current,ju(),e,t,null,null)},zf.prototype.unmount=Pf.prototype.unmount=function(){var e=this._internalRoot;if(null!==e){this._internalRoot=null;var t=e.containerInfo;Yd(e.current,2,null,e,null,null),$u(),t[je]=null}},zf.prototype.unstable_scheduleHydration=function(e){if(e){var t=Ne();e={blockedOn:null,target:e,priority:t};for(var n=0;n<hf.length&&0!==t&&t<hf[n].priority;n++);hf.splice(n,0,e),0===n&&vf(e)}};var _f=a.version;if("19.1.0"!==_f)throw Error(o(527,_f,"19.1.0"));R.findDOMNode=function(e){var t=e._reactInternals;if(void 0===t){if("function"==typeof e.render)throw Error(o(188));throw e=Object.keys(e).join(","),Error(o(268,e))}return e=function(e){var t=e.alternate;if(!t){if(null===(t=i(e)))throw Error(o(188));return t!==e?null:e}for(var n=e,r=t;;){var a=n.return;if(null===a)break;var l=a.alternate;if(null===l){if(null!==(r=a.return)){n=r;continue}break}if(a.child===l.child){for(l=a.child;l;){if(l===n)return u(a),e;if(l===r)return u(a),t;l=l.sibling}throw Error(o(188))}if(n.return!==r.return)n=a,r=l;else{for(var s=!1,c=a.child;c;){if(c===n){s=!0,n=a,r=l;break}if(c===r){s=!0,r=a,n=l;break}c=c.sibling}if(!s){for(c=l.child;c;){if(c===n){s=!0,n=l,r=a;break}if(c===r){s=!0,r=l,n=a;break}c=c.sibling}if(!s)throw Error(o(189))}}if(n.alternate!==r)throw Error(o(190))}if(3!==n.tag)throw Error(o(188));return n.stateNode.current===n?e:t}(t),null===(e=null!==e?c(e):null)?null:e.stateNode};var Nf={bundleType:0,version:"19.1.0",rendererPackageName:"react-dom",currentDispatcherRef:j,reconcilerVersion:"19.1.0"};if("undefined"!=typeof __REACT_DEVTOOLS_GLOBAL_HOOK__){var Tf=__REACT_DEVTOOLS_GLOBAL_HOOK__;if(!Tf.isDisabled&&Tf.supportsFiber)try{ue=Tf.inject(Nf),ce=Tf}catch(e){}}t.createRoot=function(e,t){if(!(n=e)||1!==n.nodeType&&9!==n.nodeType&&11!==n.nodeType)throw Error(o(299));var n,r=!1,a="",l=yi,i=vi,s=bi;return null!=t&&(!0===t.unstable_strictMode&&(r=!0),void 0!==t.identifierPrefix&&(a=t.identifierPrefix),void 0!==t.onUncaughtError&&(l=t.onUncaughtError),void 0!==t.onCaughtError&&(i=t.onCaughtError),void 0!==t.onRecoverableError&&(s=t.onRecoverableError),void 0!==t.unstable_transitionCallbacks&&t.unstable_transitionCallbacks),t=function(e,t,n,r,a,l,o,i,s,u,c,d){return e=new Gd(e,t,n,o,i,s,u,d),t=1,!0===l&&(t|=24),l=Ar(3,null,null,t),e.current=l,l.stateNode=e,(t=Oa()).refCount++,e.pooledCache=t,t.refCount++,l.memoizedState={element:r,isDehydrated:n,cache:t},tl(l),e}(e,1,!1,null,0,r,a,l,i,s,0,null),e[je]=t.current,Mc(e),new Pf(t)}},338:(e,t,n)=>{"use strict";!function e(){if("undefined"!=typeof __REACT_DEVTOOLS_GLOBAL_HOOK__&&"function"==typeof __REACT_DEVTOOLS_GLOBAL_HOOK__.checkDCE)try{__REACT_DEVTOOLS_GLOBAL_HOOK__.checkDCE(e)}catch(e){console.error(e)}}(),e.exports=n(247)},477:(e,t)=>{"use strict";function n(e,t){var n=e.length;e.push(t);e:for(;0<n;){var r=n-1>>>1,a=e[r];if(!(0<l(a,t)))break e;e[r]=t,e[n]=a,n=r}}function r(e){return 0===e.length?null:e[0]}function a(e){if(0===e.length)return null;var t=e[0],n=e.pop();if(n!==t){e[0]=n;e:for(var r=0,a=e.length,o=a>>>1;r<o;){var i=2*(r+1)-1,s=e[i],u=i+1,c=e[u];if(0>l(s,n))u<a&&0>l(c,s)?(e[r]=c,e[u]=n,r=u):(e[r]=s,e[i]=n,r=i);else{if(!(u<a&&0>l(c,n)))break e;e[r]=c,e[u]=n,r=u}}}return t}function l(e,t){var n=e.sortIndex-t.sortIndex;return 0!==n?n:e.id-t.id}if(t.unstable_now=void 0,"object"==typeof performance&&"function"==typeof performance.now){var o=performance;t.unstable_now=function(){return o.now()}}else{var i=Date,s=i.now();t.unstable_now=function(){return i.now()-s}}var u=[],c=[],d=1,f=null,p=3,h=!1,m=!1,g=!1,y=!1,v="function"==typeof setTimeout?setTimeout:null,b="function"==typeof clearTimeout?clearTimeout:null,w="undefined"!=typeof setImmediate?setImmediate:null;function k(e){for(var t=r(c);null!==t;){if(null===t.callback)a(c);else{if(!(t.startTime<=e))break;a(c),t.sortIndex=t.expirationTime,n(u,t)}t=r(c)}}function S(e){if(g=!1,k(e),!m)if(null!==r(u))m=!0,C||(C=!0,x());else{var t=r(c);null!==t&&O(S,t.startTime-e)}}var x,C=!1,E=-1,P=5,z=-1;function _(){return!(!y&&t.unstable_now()-z<P)}function N(){if(y=!1,C){var e=t.unstable_now();z=e;var n=!0;try{e:{m=!1,g&&(g=!1,b(E),E=-1),h=!0;var l=p;try{t:{for(k(e),f=r(u);null!==f&&!(f.expirationTime>e&&_());){var o=f.callback;if("function"==typeof o){f.callback=null,p=f.priorityLevel;var i=o(f.expirationTime<=e);if(e=t.unstable_now(),"function"==typeof i){f.callback=i,k(e),n=!0;break t}f===r(u)&&a(u),k(e)}else a(u);f=r(u)}if(null!==f)n=!0;else{var s=r(c);null!==s&&O(S,s.startTime-e),n=!1}}break e}finally{f=null,p=l,h=!1}n=void 0}}finally{n?x():C=!1}}}if("function"==typeof w)x=function(){w(N)};else if("undefined"!=typeof MessageChannel){var T=new MessageChannel,L=T.port2;T.port1.onmessage=N,x=function(){L.postMessage(null)}}else x=function(){v(N,0)};function O(e,n){E=v(function(){e(t.unstable_now())},n)}t.unstable_IdlePriority=5,t.unstable_ImmediatePriority=1,t.unstable_LowPriority=4,t.unstable_NormalPriority=3,t.unstable_Profiling=null,t.unstable_UserBlockingPriority=2,t.unstable_cancelCallback=function(e){e.callback=null},t.unstable_forceFrameRate=function(e){0>e||125<e?console.error("forceFrameRate takes a positive int between 0 and 125, forcing frame rates higher than 125 fps is not supported"):P=0<e?Math.floor(1e3/e):5},t.unstable_getCurrentPriorityLevel=function(){return p},t.unstable_next=function(e){switch(p){case 1:case 2:case 3:var t=3;break;default:t=p}var n=p;p=t;try{return e()}finally{p=n}},t.unstable_requestPaint=function(){y=!0},t.unstable_runWithPriority=function(e,t){switch(e){case 1:case 2:case 3:case 4:case 5:break;default:e=3}var n=p;p=e;try{return t()}finally{p=n}},t.unstable_scheduleCallback=function(e,a,l){var o=t.unstable_now();switch(l="object"==typeof l&&null!==l&&"number"==typeof(l=l.delay)&&0<l?o+l:o,e){case 1:var i=-1;break;case 2:i=250;break;case 5:i=1073741823;break;case 4:i=1e4;break;default:i=5e3}return e={id:d++,callback:a,priorityLevel:e,startTime:l,expirationTime:i=l+i,sortIndex:-1},l>o?(e.sortIndex=l,n(c,e),null===r(u)&&e===r(c)&&(g?(b(E),E=-1):g=!0,O(S,l-o))):(e.sortIndex=i,n(u,e),m||h||(m=!0,C||(C=!0,x()))),e},t.unstable_shouldYield=_,t.unstable_wrapCallback=function(e){var t=p;return function(){var n=p;p=t;try{return e.apply(this,arguments)}finally{p=n}}}},540:(e,t,n)=>{"use strict";e.exports=n(869)},698:(e,t)=>{"use strict";var n=Symbol.for("react.transitional.element"),r=Symbol.for("react.fragment");function a(e,t,r){var a=null;if(void 0!==r&&(a=""+r),void 0!==t.key&&(a=""+t.key),"key"in t)for(var l in r={},t)"key"!==l&&(r[l]=t[l]);else r=t;return t=r.ref,{$$typeof:n,type:e,key:a,ref:void 0!==t?t:null,props:r}}t.Fragment=r,t.jsx=a,t.jsxs=a},833:e=>{e.exports=function(e,t,n,r){var a=n?n.call(r,e,t):void 0;if(void 0!==a)return!!a;if(e===t)return!0;if("object"!=typeof e||!e||"object"!=typeof t||!t)return!1;var l=Object.keys(e),o=Object.keys(t);if(l.length!==o.length)return!1;for(var i=Object.prototype.hasOwnProperty.bind(t),s=0;s<l.length;s++){var u=l[s];if(!i(u))return!1;var c=e[u],d=t[u];if(!1===(a=n?n.call(r,c,d,u):void 0)||void 0===a&&c!==d)return!1}return!0}},848:(e,t,n)=>{"use strict";e.exports=n(698)},869:(e,t)=>{"use strict";var n=Symbol.for("react.transitional.element"),r=Symbol.for("react.portal"),a=Symbol.for("react.fragment"),l=Symbol.for("react.strict_mode"),o=Symbol.for("react.profiler"),i=Symbol.for("react.consumer"),s=Symbol.for("react.context"),u=Symbol.for("react.forward_ref"),c=Symbol.for("react.suspense"),d=Symbol.for("react.memo"),f=Symbol.for("react.lazy"),p=Symbol.iterator,h={isMounted:function(){return!1},enqueueForceUpdate:function(){},enqueueReplaceState:function(){},enqueueSetState:function(){}},m=Object.assign,g={};function y(e,t,n){this.props=e,this.context=t,this.refs=g,this.updater=n||h}function v(){}function b(e,t,n){this.props=e,this.context=t,this.refs=g,this.updater=n||h}y.prototype.isReactComponent={},y.prototype.setState=function(e,t){if("object"!=typeof e&&"function"!=typeof e&&null!=e)throw Error("takes an object of state variables to update or a function which returns an object of state variables.");this.updater.enqueueSetState(this,e,t,"setState")},y.prototype.forceUpdate=function(e){this.updater.enqueueForceUpdate(this,e,"forceUpdate")},v.prototype=y.prototype;var w=b.prototype=new v;w.constructor=b,m(w,y.prototype),w.isPureReactComponent=!0;var k=Array.isArray,S={H:null,A:null,T:null,S:null,V:null},x=Object.prototype.hasOwnProperty;function C(e,t,r,a,l,o){return r=o.ref,{$$typeof:n,type:e,key:t,ref:void 0!==r?r:null,props:o}}function E(e){return"object"==typeof e&&null!==e&&e.$$typeof===n}var P=/\/+/g;function z(e,t){return"object"==typeof e&&null!==e&&null!=e.key?(n=""+e.key,r={"=":"=0",":":"=2"},"$"+n.replace(/[=:]/g,function(e){return r[e]})):t.toString(36);var n,r}function _(){}function N(e,t,a,l,o){var i=typeof e;"undefined"!==i&&"boolean"!==i||(e=null);var s,u,c=!1;if(null===e)c=!0;else switch(i){case"bigint":case"string":case"number":c=!0;break;case"object":switch(e.$$typeof){case n:case r:c=!0;break;case f:return N((c=e._init)(e._payload),t,a,l,o)}}if(c)return o=o(e),c=""===l?"."+z(e,0):l,k(o)?(a="",null!=c&&(a=c.replace(P,"$&/")+"/"),N(o,t,a,"",function(e){return e})):null!=o&&(E(o)&&(s=o,u=a+(null==o.key||e&&e.key===o.key?"":(""+o.key).replace(P,"$&/")+"/")+c,o=C(s.type,u,void 0,0,0,s.props)),t.push(o)),1;c=0;var d,h=""===l?".":l+":";if(k(e))for(var m=0;m<e.length;m++)c+=N(l=e[m],t,a,i=h+z(l,m),o);else if("function"==typeof(m=null===(d=e)||"object"!=typeof d?null:"function"==typeof(d=p&&d[p]||d["@@iterator"])?d:null))for(e=m.call(e),m=0;!(l=e.next()).done;)c+=N(l=l.value,t,a,i=h+z(l,m++),o);else if("object"===i){if("function"==typeof e.then)return N(function(e){switch(e.status){case"fulfilled":return e.value;case"rejected":throw e.reason;default:switch("string"==typeof e.status?e.then(_,_):(e.status="pending",e.then(function(t){"pending"===e.status&&(e.status="fulfilled",e.value=t)},function(t){"pending"===e.status&&(e.status="rejected",e.reason=t)})),e.status){case"fulfilled":return e.value;case"rejected":throw e.reason}}throw e}(e),t,a,l,o);throw t=String(e),Error("Objects are not valid as a React child (found: "+("[object Object]"===t?"object with keys {"+Object.keys(e).join(", ")+"}":t)+"). If you meant to render a collection of children, use an array instead.")}return c}function T(e,t,n){if(null==e)return e;var r=[],a=0;return N(e,r,"","",function(e){return t.call(n,e,a++)}),r}function L(e){if(-1===e._status){var t=e._result;(t=t()).then(function(t){0!==e._status&&-1!==e._status||(e._status=1,e._result=t)},function(t){0!==e._status&&-1!==e._status||(e._status=2,e._result=t)}),-1===e._status&&(e._status=0,e._result=t)}if(1===e._status)return e._result.default;throw e._result}var O="function"==typeof reportError?reportError:function(e){if("object"==typeof window&&"function"==typeof window.ErrorEvent){var t=new window.ErrorEvent("error",{bubbles:!0,cancelable:!0,message:"object"==typeof e&&null!==e&&"string"==typeof e.message?String(e.message):String(e),error:e});if(!window.dispatchEvent(t))return}else if("object"==typeof process&&"function"==typeof process.emit)return void process.emit("uncaughtException",e);console.error(e)};function j(){}t.Children={map:T,forEach:function(e,t,n){T(e,function(){t.apply(this,arguments)},n)},count:function(e){var t=0;return T(e,function(){t++}),t},toArray:function(e){return T(e,function(e){return e})||[]},only:function(e){if(!E(e))throw Error("React.Children.only expected to receive a single React element child.");return e}},t.Component=y,t.Fragment=a,t.Profiler=o,t.PureComponent=b,t.StrictMode=l,t.Suspense=c,t.__CLIENT_INTERNALS_DO_NOT_USE_OR_WARN_USERS_THEY_CANNOT_UPGRADE=S,t.__COMPILER_RUNTIME={__proto__:null,c:function(e){return S.H.useMemoCache(e)}},t.cache=function(e){return function(){return e.apply(null,arguments)}},t.cloneElement=function(e,t,n){if(null==e)throw Error("The argument must be a React element, but you passed "+e+".");var r=m({},e.props),a=e.key;if(null!=t)for(l in t.ref,void 0!==t.key&&(a=""+t.key),t)!x.call(t,l)||"key"===l||"__self"===l||"__source"===l||"ref"===l&&void 0===t.ref||(r[l]=t[l]);var l=arguments.length-2;if(1===l)r.children=n;else if(1<l){for(var o=Array(l),i=0;i<l;i++)o[i]=arguments[i+2];r.children=o}return C(e.type,a,void 0,0,0,r)},t.createContext=function(e){return(e={$$typeof:s,_currentValue:e,_currentValue2:e,_threadCount:0,Provider:null,Consumer:null}).Provider=e,e.Consumer={$$typeof:i,_context:e},e},t.createElement=function(e,t,n){var r,a={},l=null;if(null!=t)for(r in void 0!==t.key&&(l=""+t.key),t)x.call(t,r)&&"key"!==r&&"__self"!==r&&"__source"!==r&&(a[r]=t[r]);var o=arguments.length-2;if(1===o)a.children=n;else if(1<o){for(var i=Array(o),s=0;s<o;s++)i[s]=arguments[s+2];a.children=i}if(e&&e.defaultProps)for(r in o=e.defaultProps)void 0===a[r]&&(a[r]=o[r]);return C(e,l,void 0,0,0,a)},t.createRef=function(){return{current:null}},t.forwardRef=function(e){return{$$typeof:u,render:e}},t.isValidElement=E,t.lazy=function(e){return{$$typeof:f,_payload:{_status:-1,_result:e},_init:L}},t.memo=function(e,t){return{$$typeof:d,type:e,compare:void 0===t?null:t}},t.startTransition=function(e){var t=S.T,n={};S.T=n;try{var r=e(),a=S.S;null!==a&&a(n,r),"object"==typeof r&&null!==r&&"function"==typeof r.then&&r.then(j,O)}catch(e){O(e)}finally{S.T=t}},t.unstable_useCacheRefresh=function(){return S.H.useCacheRefresh()},t.use=function(e){return S.H.use(e)},t.useActionState=function(e,t,n){return S.H.useActionState(e,t,n)},t.useCallback=function(e,t){return S.H.useCallback(e,t)},t.useContext=function(e){return S.H.useContext(e)},t.useDebugValue=function(){},t.useDeferredValue=function(e,t){return S.H.useDeferredValue(e,t)},t.useEffect=function(e,t,n){var r=S.H;if("function"==typeof n)throw Error("useEffect CRUD overload is not enabled in this build of React.");return r.useEffect(e,t)},t.useId=function(){return S.H.useId()},t.useImperativeHandle=function(e,t,n){return S.H.useImperativeHandle(e,t,n)},t.useInsertionEffect=function(e,t){return S.H.useInsertionEffect(e,t)},t.useLayoutEffect=function(e,t){return S.H.useLayoutEffect(e,t)},t.useMemo=function(e,t){return S.H.useMemo(e,t)},t.useOptimistic=function(e,t){return S.H.useOptimistic(e,t)},t.useReducer=function(e,t,n){return S.H.useReducer(e,t,n)},t.useRef=function(e){return S.H.useRef(e)},t.useState=function(e){return S.H.useState(e)},t.useSyncExternalStore=function(e,t,n){return S.H.useSyncExternalStore(e,t,n)},t.useTransition=function(){return S.H.useTransition()},t.version="19.1.0"},961:(e,t,n)=>{"use strict";!function e(){if("undefined"!=typeof __REACT_DEVTOOLS_GLOBAL_HOOK__&&"function"==typeof __REACT_DEVTOOLS_GLOBAL_HOOK__.checkDCE)try{__REACT_DEVTOOLS_GLOBAL_HOOK__.checkDCE(e)}catch(e){console.error(e)}}(),e.exports=n(221)},982:(e,t,n)=>{"use strict";e.exports=n(477)}},t={};function n(r){var a=t[r];if(void 0!==a)return a.exports;var l=t[r]={exports:{}};return e[r](l,l.exports,n),l.exports}n.n=e=>{var t=e&&e.__esModule?()=>e.default:()=>e;return n.d(t,{a:t}),t},n.d=(e,t)=>{for(var r in t)n.o(t,r)&&!n.o(e,r)&&Object.defineProperty(e,r,{enumerable:!0,get:t[r]})},n.o=(e,t)=>Object.prototype.hasOwnProperty.call(e,t),n.nc=void 0,(()=>{"use strict";var e=n(848),t=n(338),r=n(540),a=function(){return a=Object.assign||function(e){for(var t,n=1,r=arguments.length;n<r;n++)for(var a in t=arguments[n])Object.prototype.hasOwnProperty.call(t,a)&&(e[a]=t[a]);return e},a.apply(this,arguments)};function l(e,t,n){if(n||2===arguments.length)for(var r,a=0,l=t.length;a<l;a++)!r&&a in t||(r||(r=Array.prototype.slice.call(t,0,a)),r[a]=t[a]);return e.concat(r||Array.prototype.slice.call(t))}Object.create,Object.create,"function"==typeof SuppressedError&&SuppressedError;var o=n(833),i=n.n(o),s="-ms-",u="-moz-",c="-webkit-",d="comm",f="rule",p="decl",h="@keyframes",m=Math.abs,g=String.fromCharCode,y=Object.assign;function v(e){return e.trim()}function b(e,t){return(e=t.exec(e))?e[0]:e}function w(e,t,n){return e.replace(t,n)}function k(e,t,n){return e.indexOf(t,n)}function S(e,t){return 0|e.charCodeAt(t)}function x(e,t,n){return e.slice(t,n)}function C(e){return e.length}function E(e){return e.length}function P(e,t){return t.push(e),e}function z(e,t){return e.filter(function(e){return!b(e,t)})}var _=1,N=1,T=0,L=0,O=0,j="";function R(e,t,n,r,a,l,o,i){return{value:e,root:t,parent:n,type:r,props:a,children:l,line:_,column:N,length:o,return:"",siblings:i}}function A(e,t){return y(R("",null,null,"",null,null,0,e.siblings),e,{length:-e.length},t)}function D(e){for(;e.root;)e=A(e.root,{children:[e]});P(e,e.siblings)}function I(){return O=L>0?S(j,--L):0,N--,10===O&&(N=1,_--),O}function F(){return O=L<T?S(j,L++):0,N++,10===O&&(N=1,_++),O}function M(){return S(j,L)}function $(){return L}function U(e,t){return x(j,e,t)}function H(e){switch(e){case 0:case 9:case 10:case 13:case 32:return 5;case 33:case 43:case 44:case 47:case 62:case 64:case 126:case 59:case 123:case 125:return 4;case 58:return 3;case 34:case 39:case 40:case 91:return 2;case 41:case 93:return 1}return 0}function B(e){return v(U(L-1,Q(91===e?e+2:40===e?e+1:e)))}function V(e){for(;(O=M())&&O<33;)F();return H(e)>2||H(O)>3?"":" "}function W(e,t){for(;--t&&F()&&!(O<48||O>102||O>57&&O<65||O>70&&O<97););return U(e,$()+(t<6&&32==M()&&32==F()))}function Q(e){for(;F();)switch(O){case e:return L;case 34:case 39:34!==e&&39!==e&&Q(O);break;case 40:41===e&&Q(e);break;case 92:F()}return L}function q(e,t){for(;F()&&e+O!==57&&(e+O!==84||47!==M()););return"/*"+U(t,L-1)+"*"+g(47===e?e:F())}function G(e){for(;!H(M());)F();return U(e,L)}function Y(e,t){for(var n="",r=0;r<e.length;r++)n+=t(e[r],r,e,t)||"";return n}function K(e,t,n,r){switch(e.type){case"@layer":if(e.children.length)break;case"@import":case p:return e.return=e.return||e.value;case d:return"";case h:return e.return=e.value+"{"+Y(e.children,r)+"}";case f:if(!C(e.value=e.props.join(",")))return""}return C(n=Y(e.children,r))?e.return=e.value+"{"+n+"}":""}function X(e,t,n){switch(function(e,t){return 45^S(e,0)?(((t<<2^S(e,0))<<2^S(e,1))<<2^S(e,2))<<2^S(e,3):0}(e,t)){case 5103:return c+"print-"+e+e;case 5737:case 4201:case 3177:case 3433:case 1641:case 4457:case 2921:case 5572:case 6356:case 5844:case 3191:case 6645:case 3005:case 6391:case 5879:case 5623:case 6135:case 4599:case 4855:case 4215:case 6389:case 5109:case 5365:case 5621:case 3829:return c+e+e;case 4789:return u+e+e;case 5349:case 4246:case 4810:case 6968:case 2756:return c+e+u+e+s+e+e;case 5936:switch(S(e,t+11)){case 114:return c+e+s+w(e,/[svh]\w+-[tblr]{2}/,"tb")+e;case 108:return c+e+s+w(e,/[svh]\w+-[tblr]{2}/,"tb-rl")+e;case 45:return c+e+s+w(e,/[svh]\w+-[tblr]{2}/,"lr")+e}case 6828:case 4268:case 2903:return c+e+s+e+e;case 6165:return c+e+s+"flex-"+e+e;case 5187:return c+e+w(e,/(\w+).+(:[^]+)/,c+"box-$1$2"+s+"flex-$1$2")+e;case 5443:return c+e+s+"flex-item-"+w(e,/flex-|-self/g,"")+(b(e,/flex-|baseline/)?"":s+"grid-row-"+w(e,/flex-|-self/g,""))+e;case 4675:return c+e+s+"flex-line-pack"+w(e,/align-content|flex-|-self/g,"")+e;case 5548:return c+e+s+w(e,"shrink","negative")+e;case 5292:return c+e+s+w(e,"basis","preferred-size")+e;case 6060:return c+"box-"+w(e,"-grow","")+c+e+s+w(e,"grow","positive")+e;case 4554:return c+w(e,/([^-])(transform)/g,"$1"+c+"$2")+e;case 6187:return w(w(w(e,/(zoom-|grab)/,c+"$1"),/(image-set)/,c+"$1"),e,"")+e;case 5495:case 3959:return w(e,/(image-set\([^]*)/,c+"$1$`$1");case 4968:return w(w(e,/(.+:)(flex-)?(.*)/,c+"box-pack:$3"+s+"flex-pack:$3"),/s.+-b[^;]+/,"justify")+c+e+e;case 4200:if(!b(e,/flex-|baseline/))return s+"grid-column-align"+x(e,t)+e;break;case 2592:case 3360:return s+w(e,"template-","")+e;case 4384:case 3616:return n&&n.some(function(e,n){return t=n,b(e.props,/grid-\w+-end/)})?~k(e+(n=n[t].value),"span",0)?e:s+w(e,"-start","")+e+s+"grid-row-span:"+(~k(n,"span",0)?b(n,/\d+/):+b(n,/\d+/)-+b(e,/\d+/))+";":s+w(e,"-start","")+e;case 4896:case 4128:return n&&n.some(function(e){return b(e.props,/grid-\w+-start/)})?e:s+w(w(e,"-end","-span"),"span ","")+e;case 4095:case 3583:case 4068:case 2532:return w(e,/(.+)-inline(.+)/,c+"$1$2")+e;case 8116:case 7059:case 5753:case 5535:case 5445:case 5701:case 4933:case 4677:case 5533:case 5789:case 5021:case 4765:if(C(e)-1-t>6)switch(S(e,t+1)){case 109:if(45!==S(e,t+4))break;case 102:return w(e,/(.+:)(.+)-([^]+)/,"$1"+c+"$2-$3$1"+u+(108==S(e,t+3)?"$3":"$2-$3"))+e;case 115:return~k(e,"stretch",0)?X(w(e,"stretch","fill-available"),t,n)+e:e}break;case 5152:case 5920:return w(e,/(.+?):(\d+)(\s*\/\s*(span)?\s*(\d+))?(.*)/,function(t,n,r,a,l,o,i){return s+n+":"+r+i+(a?s+n+"-span:"+(l?o:+o-+r)+i:"")+e});case 4949:if(121===S(e,t+6))return w(e,":",":"+c)+e;break;case 6444:switch(S(e,45===S(e,14)?18:11)){case 120:return w(e,/(.+:)([^;\s!]+)(;|(\s+)?!.+)?/,"$1"+c+(45===S(e,14)?"inline-":"")+"box$3$1"+c+"$2$3$1"+s+"$2box$3")+e;case 100:return w(e,":",":"+s)+e}break;case 5719:case 2647:case 2135:case 3927:case 2391:return w(e,"scroll-","scroll-snap-")+e}return e}function Z(e,t,n,r){if(e.length>-1&&!e.return)switch(e.type){case p:return void(e.return=X(e.value,e.length,n));case h:return Y([A(e,{value:w(e.value,"@","@"+c)})],r);case f:if(e.length)return function(e,t){return e.map(t).join("")}(n=e.props,function(t){switch(b(t,r=/(::plac\w+|:read-\w+)/)){case":read-only":case":read-write":D(A(e,{props:[w(t,/:(read-\w+)/,":-moz-$1")]})),D(A(e,{props:[t]})),y(e,{props:z(n,r)});break;case"::placeholder":D(A(e,{props:[w(t,/:(plac\w+)/,":"+c+"input-$1")]})),D(A(e,{props:[w(t,/:(plac\w+)/,":-moz-$1")]})),D(A(e,{props:[w(t,/:(plac\w+)/,s+"input-$1")]})),D(A(e,{props:[t]})),y(e,{props:z(n,r)})}return""})}}function J(e){return function(e){return j="",e}(ee("",null,null,null,[""],e=function(e){return _=N=1,T=C(j=e),L=0,[]}(e),0,[0],e))}function ee(e,t,n,r,a,l,o,i,s){for(var u=0,c=0,d=o,f=0,p=0,h=0,y=1,v=1,b=1,x=0,E="",z=a,_=l,N=r,T=E;v;)switch(h=x,x=F()){case 40:if(108!=h&&58==S(T,d-1)){-1!=k(T+=w(B(x),"&","&\f"),"&\f",m(u?i[u-1]:0))&&(b=-1);break}case 34:case 39:case 91:T+=B(x);break;case 9:case 10:case 13:case 32:T+=V(h);break;case 92:T+=W($()-1,7);continue;case 47:switch(M()){case 42:case 47:P(ne(q(F(),$()),t,n,s),s);break;default:T+="/"}break;case 123*y:i[u++]=C(T)*b;case 125*y:case 59:case 0:switch(x){case 0:case 125:v=0;case 59+c:-1==b&&(T=w(T,/\f/g,"")),p>0&&C(T)-d&&P(p>32?re(T+";",r,n,d-1,s):re(w(T," ","")+";",r,n,d-2,s),s);break;case 59:T+=";";default:if(P(N=te(T,t,n,u,c,a,i,E,z=[],_=[],d,l),l),123===x)if(0===c)ee(T,t,N,N,z,l,d,i,_);else switch(99===f&&110===S(T,3)?100:f){case 100:case 108:case 109:case 115:ee(e,N,N,r&&P(te(e,N,N,0,0,a,i,E,a,z=[],d,_),_),a,_,d,i,r?z:_);break;default:ee(T,N,N,N,[""],_,0,i,_)}}u=c=p=0,y=b=1,E=T="",d=o;break;case 58:d=1+C(T),p=h;default:if(y<1)if(123==x)--y;else if(125==x&&0==y++&&125==I())continue;switch(T+=g(x),x*y){case 38:b=c>0?1:(T+="\f",-1);break;case 44:i[u++]=(C(T)-1)*b,b=1;break;case 64:45===M()&&(T+=B(F())),f=M(),c=d=C(E=T+=G($())),x++;break;case 45:45===h&&2==C(T)&&(y=0)}}return l}function te(e,t,n,r,a,l,o,i,s,u,c,d){for(var p=a-1,h=0===a?l:[""],g=E(h),y=0,b=0,k=0;y<r;++y)for(var S=0,C=x(e,p+1,p=m(b=o[y])),P=e;S<g;++S)(P=v(b>0?h[S]+" "+C:w(C,/&\f/g,h[S])))&&(s[k++]=P);return R(e,t,n,0===a?f:i,s,u,c,d)}function ne(e,t,n,r){return R(e,t,n,d,g(O),x(e,2,-2),0,r)}function re(e,t,n,r,a){return R(e,t,n,p,x(e,0,r),x(e,r+1,-1),r,a)}var ae={animationIterationCount:1,aspectRatio:1,borderImageOutset:1,borderImageSlice:1,borderImageWidth:1,boxFlex:1,boxFlexGroup:1,boxOrdinalGroup:1,columnCount:1,columns:1,flex:1,flexGrow:1,flexPositive:1,flexShrink:1,flexNegative:1,flexOrder:1,gridRow:1,gridRowEnd:1,gridRowSpan:1,gridRowStart:1,gridColumn:1,gridColumnEnd:1,gridColumnSpan:1,gridColumnStart:1,msGridRow:1,msGridRowSpan:1,msGridColumn:1,msGridColumnSpan:1,fontWeight:1,lineHeight:1,opacity:1,order:1,orphans:1,tabSize:1,widows:1,zIndex:1,zoom:1,WebkitLineClamp:1,fillOpacity:1,floodOpacity:1,stopOpacity:1,strokeDasharray:1,strokeDashoffset:1,strokeMiterlimit:1,strokeOpacity:1,strokeWidth:1},le="undefined"!=typeof process&&void 0!==process.env&&(process.env.REACT_APP_SC_ATTR||process.env.SC_ATTR)||"data-styled",oe="active",ie="data-styled-version",se="6.1.19",ue="/*!sc*/\n",ce="undefined"!=typeof window&&"undefined"!=typeof document,de=Boolean("boolean"==typeof SC_DISABLE_SPEEDY?SC_DISABLE_SPEEDY:"undefined"!=typeof process&&void 0!==process.env&&void 0!==process.env.REACT_APP_SC_DISABLE_SPEEDY&&""!==process.env.REACT_APP_SC_DISABLE_SPEEDY?"false"!==process.env.REACT_APP_SC_DISABLE_SPEEDY&&process.env.REACT_APP_SC_DISABLE_SPEEDY:"undefined"!=typeof process&&void 0!==process.env&&void 0!==process.env.SC_DISABLE_SPEEDY&&""!==process.env.SC_DISABLE_SPEEDY&&"false"!==process.env.SC_DISABLE_SPEEDY&&process.env.SC_DISABLE_SPEEDY),fe=(new Set,Object.freeze([])),pe=Object.freeze({});var he=new Set(["a","abbr","address","area","article","aside","audio","b","base","bdi","bdo","big","blockquote","body","br","button","canvas","caption","cite","code","col","colgroup","data","datalist","dd","del","details","dfn","dialog","div","dl","dt","em","embed","fieldset","figcaption","figure","footer","form","h1","h2","h3","h4","h5","h6","header","hgroup","hr","html","i","iframe","img","input","ins","kbd","keygen","label","legend","li","link","main","map","mark","menu","menuitem","meta","meter","nav","noscript","object","ol","optgroup","option","output","p","param","picture","pre","progress","q","rp","rt","ruby","s","samp","script","section","select","small","source","span","strong","style","sub","summary","sup","table","tbody","td","textarea","tfoot","th","thead","time","tr","track","u","ul","use","var","video","wbr","circle","clipPath","defs","ellipse","foreignObject","g","image","line","linearGradient","marker","mask","path","pattern","polygon","polyline","radialGradient","rect","stop","svg","text","tspan"]),me=/[!"#$%&'()*+,./:;<=>?@[\\\]^`{|}~-]+/g,ge=/(^-|-$)/g;function ye(e){return e.replace(me,"-").replace(ge,"")}var ve=/(a)(d)/gi,be=function(e){return String.fromCharCode(e+(e>25?39:97))};function we(e){var t,n="";for(t=Math.abs(e);t>52;t=t/52|0)n=be(t%52)+n;return(be(t%52)+n).replace(ve,"$1-$2")}var ke,Se=function(e,t){for(var n=t.length;n;)e=33*e^t.charCodeAt(--n);return e},xe=function(e){return Se(5381,e)};function Ce(e){return"string"==typeof e&&!0}var Ee="function"==typeof Symbol&&Symbol.for,Pe=Ee?Symbol.for("react.memo"):60115,ze=Ee?Symbol.for("react.forward_ref"):60112,_e={childContextTypes:!0,contextType:!0,contextTypes:!0,defaultProps:!0,displayName:!0,getDefaultProps:!0,getDerivedStateFromError:!0,getDerivedStateFromProps:!0,mixins:!0,propTypes:!0,type:!0},Ne={name:!0,length:!0,prototype:!0,caller:!0,callee:!0,arguments:!0,arity:!0},Te={$$typeof:!0,compare:!0,defaultProps:!0,displayName:!0,propTypes:!0,type:!0},Le=((ke={})[ze]={$$typeof:!0,render:!0,defaultProps:!0,displayName:!0,propTypes:!0},ke[Pe]=Te,ke);function Oe(e){return("type"in(t=e)&&t.type.$$typeof)===Pe?Te:"$$typeof"in e?Le[e.$$typeof]:_e;var t}var je=Object.defineProperty,Re=Object.getOwnPropertyNames,Ae=Object.getOwnPropertySymbols,De=Object.getOwnPropertyDescriptor,Ie=Object.getPrototypeOf,Fe=Object.prototype;function Me(e,t,n){if("string"!=typeof t){if(Fe){var r=Ie(t);r&&r!==Fe&&Me(e,r,n)}var a=Re(t);Ae&&(a=a.concat(Ae(t)));for(var l=Oe(e),o=Oe(t),i=0;i<a.length;++i){var s=a[i];if(!(s in Ne||n&&n[s]||o&&s in o||l&&s in l)){var u=De(t,s);try{je(e,s,u)}catch(e){}}}}return e}function $e(e){return"function"==typeof e}function Ue(e){return"object"==typeof e&&"styledComponentId"in e}function He(e,t){return e&&t?"".concat(e," ").concat(t):e||t||""}function Be(e,t){if(0===e.length)return"";for(var n=e[0],r=1;r<e.length;r++)n+=t?t+e[r]:e[r];return n}function Ve(e){return null!==e&&"object"==typeof e&&e.constructor.name===Object.name&&!("props"in e&&e.$$typeof)}function We(e,t,n){if(void 0===n&&(n=!1),!n&&!Ve(e)&&!Array.isArray(e))return t;if(Array.isArray(t))for(var r=0;r<t.length;r++)e[r]=We(e[r],t[r]);else if(Ve(t))for(var r in t)e[r]=We(e[r],t[r]);return e}function Qe(e,t){Object.defineProperty(e,"toString",{value:t})}function qe(e){for(var t=[],n=1;n<arguments.length;n++)t[n-1]=arguments[n];return new Error("An error occurred. See https://github.com/styled-components/styled-components/blob/main/packages/styled-components/src/utils/errors.md#".concat(e," for more information.").concat(t.length>0?" Args: ".concat(t.join(", ")):""))}var Ge=function(){function e(e){this.groupSizes=new Uint32Array(512),this.length=512,this.tag=e}return e.prototype.indexOfGroup=function(e){for(var t=0,n=0;n<e;n++)t+=this.groupSizes[n];return t},e.prototype.insertRules=function(e,t){if(e>=this.groupSizes.length){for(var n=this.groupSizes,r=n.length,a=r;e>=a;)if((a<<=1)<0)throw qe(16,"".concat(e));this.groupSizes=new Uint32Array(a),this.groupSizes.set(n),this.length=a;for(var l=r;l<a;l++)this.groupSizes[l]=0}for(var o=this.indexOfGroup(e+1),i=(l=0,t.length);l<i;l++)this.tag.insertRule(o,t[l])&&(this.groupSizes[e]++,o++)},e.prototype.clearGroup=function(e){if(e<this.length){var t=this.groupSizes[e],n=this.indexOfGroup(e),r=n+t;this.groupSizes[e]=0;for(var a=n;a<r;a++)this.tag.deleteRule(n)}},e.prototype.getGroup=function(e){var t="";if(e>=this.length||0===this.groupSizes[e])return t;for(var n=this.groupSizes[e],r=this.indexOfGroup(e),a=r+n,l=r;l<a;l++)t+="".concat(this.tag.getRule(l)).concat(ue);return t},e}(),Ye=new Map,Ke=new Map,Xe=1,Ze=function(e){if(Ye.has(e))return Ye.get(e);for(;Ke.has(Xe);)Xe++;var t=Xe++;return Ye.set(e,t),Ke.set(t,e),t},Je=function(e,t){Xe=t+1,Ye.set(e,t),Ke.set(t,e)},et="style[".concat(le,"][").concat(ie,'="').concat(se,'"]'),tt=new RegExp("^".concat(le,'\\.g(\\d+)\\[id="([\\w\\d-]+)"\\].*?"([^"]*)')),nt=function(e,t,n){for(var r,a=n.split(","),l=0,o=a.length;l<o;l++)(r=a[l])&&e.registerName(t,r)},rt=function(e,t){for(var n,r=(null!==(n=t.textContent)&&void 0!==n?n:"").split(ue),a=[],l=0,o=r.length;l<o;l++){var i=r[l].trim();if(i){var s=i.match(tt);if(s){var u=0|parseInt(s[1],10),c=s[2];0!==u&&(Je(c,u),nt(e,c,s[3]),e.getTag().insertRules(u,a)),a.length=0}else a.push(i)}}},at=function(e){for(var t=document.querySelectorAll(et),n=0,r=t.length;n<r;n++){var a=t[n];a&&a.getAttribute(le)!==oe&&(rt(e,a),a.parentNode&&a.parentNode.removeChild(a))}};function lt(){return n.nc}var ot=function(e){var t=document.head,n=e||t,r=document.createElement("style"),a=function(e){var t=Array.from(e.querySelectorAll("style[".concat(le,"]")));return t[t.length-1]}(n),l=void 0!==a?a.nextSibling:null;r.setAttribute(le,oe),r.setAttribute(ie,se);var o=lt();return o&&r.setAttribute("nonce",o),n.insertBefore(r,l),r},it=function(){function e(e){this.element=ot(e),this.element.appendChild(document.createTextNode("")),this.sheet=function(e){if(e.sheet)return e.sheet;for(var t=document.styleSheets,n=0,r=t.length;n<r;n++){var a=t[n];if(a.ownerNode===e)return a}throw qe(17)}(this.element),this.length=0}return e.prototype.insertRule=function(e,t){try{return this.sheet.insertRule(t,e),this.length++,!0}catch(e){return!1}},e.prototype.deleteRule=function(e){this.sheet.deleteRule(e),this.length--},e.prototype.getRule=function(e){var t=this.sheet.cssRules[e];return t&&t.cssText?t.cssText:""},e}(),st=function(){function e(e){this.element=ot(e),this.nodes=this.element.childNodes,this.length=0}return e.prototype.insertRule=function(e,t){if(e<=this.length&&e>=0){var n=document.createTextNode(t);return this.element.insertBefore(n,this.nodes[e]||null),this.length++,!0}return!1},e.prototype.deleteRule=function(e){this.element.removeChild(this.nodes[e]),this.length--},e.prototype.getRule=function(e){return e<this.length?this.nodes[e].textContent:""},e}(),ut=function(){function e(e){this.rules=[],this.length=0}return e.prototype.insertRule=function(e,t){return e<=this.length&&(this.rules.splice(e,0,t),this.length++,!0)},e.prototype.deleteRule=function(e){this.rules.splice(e,1),this.length--},e.prototype.getRule=function(e){return e<this.length?this.rules[e]:""},e}(),ct=ce,dt={isServer:!ce,useCSSOMInjection:!de},ft=function(){function e(e,t,n){void 0===e&&(e=pe),void 0===t&&(t={});var r=this;this.options=a(a({},dt),e),this.gs=t,this.names=new Map(n),this.server=!!e.isServer,!this.server&&ce&&ct&&(ct=!1,at(this)),Qe(this,function(){return function(e){for(var t=e.getTag(),n=t.length,r="",a=function(n){var a=function(e){return Ke.get(e)}(n);if(void 0===a)return"continue";var l=e.names.get(a),o=t.getGroup(n);if(void 0===l||!l.size||0===o.length)return"continue";var i="".concat(le,".g").concat(n,'[id="').concat(a,'"]'),s="";void 0!==l&&l.forEach(function(e){e.length>0&&(s+="".concat(e,","))}),r+="".concat(o).concat(i,'{content:"').concat(s,'"}').concat(ue)},l=0;l<n;l++)a(l);return r}(r)})}return e.registerId=function(e){return Ze(e)},e.prototype.rehydrate=function(){!this.server&&ce&&at(this)},e.prototype.reconstructWithOptions=function(t,n){return void 0===n&&(n=!0),new e(a(a({},this.options),t),this.gs,n&&this.names||void 0)},e.prototype.allocateGSInstance=function(e){return this.gs[e]=(this.gs[e]||0)+1},e.prototype.getTag=function(){return this.tag||(this.tag=(e=function(e){var t=e.useCSSOMInjection,n=e.target;return e.isServer?new ut(n):t?new it(n):new st(n)}(this.options),new Ge(e)));var e},e.prototype.hasNameForId=function(e,t){return this.names.has(e)&&this.names.get(e).has(t)},e.prototype.registerName=function(e,t){if(Ze(e),this.names.has(e))this.names.get(e).add(t);else{var n=new Set;n.add(t),this.names.set(e,n)}},e.prototype.insertRules=function(e,t,n){this.registerName(e,t),this.getTag().insertRules(Ze(e),n)},e.prototype.clearNames=function(e){this.names.has(e)&&this.names.get(e).clear()},e.prototype.clearRules=function(e){this.getTag().clearGroup(Ze(e)),this.clearNames(e)},e.prototype.clearTag=function(){this.tag=void 0},e}(),pt=/&/g,ht=/^\s*\/\/.*$/gm;function mt(e,t){return e.map(function(e){return"rule"===e.type&&(e.value="".concat(t," ").concat(e.value),e.value=e.value.replaceAll(",",",".concat(t," ")),e.props=e.props.map(function(e){return"".concat(t," ").concat(e)})),Array.isArray(e.children)&&"@keyframes"!==e.type&&(e.children=mt(e.children,t)),e})}function gt(e){var t,n,r,a=void 0===e?pe:e,l=a.options,o=void 0===l?pe:l,i=a.plugins,s=void 0===i?fe:i,u=function(e,r,a){return a.startsWith(n)&&a.endsWith(n)&&a.replaceAll(n,"").length>0?".".concat(t):e},c=s.slice();c.push(function(e){e.type===f&&e.value.includes("&")&&(e.props[0]=e.props[0].replace(pt,n).replace(r,u))}),o.prefix&&c.push(Z),c.push(K);var d=function(e,a,l,i){void 0===a&&(a=""),void 0===l&&(l=""),void 0===i&&(i="&"),t=i,n=a,r=new RegExp("\\".concat(n,"\\b"),"g");var s=e.replace(ht,""),u=J(l||a?"".concat(l," ").concat(a," { ").concat(s," }"):s);o.namespace&&(u=mt(u,o.namespace));var d,f,p,h=[];return Y(u,(d=c.concat((p=function(e){return h.push(e)},function(e){e.root||(e=e.return)&&p(e)})),f=E(d),function(e,t,n,r){for(var a="",l=0;l<f;l++)a+=d[l](e,t,n,r)||"";return a})),h};return d.hash=s.length?s.reduce(function(e,t){return t.name||qe(15),Se(e,t.name)},5381).toString():"",d}var yt=new ft,vt=gt(),bt=r.createContext({shouldForwardProp:void 0,styleSheet:yt,stylis:vt}),wt=(bt.Consumer,r.createContext(void 0));function kt(){return(0,r.useContext)(bt)}function St(e){var t=(0,r.useState)(e.stylisPlugins),n=t[0],a=t[1],l=kt().styleSheet,o=(0,r.useMemo)(function(){var t=l;return e.sheet?t=e.sheet:e.target&&(t=t.reconstructWithOptions({target:e.target},!1)),e.disableCSSOMInjection&&(t=t.reconstructWithOptions({useCSSOMInjection:!1})),t},[e.disableCSSOMInjection,e.sheet,e.target,l]),s=(0,r.useMemo)(function(){return gt({options:{namespace:e.namespace,prefix:e.enableVendorPrefixes},plugins:n})},[e.enableVendorPrefixes,e.namespace,n]);(0,r.useEffect)(function(){i()(n,e.stylisPlugins)||a(e.stylisPlugins)},[e.stylisPlugins]);var u=(0,r.useMemo)(function(){return{shouldForwardProp:e.shouldForwardProp,styleSheet:o,stylis:s}},[e.shouldForwardProp,o,s]);return r.createElement(bt.Provider,{value:u},r.createElement(wt.Provider,{value:s},e.children))}var xt=function(){function e(e,t){var n=this;this.inject=function(e,t){void 0===t&&(t=vt);var r=n.name+t.hash;e.hasNameForId(n.id,r)||e.insertRules(n.id,r,t(n.rules,r,"@keyframes"))},this.name=e,this.id="sc-keyframes-".concat(e),this.rules=t,Qe(this,function(){throw qe(12,String(n.name))})}return e.prototype.getName=function(e){return void 0===e&&(e=vt),this.name+e.hash},e}(),Ct=function(e){return e>="A"&&e<="Z"};function Et(e){for(var t="",n=0;n<e.length;n++){var r=e[n];if(1===n&&"-"===r&&"-"===e[0])return e;Ct(r)?t+="-"+r.toLowerCase():t+=r}return t.startsWith("ms-")?"-"+t:t}var Pt=function(e){return null==e||!1===e||""===e},zt=function(e){var t,n,r=[];for(var a in e){var o=e[a];e.hasOwnProperty(a)&&!Pt(o)&&(Array.isArray(o)&&o.isCss||$e(o)?r.push("".concat(Et(a),":"),o,";"):Ve(o)?r.push.apply(r,l(l(["".concat(a," {")],zt(o),!1),["}"],!1)):r.push("".concat(Et(a),": ").concat((t=a,null==(n=o)||"boolean"==typeof n||""===n?"":"number"!=typeof n||0===n||t in ae||t.startsWith("--")?String(n).trim():"".concat(n,"px")),";")))}return r};function _t(e,t,n,r){return Pt(e)?[]:Ue(e)?[".".concat(e.styledComponentId)]:$e(e)?!$e(a=e)||a.prototype&&a.prototype.isReactComponent||!t?[e]:_t(e(t),t,n,r):e instanceof xt?n?(e.inject(n,r),[e.getName(r)]):[e]:Ve(e)?zt(e):Array.isArray(e)?Array.prototype.concat.apply(fe,e.map(function(e){return _t(e,t,n,r)})):[e.toString()];var a}function Nt(e){for(var t=0;t<e.length;t+=1){var n=e[t];if($e(n)&&!Ue(n))return!1}return!0}var Tt=xe(se),Lt=function(){function e(e,t,n){this.rules=e,this.staticRulesId="",this.isStatic=(void 0===n||n.isStatic)&&Nt(e),this.componentId=t,this.baseHash=Se(Tt,t),this.baseStyle=n,ft.registerId(t)}return e.prototype.generateAndInjectStyles=function(e,t,n){var r=this.baseStyle?this.baseStyle.generateAndInjectStyles(e,t,n):"";if(this.isStatic&&!n.hash)if(this.staticRulesId&&t.hasNameForId(this.componentId,this.staticRulesId))r=He(r,this.staticRulesId);else{var a=Be(_t(this.rules,e,t,n)),l=we(Se(this.baseHash,a)>>>0);if(!t.hasNameForId(this.componentId,l)){var o=n(a,".".concat(l),void 0,this.componentId);t.insertRules(this.componentId,l,o)}r=He(r,l),this.staticRulesId=l}else{for(var i=Se(this.baseHash,n.hash),s="",u=0;u<this.rules.length;u++){var c=this.rules[u];if("string"==typeof c)s+=c;else if(c){var d=Be(_t(c,e,t,n));i=Se(i,d+u),s+=d}}if(s){var f=we(i>>>0);t.hasNameForId(this.componentId,f)||t.insertRules(this.componentId,f,n(s,".".concat(f),void 0,this.componentId)),r=He(r,f)}}return r},e}(),Ot=r.createContext(void 0);Ot.Consumer;var jt={};function Rt(e,t,n){var l=Ue(e),o=e,i=!Ce(e),s=t.attrs,u=void 0===s?fe:s,c=t.componentId,d=void 0===c?function(e,t){var n="string"!=typeof e?"sc":ye(e);jt[n]=(jt[n]||0)+1;var r="".concat(n,"-").concat(function(e){return we(xe(e)>>>0)}(se+n+jt[n]));return t?"".concat(t,"-").concat(r):r}(t.displayName,t.parentComponentId):c,f=t.displayName,p=void 0===f?function(e){return Ce(e)?"styled.".concat(e):"Styled(".concat(function(e){return e.displayName||e.name||"Component"}(e),")")}(e):f,h=t.displayName&&t.componentId?"".concat(ye(t.displayName),"-").concat(t.componentId):t.componentId||d,m=l&&o.attrs?o.attrs.concat(u).filter(Boolean):u,g=t.shouldForwardProp;if(l&&o.shouldForwardProp){var y=o.shouldForwardProp;if(t.shouldForwardProp){var v=t.shouldForwardProp;g=function(e,t){return y(e,t)&&v(e,t)}}else g=y}var b=new Lt(n,h,l?o.componentStyle:void 0);function w(e,t){return function(e,t,n){var l=e.attrs,o=e.componentStyle,i=e.defaultProps,s=e.foldedComponentIds,u=e.styledComponentId,c=e.target,d=r.useContext(Ot),f=kt(),p=e.shouldForwardProp||f.shouldForwardProp,h=function(e,t,n){return void 0===n&&(n=pe),e.theme!==n.theme&&e.theme||t||n.theme}(t,d,i)||pe,m=function(e,t,n){for(var r,l=a(a({},t),{className:void 0,theme:n}),o=0;o<e.length;o+=1){var i=$e(r=e[o])?r(l):r;for(var s in i)l[s]="className"===s?He(l[s],i[s]):"style"===s?a(a({},l[s]),i[s]):i[s]}return t.className&&(l.className=He(l.className,t.className)),l}(l,t,h),g=m.as||c,y={};for(var v in m)void 0===m[v]||"$"===v[0]||"as"===v||"theme"===v&&m.theme===h||("forwardedAs"===v?y.as=m.forwardedAs:p&&!p(v,g)||(y[v]=m[v]));var b=function(e,t){var n=kt();return e.generateAndInjectStyles(t,n.styleSheet,n.stylis)}(o,m),w=He(s,u);return b&&(w+=" "+b),m.className&&(w+=" "+m.className),y[Ce(g)&&!he.has(g)?"class":"className"]=w,n&&(y.ref=n),(0,r.createElement)(g,y)}(k,e,t)}w.displayName=p;var k=r.forwardRef(w);return k.attrs=m,k.componentStyle=b,k.displayName=p,k.shouldForwardProp=g,k.foldedComponentIds=l?He(o.foldedComponentIds,o.styledComponentId):"",k.styledComponentId=h,k.target=l?o.target:e,Object.defineProperty(k,"defaultProps",{get:function(){return this._foldedDefaultProps},set:function(e){this._foldedDefaultProps=l?function(e){for(var t=[],n=1;n<arguments.length;n++)t[n-1]=arguments[n];for(var r=0,a=t;r<a.length;r++)We(e,a[r],!0);return e}({},o.defaultProps,e):e}}),Qe(k,function(){return".".concat(k.styledComponentId)}),i&&Me(k,e,{attrs:!0,componentStyle:!0,displayName:!0,foldedComponentIds:!0,shouldForwardProp:!0,styledComponentId:!0,target:!0}),k}function At(e,t){for(var n=[e[0]],r=0,a=t.length;r<a;r+=1)n.push(t[r],e[r+1]);return n}new Set;var Dt=function(e){return Object.assign(e,{isCss:!0})};function It(e){for(var t=[],n=1;n<arguments.length;n++)t[n-1]=arguments[n];if($e(e)||Ve(e))return Dt(_t(At(fe,l([e],t,!0))));var r=e;return 0===t.length&&1===r.length&&"string"==typeof r[0]?_t(r):Dt(_t(At(r,t)))}function Ft(e,t,n){if(void 0===n&&(n=pe),!t)throw qe(1,t);var r=function(r){for(var a=[],o=1;o<arguments.length;o++)a[o-1]=arguments[o];return e(t,n,It.apply(void 0,l([r],a,!1)))};return r.attrs=function(r){return Ft(e,t,a(a({},n),{attrs:Array.prototype.concat(n.attrs,r).filter(Boolean)}))},r.withConfig=function(r){return Ft(e,t,a(a({},n),r))},r}var Mt=function(e){return Ft(Rt,e)},$t=Mt;he.forEach(function(e){$t[e]=Mt(e)}),function(){function e(e,t){this.rules=e,this.componentId=t,this.isStatic=Nt(e),ft.registerId(this.componentId+1)}e.prototype.createStyles=function(e,t,n,r){var a=r(Be(_t(this.rules,t,n,r)),""),l=this.componentId+e;n.insertRules(l,l,a)},e.prototype.removeStyles=function(e,t){t.clearRules(this.componentId+e)},e.prototype.renderStyles=function(e,t,n,r){e>2&&ft.registerId(this.componentId+e),this.removeStyles(e,n),this.createStyles(e,t,n,r)}}(),function(){function e(){var e=this;this._emitSheetCSS=function(){var t=e.instance.toString();if(!t)return"";var n=lt(),r=Be([n&&'nonce="'.concat(n,'"'),"".concat(le,'="true"'),"".concat(ie,'="').concat(se,'"')].filter(Boolean)," ");return"<style ".concat(r,">").concat(t,"</style>")},this.getStyleTags=function(){if(e.sealed)throw qe(2);return e._emitSheetCSS()},this.getStyleElement=function(){var t;if(e.sealed)throw qe(2);var n=e.instance.toString();if(!n)return[];var l=((t={})[le]="",t[ie]=se,t.dangerouslySetInnerHTML={__html:n},t),o=lt();return o&&(l.nonce=o),[r.createElement("style",a({},l,{key:"sc-0-0"}))]},this.seal=function(){e.sealed=!0},this.instance=new ft({isServer:!0}),this.sealed=!1}e.prototype.collectStyles=function(e){if(this.sealed)throw qe(2);return r.createElement(St,{sheet:this.instance},e)},e.prototype.interleaveWithNodeStream=function(e){throw qe(3)}}(),"__sc-".concat(le,"__");class Ut{static generateId(){return Math.random().toString(36).substr(2,9)}static parseM3U(e,t,n){const r=e.split("\n").map(e=>e.trim()).filter(e=>e),a=[],l=new Set;let o=null;for(let e=0;e<r.length;e++){const t=r[e];t.startsWith("#EXTINF:")?o=this.parseExtinf(t):(t.startsWith("http://")||t.startsWith("https://"))&&o&&(o.url=t,o.id=this.generateId(),o.type=this.detectContentType(o),this.extractXtreamInfo(o),a.push(o),o.group&&l.add(o.group),o=null)}return{id:this.generateId(),name:t,filePath:n,entries:a,groups:Array.from(l),lastUpdated:new Date}}static parseExtinf(e){const t={},n=e.match(/#EXTINF:(-?\d+(?:\.\d+)?)/);t.duration=n?parseFloat(n[1]):-1;const r=this.extractAttributes(e);t.title=r["tvg-name"]||r.title||"Unknown",t.group=r["group-title"]||"Uncategorized",t.logo=r["tvg-logo"]||r.logo,t.metadata={year:r.year?parseInt(r.year):void 0,genre:r.genre,plot:r.plot,cast:r.cast,director:r.director,rating:r.rating?parseFloat(r.rating):void 0};const a=e.match(/,(.+)$/);return a&&(t.title=a[1].trim()),t}static extractAttributes(e){const t={},n=/(\w+(?:-\w+)*)="([^"]*)"/g;let r;for(;null!==(r=n.exec(e));)t[r[1]]=r[2];return t}static detectContentType(e){const t=e.url.toLowerCase(),n=e.title.toLowerCase(),r=e.group.toLowerCase();return this.isSeriesContent(t,n,r)?"series":this.isMovieContent(t,n,r)?"movie":this.isLiveContent(t,n,r)?"live":"unknown"}static isSeriesContent(e,t,n){const r=[/\bs\d{1,2}e\d{1,3}\b/i,/\bs\d{1,2}\s+e\d{1,3}\b/i,/season\s*\d+.*episode\s*\d+/i,/\d+x\d+/,/temporada\s*\d+/i,/episodio\s*\d+/i,/cap[ií]tulo\s*\d+/i].some(e=>e.test(t)),a=[/series/i,/anime/i,/telenovela/i,/show/i,/temporadas/i].some(e=>e.test(n)),l=[/\/series\//i,/\/show\//i].some(t=>t.test(e));return r||a||l}static isMovieContent(e,t,n){return[/movies?/i,/films?/i,/cinema/i,/peliculas?/i,/\b\d{4}\b/].some(n=>n.test(e)||n.test(t))||[/movies?/i,/films?/i,/cinema/i,/peliculas?/i].some(e=>e.test(n))}static isLiveContent(e,t,n){return[/live/i,/tv/i,/channel/i,/canal/i,/directo/i,/en.*vivo/i].some(r=>r.test(e)||r.test(t)||r.test(n))}static extractXtreamInfo(e){const t=e.url,n=t.match(/\/([^\/]+)\/([^\/]+)\/(\d+)/);if(n){const[,r,a,l]=n;"series"===e.type&&this.extractSeriesInfo(e,t)}}static extractSeriesInfo(e,t){let n=1,r=1;const a=e.title.match(/[Ss](\d{1,2})[Ee](\d{1,3})/);if(a)n=parseInt(a[1]),r=parseInt(a[2]);else{const t=e.title.match(/[Ss](\d{1,2})\s+[Ee](\d{1,3})/);if(t)n=parseInt(t[1]),r=parseInt(t[2]);else{const t=e.title.match(/(\d+)x(\d+)/);t&&(n=parseInt(t[1]),r=parseInt(t[2]))}}e.metadata={...e.metadata,season:n,episode:r};const l=t.match(/\/(\d+)\.mkv?$/);l&&(e.metadata={...e.metadata,seriesId:l[1]})}static groupSeriesByShow(e){const t=new Map;return e.filter(e=>"series"===e.type&&e.metadata?.seriesId).forEach(e=>{const n=e.metadata.seriesId,r=e.metadata.season||1,a=e.metadata.episode||1;t.has(n)||t.set(n,{id:n,title:this.extractSeriesTitle(e.title),seasons:[],totalEpisodes:0,logo:e.logo,plot:e.metadata?.plot,year:e.metadata?.year,genre:e.metadata?.genre});const l=t.get(n);let o=l.seasons.find(e=>e.number===r);o||(o={number:r,episodes:[]},l.seasons.push(o)),o.episodes.push({number:a,title:e.title,url:e.url,duration:e.duration,plot:e.metadata?.plot}),l.totalEpisodes++}),t.forEach(e=>{e.seasons.sort((e,t)=>e.number-t.number),e.seasons.forEach(e=>{e.episodes.sort((e,t)=>e.number-t.number)})}),Array.from(t.values())}static extractSeriesTitle(e){let t=e;return t=t.replace(/\s+[Ss]\d{1,2}\s*[Ee]\d{1,3}.*$/,"").replace(/\s+\d+x\d+.*$/,"").replace(/\s+Season\s*\d+.*$/i,"").replace(/\s+Episode\s*\d+.*$/i,"").replace(/\s+Temporada\s*\d+.*$/i,"").replace(/\s+Episodio\s*\d+.*$/i,"").replace(/\s+Cap[ií]tulo\s*\d+.*$/i,"").trim(),t}}const Ht=$t.div`
  height: 100%;
  background: #2d2d2d;
  border-right: 1px solid #404040;
  display: flex;
  flex-direction: column;
`,Bt=$t.div`
  padding: 15px;
  background: #3d3d3d;
  border-bottom: 1px solid #404040;
`,Vt=$t.h3`
  margin: 0;
  color: #ffffff;
  font-size: 16px;
`,Wt=$t.div`
  flex: 1;
  padding: 15px;
  overflow-y: auto;
`,Qt=$t.button`
  background: #0078d4;
  color: white;
  border: none;
  padding: 10px 15px;
  border-radius: 4px;
  cursor: pointer;
  margin: 5px 0;
  
  &:hover {
    background: #106ebe;
  }
`,qt=$t.input`
  margin: 10px 0;
  color: white;
`,Gt=$t.div`
  background: #404040;
  padding: 10px;
  margin: 5px 0;
  border-radius: 4px;
  cursor: pointer;

  &:hover {
    background: #505050;
  }
`,Yt=$t.div`
  max-height: 300px;
  overflow-y: auto;
  margin-top: 10px;
  border: 1px solid #555;
  border-radius: 4px;
`,Kt=$t.div`
  padding: 8px 12px;
  border-bottom: 1px solid #555;
  cursor: pointer;

  &:hover {
    background: #505050;
  }

  &:last-child {
    border-bottom: none;
  }
`,Xt=$t.div`
  color: white;
  font-size: 13px;
  font-weight: bold;
  margin-bottom: 4px;
`,Zt=$t.div`
  color: #ccc;
  font-size: 11px;
  display: flex;
  justify-content: space-between;
`,Jt=$t.span`
  background: ${e=>{switch(e.type){case"movie":return"#e74c3c";case"series":return"#3498db";case"live":return"#2ecc71";default:return"#95a5a6"}}};
  color: white;
  padding: 2px 6px;
  border-radius: 10px;
  font-size: 10px;
  font-weight: bold;
  margin-left: 8px;
`,en=({playlists:t,currentPlaylist:n,onPlaylistUpdate:a,onDownloadRequest:l})=>{const[o,i]=(0,r.useState)(null),[s,u]=(0,r.useState)(""),c=async()=>{if(s.trim())try{console.log("🌐 Cargando M3U desde URL:",s);const e=await fetch(s);if(!e.ok)throw new Error(`HTTP ${e.status}: ${e.statusText}`);const t=await e.text(),n=s.split("/").pop()||"playlist.m3u",r=Ut.parseM3U(t,n,s);console.log("📺 Playlist desde URL cargada:",r.name,"con",r.entries.length,"entradas"),a(r),u("")}catch(e){console.error("❌ Error cargando desde URL:",e),alert(`Error cargando desde URL: ${e}`)}else alert("Por favor ingresa una URL válida")},d=()=>{const e=t.find(e=>e.id===n);e&&l(e.entries)};return(0,e.jsxs)(Ht,{children:[(0,e.jsx)(Bt,{children:(0,e.jsx)(Vt,{children:"📺 Panel M3U"})}),(0,e.jsxs)(Wt,{children:[(0,e.jsxs)("div",{children:[(0,e.jsx)("h4",{style:{color:"white",marginBottom:"10px"},children:"Cargar Playlist M3U"}),(0,e.jsx)(qt,{type:"file",accept:".m3u,.m3u8",onChange:e=>{const t=e.target.files?.[0];t&&i(t)}}),(0,e.jsx)(Qt,{onClick:async()=>{if(o)try{const t=await(e=o,new Promise((t,n)=>{const r=new FileReader;r.onload=e=>{const n=e.target?.result;t(n)},r.onerror=()=>n(new Error("Error leyendo archivo")),r.readAsText(e,"utf-8")})),n=Ut.parseM3U(t,o.name);console.log("📺 Playlist cargada:",n.name,"con",n.entries.length,"entradas"),a(n),i(null)}catch(e){console.error("❌ Error cargando playlist:",e),alert(`Error cargando playlist: ${e}`)}var e},disabled:!o,children:"📂 Cargar Playlist"}),(0,e.jsxs)("div",{style:{marginTop:"15px",padding:"10px",background:"#353535",borderRadius:"4px"},children:[(0,e.jsx)("div",{style:{color:"white",fontWeight:"bold",marginBottom:"8px"},children:"🌐 Cargar desde URL"}),(0,e.jsx)("input",{type:"text",value:s,onChange:e=>u(e.target.value),placeholder:"https://ejemplo.com/playlist.m3u",style:{width:"100%",padding:"8px",background:"#2d2d2d",border:"1px solid #555",borderRadius:"4px",color:"white",fontSize:"12px"},onKeyPress:e=>"Enter"===e.key&&c()}),(0,e.jsx)(Qt,{onClick:c,style:{marginTop:"8px",width:"100%"},disabled:!s.trim(),children:"🔗 Cargar desde URL"})]})]}),(0,e.jsxs)("div",{style:{marginTop:"20px"},children:[(0,e.jsxs)("h4",{style:{color:"white",marginBottom:"10px"},children:["Playlists Cargadas (",t.length,")"]}),t.map(t=>(0,e.jsxs)(Gt,{children:[(0,e.jsx)("div",{style:{fontWeight:"bold",color:"white"},children:t.name}),(0,e.jsxs)("div",{style:{fontSize:"12px",color:"#ccc",marginTop:"5px"},children:[t.entries.length," elementos • ",t.groups.length," categorías"]}),n===t.id&&(0,e.jsxs)(e.Fragment,{children:[(0,e.jsxs)(Qt,{onClick:d,style:{marginTop:"10px"},children:["📥 Descargar Todo (",t.entries.length,")"]}),(0,e.jsxs)(Yt,{children:[t.entries.slice(0,50).map(t=>(0,e.jsxs)(Kt,{onClick:()=>l([t]),children:[(0,e.jsxs)(Xt,{children:[t.title,(0,e.jsx)(Jt,{type:t.type,children:"movie"===t.type?"PEL":"series"===t.type?"SER":"live"===t.type?"TV":"?"})]}),(0,e.jsxs)(Zt,{children:[(0,e.jsxs)("span",{children:["📁 ",t.group]}),(0,e.jsxs)("span",{children:["⏱️ ",Math.floor(t.duration/60),"min"]})]})]},t.id)),t.entries.length>50&&(0,e.jsxs)("div",{style:{padding:"10px",textAlign:"center",color:"#888",fontStyle:"italic"},children:["... y ",t.entries.length-50," elementos más"]})]})]})]},t.id))]}),0===t.length&&(0,e.jsxs)("div",{style:{color:"#888",textAlign:"center",marginTop:"50px",fontStyle:"italic"},children:["No hay playlists cargadas.",(0,e.jsx)("br",{}),"Selecciona un archivo M3U para comenzar."]})]})]})};class tn{constructor(){this.connections=new Map,this.mockFiles=[{name:"Downloads",path:"/home/<USER>/Downloads",type:"directory",size:0,modified:new Date,permissions:"drwxr-xr-x"},{name:"Movies",path:"/home/<USER>/Movies",type:"directory",size:0,modified:new Date,permissions:"drwxr-xr-x"},{name:"Series",path:"/home/<USER>/Series",type:"directory",size:0,modified:new Date,permissions:"drwxr-xr-x"},{name:"example.mp4",path:"/home/<USER>/example.mp4",type:"file",size:524288e3,modified:new Date,permissions:"-rw-r--r--"}]}async connect(e){try{if(console.log("🔐 Conectando a SSH:",e.host),await new Promise(e=>setTimeout(e,1e3)),!e.host||!e.username)throw new Error("Host y usuario son requeridos");const t={...e,isConnected:!0,currentPath:`/home/<USER>"✅ Conectado exitosamente a:",e.host),!0}catch(e){throw console.error("❌ Error conectando SSH:",e),e}}async disconnect(e){const t=this.connections.get(e);t&&(t.isConnected=!1,console.log("🔌 Desconectado de:",t.host))}async listFiles(e,t){const n=this.connections.get(e);if(!n||!n.isConnected)throw new Error("No hay conexión SSH activa");console.log("📁 Listando archivos en:",t),await new Promise(e=>setTimeout(e,300));const r=[];if(t===`/home/<USER>"/"===t?r.push(...this.mockFiles):t.includes("Downloads")?r.push({name:"movie1.mp4",path:t+"/movie1.mp4",type:"file",size:1258291200,modified:new Date(Date.now()-864e5),permissions:"-rw-r--r--"},{name:"series_s01e01.mp4",path:t+"/series_s01e01.mp4",type:"file",size:838860800,modified:new Date(Date.now()-1728e5),permissions:"-rw-r--r--"}):t.includes("Movies")&&r.push({name:"Action",path:t+"/Action",type:"directory",size:0,modified:new Date,permissions:"drwxr-xr-x"},{name:"Comedy",path:t+"/Comedy",type:"directory",size:0,modified:new Date,permissions:"drwxr-xr-x"}),t!==`/home/<USER>"/"!==t){const e=t.split("/").slice(0,-1).join("/")||"/";r.unshift({name:"..",path:e,type:"directory",size:0,modified:new Date,permissions:"drwxr-xr-x"})}return{path:t,files:r,parent:t!==`/home/<USER>"/").slice(0,-1).join("/"):void 0}}async createDirectory(e,t){const n=this.connections.get(e);if(!n||!n.isConnected)throw new Error("No hay conexión SSH activa");return console.log("📁 Creando directorio:",t),await new Promise(e=>setTimeout(e,200)),console.log("✅ Directorio creado:",t),!0}async deleteFile(e,t){const n=this.connections.get(e);if(!n||!n.isConnected)throw new Error("No hay conexión SSH activa");return console.log("🗑️ Eliminando:",t),await new Promise(e=>setTimeout(e,300)),console.log("✅ Eliminado:",t),!0}getConnection(e){return this.connections.get(e)}isConnected(e){const t=this.connections.get(e);return t?.isConnected||!1}async executeCommand(e,t){const n=this.connections.get(e);if(!n||!n.isConnected)throw new Error("No hay conexión SSH activa");return console.log("⚡ Ejecutando comando:",t),await new Promise(e=>setTimeout(e,500)),t.includes("wget")?"wget: comando iniciado en segundo plano":t.includes("ls")?"archivo1.mp4\narchivo2.mp4\ndirectorio1/":t.includes("pwd")?n.currentPath:`Comando ejecutado: ${t}`}async getDiskSpace(e,t){const n=this.connections.get(e);if(!n||!n.isConnected)throw new Error("No hay conexión SSH activa");return{total:536870912e3,free:214748364800,used:322122547200}}}const nn=$t.div`
  height: 100%;
  background: #2d2d2d;
  border-right: 1px solid #404040;
  display: flex;
  flex-direction: column;
`,rn=$t.div`
  padding: 15px;
  background: #3d3d3d;
  border-bottom: 1px solid #404040;
`,an=$t.h3`
  margin: 0;
  color: #ffffff;
  font-size: 16px;
`,ln=$t.div`
  flex: 1;
  padding: 15px;
  overflow-y: auto;
`,on=$t.div`
  display: flex;
  flex-direction: column;
  gap: 10px;
  margin-bottom: 20px;
`,sn=$t.input`
  padding: 8px;
  border: 1px solid #555;
  border-radius: 4px;
  background: #404040;
  color: white;
  
  &::placeholder {
    color: #aaa;
  }
`,un=$t.button`
  background: #0078d4;
  color: white;
  border: none;
  padding: 10px 15px;
  border-radius: 4px;
  cursor: pointer;
  
  &:hover {
    background: #106ebe;
  }
  
  &:disabled {
    background: #555;
    cursor: not-allowed;
  }
`,cn=$t.div`
  background: #404040;
  padding: 10px;
  margin: 5px 0;
  border-radius: 4px;
  cursor: pointer;

  &:hover {
    background: #505050;
  }
`,dn=$t.div`
  margin-top: 15px;
  border: 1px solid #555;
  border-radius: 4px;
  background: #2d2d2d;
`,fn=$t.div`
  background: #404040;
  padding: 8px 12px;
  border-bottom: 1px solid #555;
  color: #ccc;
  font-size: 12px;
  font-family: monospace;
`,pn=$t.div`
  max-height: 300px;
  overflow-y: auto;
`,hn=$t.div`
  padding: 8px 12px;
  border-bottom: 1px solid #333;
  cursor: pointer;
  display: flex;
  align-items: center;

  &:hover {
    background: #404040;
  }

  &:last-child {
    border-bottom: none;
  }
`,mn=$t.span`
  margin-right: 8px;
  width: 16px;
  text-align: center;
`,gn=$t.span`
  flex: 1;
  color: white;
  font-size: 13px;
`,yn=$t.span`
  color: #888;
  font-size: 11px;
  margin-left: 8px;
`,vn=$t.div`
  background: #353535;
  padding: 6px 12px;
  border-top: 1px solid #555;
  color: #ccc;
  font-size: 11px;
  display: flex;
  justify-content: space-between;
`,bn=$t.span`
  display: inline-block;
  width: 8px;
  height: 8px;
  border-radius: 50%;
  background: ${e=>e.connected?"#00ff00":"#ff0000"};
  margin-right: 8px;
`,wn=($t.div`
  padding: 5px 10px;
  cursor: pointer;
  color: #ccc;
  
  &:hover {
    background: #404040;
    color: white;
  }
`,({connections:t,currentConnection:n,selectedDestination:a,onConnectionUpdate:l,onDestinationSelect:o})=>{const[i,s]=(0,r.useState)({name:"",host:"",port:"22",username:"",password:""}),[u,c]=(0,r.useState)(null),[d,f]=(0,r.useState)(!1),[p]=(0,r.useState)(()=>new tn),h=(e,t)=>{s(n=>({...n,[e]:t}))},m=async(e,t)=>{try{const n=await p.listFiles(e,t);c(n)}catch(e){console.error("❌ Error cargando directorio:",e),alert(`Error cargando directorio: ${e}`)}},g=t.find(e=>e.id===n),y=e=>{if(0===e)return"-";const t=Math.floor(Math.log(e)/Math.log(1024));return parseFloat((e/Math.pow(1024,t)).toFixed(1))+" "+["B","KB","MB","GB"][t]},v=e=>{if(".."===e.name)return"⬆️";if("directory"===e.type)return"📁";const t=e.name.split(".").pop()?.toLowerCase();switch(t){case"mp4":case"avi":case"mkv":case"mov":return"🎬";case"mp3":case"wav":case"flac":return"🎵";case"jpg":case"png":case"gif":return"🖼️";default:return"📄"}};return(0,e.jsxs)(nn,{children:[(0,e.jsx)(rn,{children:(0,e.jsx)(an,{children:"🔐 Panel SSH/SFTP"})}),(0,e.jsxs)(ln,{children:[(0,e.jsxs)("div",{children:[(0,e.jsx)("h4",{style:{color:"white",marginBottom:"10px"},children:"Nueva Conexión SSH"}),(0,e.jsxs)(on,{children:[(0,e.jsx)(sn,{type:"text",placeholder:"Nombre de la conexión",value:i.name,onChange:e=>h("name",e.target.value)}),(0,e.jsx)(sn,{type:"text",placeholder:"Host/IP",value:i.host,onChange:e=>h("host",e.target.value)}),(0,e.jsx)(sn,{type:"number",placeholder:"Puerto",value:i.port,onChange:e=>h("port",e.target.value)}),(0,e.jsx)(sn,{type:"text",placeholder:"Usuario",value:i.username,onChange:e=>h("username",e.target.value)}),(0,e.jsx)(sn,{type:"password",placeholder:"Contraseña",value:i.password,onChange:e=>h("password",e.target.value)}),(0,e.jsx)(un,{onClick:async()=>{if(i.name&&i.host&&i.username){f(!0);try{const e={id:Date.now().toString(),name:i.name||`${i.username}@${i.host}`,host:i.host,port:parseInt(i.port),username:i.username,password:i.password,isConnected:!1,currentPath:`/home/<USER>"",host:"",port:"22",username:"",password:""}))}catch(e){console.error("❌ Error conectando:",e),alert(`Error de conexión: ${e}`)}finally{f(!1)}}else alert("Por favor completa todos los campos requeridos")},disabled:!i.host||!i.username||d,children:d?"🔄 Conectando...":"🔗 Conectar"})]})]}),(0,e.jsxs)("div",{children:[(0,e.jsxs)("h4",{style:{color:"white",marginBottom:"10px"},children:["Conexiones (",t.length,")"]}),t.map(t=>(0,e.jsxs)(cn,{children:[(0,e.jsxs)("div",{style:{color:"white",fontWeight:"bold"},children:[(0,e.jsx)(bn,{connected:t.isConnected}),t.name]}),(0,e.jsxs)("div",{style:{fontSize:"12px",color:"#ccc",marginTop:"5px"},children:[t.username,"@",t.host,":",t.port]})]},t.id))]}),g&&g.isConnected&&(0,e.jsxs)("div",{style:{marginTop:"20px"},children:[(0,e.jsxs)("div",{style:{display:"flex",justifyContent:"space-between",alignItems:"center",marginBottom:"10px"},children:[(0,e.jsx)("h4",{style:{color:"white",margin:0},children:"📁 Explorador de Archivos"}),(0,e.jsx)(un,{onClick:async()=>{if(n){await p.disconnect(n),c(null);const e=t.find(e=>e.id===n);e&&l({...e,isConnected:!1})}},style:{fontSize:"11px",padding:"4px 8px"},children:"🔌 Desconectar"})]}),(0,e.jsx)(dn,{children:u&&(0,e.jsxs)(e.Fragment,{children:[(0,e.jsxs)(fn,{children:["📍 ",u.path]}),(0,e.jsx)(pn,{children:u.files.map((t,r)=>(0,e.jsxs)(hn,{onClick:()=>(async e=>{n&&("directory"===e.type?await m(n,e.path):o(e.path))})(t),style:{background:a===t.path?"#0078d4":"transparent"},children:[(0,e.jsx)(mn,{children:v(t)}),(0,e.jsx)(gn,{children:t.name}),(0,e.jsx)(yn,{children:y(t.size)})]},`${t.path}-${r}`))}),(0,e.jsxs)(vn,{children:[(0,e.jsxs)("span",{children:[u.files.length," elementos"]}),(0,e.jsxs)("span",{children:["Conectado a ",g.name]})]})]})}),a&&(0,e.jsxs)("div",{style:{marginTop:"10px",padding:"10px",background:"#0078d4",borderRadius:"4px",color:"white",fontSize:"12px"},children:["✅ Destino seleccionado: ",a]})]}),0===t.length&&(0,e.jsxs)("div",{style:{color:"#888",textAlign:"center",marginTop:"50px",fontStyle:"italic"},children:["No hay conexiones SSH configuradas.",(0,e.jsx)("br",{}),"Crea una nueva conexión para comenzar."]})]})]})}),kn=$t.div`
  height: 100%;
  background: #2d2d2d;
  display: flex;
  flex-direction: column;
`,Sn=$t.div`
  padding: 15px;
  background: #3d3d3d;
  border-bottom: 1px solid #404040;
  display: flex;
  justify-content: space-between;
  align-items: center;
`,xn=$t.h3`
  margin: 0;
  color: #ffffff;
  font-size: 16px;
`,Cn=$t.div`
  display: flex;
  gap: 10px;
`,En=$t.button`
  background: #0078d4;
  color: white;
  border: none;
  padding: 8px 12px;
  border-radius: 4px;
  cursor: pointer;
  font-size: 12px;
  
  &:hover {
    background: #106ebe;
  }
  
  &:disabled {
    background: #555;
    cursor: not-allowed;
  }
`,Pn=$t.div`
  flex: 1;
  padding: 15px;
  overflow-y: auto;
`,zn=$t.div`
  display: grid;
  grid-template-columns: repeat(auto-fit, minmax(120px, 1fr));
  gap: 10px;
  margin-bottom: 20px;
`,_n=$t.div`
  background: #404040;
  padding: 15px;
  border-radius: 8px;
  text-align: center;
`,Nn=$t.div`
  font-size: 24px;
  font-weight: bold;
  color: #0078d4;
  margin-bottom: 5px;
`,Tn=$t.div`
  font-size: 12px;
  color: #ccc;
`,Ln=$t.div`
  background: #404040;
  padding: 15px;
  margin: 10px 0;
  border-radius: 8px;
  border-left: 4px solid #0078d4;
`,On=$t.div`
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 10px;
`,jn=$t.div`
  color: white;
  font-weight: bold;
  font-size: 14px;
`,Rn=$t.span`
  padding: 4px 8px;
  border-radius: 12px;
  font-size: 11px;
  font-weight: bold;
  background: ${e=>{switch(e.status){case"completed":return"#28a745";case"downloading":return"#007bff";case"paused":return"#ffc107";case"failed":return"#dc3545";default:return"#6c757d"}}};
  color: white;
`,An=$t.div`
  width: 100%;
  height: 6px;
  background: #555;
  border-radius: 3px;
  overflow: hidden;
  margin: 10px 0;
`,Dn=$t.div`
  height: 100%;
  background: linear-gradient(90deg, #0078d4, #00bcf2);
  width: ${e=>e.progress}%;
  transition: width 0.3s ease;
`,In=$t.div`
  display: flex;
  justify-content: space-between;
  font-size: 12px;
  color: #ccc;
`,Fn=({downloadQueue:t,onStartQueue:n,onStopQueue:r,onPauseTask:a,onResumeTask:l,onRemoveTask:o,onClearCompleted:i})=>{const s=[{id:"1",fileName:"Película Ejemplo.mp4",status:"completed",progress:100,speed:0,size:"2.1 GB",timeRemaining:"0s"},{id:"2",fileName:"Serie S01E01.mp4",status:"downloading",progress:65,speed:5.2,size:"850 MB",timeRemaining:"2m 15s"},{id:"3",fileName:"Documental.mp4",status:"paused",progress:30,speed:0,size:"1.5 GB",timeRemaining:"∞"}],u={total:s.length,completed:s.filter(e=>"completed"===e.status).length,downloading:s.filter(e=>"downloading"===e.status).length,failed:0},c=e=>{switch(e){case"completed":return"Completado";case"downloading":return"Descargando";case"paused":return"Pausado";case"failed":return"Error";default:return"Pendiente"}};return(0,e.jsxs)(kn,{children:[(0,e.jsxs)(Sn,{children:[(0,e.jsx)(xn,{children:"📥 Panel de Descargas"}),(0,e.jsxs)(Cn,{children:[(0,e.jsx)(En,{onClick:n,disabled:t.isRunning,children:"▶️ Iniciar"}),(0,e.jsx)(En,{onClick:r,disabled:!t.isRunning,children:"⏹️ Detener"}),(0,e.jsx)(En,{onClick:i,children:"🧹 Limpiar"})]})]}),(0,e.jsxs)(Pn,{children:[(0,e.jsxs)(zn,{children:[(0,e.jsxs)(_n,{children:[(0,e.jsx)(Nn,{children:u.total}),(0,e.jsx)(Tn,{children:"Total"})]}),(0,e.jsxs)(_n,{children:[(0,e.jsx)(Nn,{children:u.downloading}),(0,e.jsx)(Tn,{children:"Descargando"})]}),(0,e.jsxs)(_n,{children:[(0,e.jsx)(Nn,{children:u.completed}),(0,e.jsx)(Tn,{children:"Completadas"})]}),(0,e.jsxs)(_n,{children:[(0,e.jsx)(Nn,{children:u.failed}),(0,e.jsx)(Tn,{children:"Fallidas"})]})]}),(0,e.jsxs)("div",{children:[(0,e.jsxs)("h4",{style:{color:"white",marginBottom:"15px"},children:["Cola de Descargas (",s.length,")"]}),s.map(t=>{return(0,e.jsxs)(Ln,{children:[(0,e.jsxs)(On,{children:[(0,e.jsx)(jn,{children:t.fileName}),(0,e.jsx)(Rn,{status:t.status,children:c(t.status)})]}),(0,e.jsx)(An,{children:(0,e.jsx)(Dn,{progress:t.progress})}),(0,e.jsxs)(In,{children:[(0,e.jsxs)("span",{children:[t.progress,"% • ",t.size]}),(0,e.jsxs)("span",{children:[(n=t.speed,0===n?"0 MB/s":`${n.toFixed(1)} MB/s`)," • ",t.timeRemaining]})]}),(0,e.jsxs)("div",{style:{marginTop:"10px",display:"flex",gap:"5px"},children:["downloading"===t.status&&(0,e.jsx)(En,{onClick:()=>a(t.id),children:"⏸️"}),"paused"===t.status&&(0,e.jsx)(En,{onClick:()=>l(t.id),children:"▶️"}),(0,e.jsx)(En,{onClick:()=>o(t.id),children:"🗑️"})]})]},t.id);var n}),0===s.length&&(0,e.jsxs)("div",{style:{color:"#888",textAlign:"center",marginTop:"50px",fontStyle:"italic"},children:["No hay descargas en la cola.",(0,e.jsx)("br",{}),"Selecciona contenido desde el panel M3U para comenzar."]})]})]})]})},Mn=$t.div`
  width: 100vw;
  height: 100vh;
  background: #1a1a1a;
  color: #ffffff;
  font-family: 'Segoe UI', Tahoma, Geneva, Verdana, sans-serif;
  display: flex;
  flex-direction: column;
`,$n=($t.div`
  height: 60px;
  background: #2d2d2d;
  border-bottom: 1px solid #404040;
  display: flex;
  align-items: center;
  padding: 0 20px;
  box-shadow: 0 2px 4px rgba(0, 0, 0, 0.3);
`,$t.h1`
  font-size: 18px;
  font-weight: 600;
  color: #ffffff;
  margin: 0;
`,$t.div`
  flex: 1;
  display: flex;
  flex-direction: column;
`,$t.div`
  flex: 1;
  display: flex;
`,()=>{const[t,n]=(0,r.useState)({m3uPlaylists:[],sshConnections:[],downloadQueue:{tasks:[],isRunning:!1,totalTasks:0,completedTasks:0,failedTasks:0}});return console.log("🎨 Rendering App JSX..."),(0,e.jsx)(Mn,{children:(0,e.jsxs)("div",{style:{display:"flex",height:"100vh"},children:[(0,e.jsx)("div",{style:{width:"33%",minWidth:"300px"},children:(0,e.jsx)(en,{playlists:t.m3uPlaylists,currentPlaylist:t.currentPlaylist,onPlaylistUpdate:e=>{console.log("📺 Playlist actualizada:",e.name),n(t=>({...t,m3uPlaylists:[...t.m3uPlaylists.filter(t=>t.id!==e.id),e],currentPlaylist:e.id}))},onDownloadRequest:(e,t)=>{console.log("📥 Solicitud de descarga:",e.length,"elementos"),alert(`Funcionalidad de descarga temporalmente deshabilitada.\nElementos: ${e.length}\nSerie: ${t||"N/A"}`)}})}),(0,e.jsx)("div",{style:{width:"33%",minWidth:"300px"},children:(0,e.jsx)(wn,{connections:t.sshConnections,currentConnection:t.currentConnection,selectedDestination:t.selectedDestination,onConnectionUpdate:e=>{console.log("🔐 Conexión SSH actualizada:",e.name),n(t=>({...t,sshConnections:[...t.sshConnections.filter(t=>t.id!==e.id),e],currentConnection:e.id}))},onDestinationSelect:e=>{console.log("📁 Destino seleccionado:",e),n(t=>({...t,selectedDestination:e}))}})}),(0,e.jsx)("div",{style:{width:"34%",minWidth:"300px"},children:(0,e.jsx)(Fn,{downloadQueue:t.downloadQueue,onStartQueue:()=>console.log("▶️ Iniciar cola"),onStopQueue:()=>console.log("⏹️ Detener cola"),onPauseTask:e=>console.log("⏸️ Pausar tarea:",e),onResumeTask:e=>console.log("▶️ Reanudar tarea:",e),onRemoveTask:e=>console.log("🗑️ Eliminar tarea:",e),onClearCompleted:()=>console.log("🧹 Limpiar completadas")})})]})})}),Un=document.getElementById("root");if(!Un)throw new Error("Root element not found");(0,t.createRoot)(Un).render((0,e.jsx)($n,{}))})()})();
//# sourceMappingURL=bundle.js.map