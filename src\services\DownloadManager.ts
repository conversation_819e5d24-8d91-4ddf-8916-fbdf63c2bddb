import { DownloadTask, DownloadQueue, M3UEntry } from '../types';
import { SSHService } from './SSHService';

export class DownloadManager {
  private queue: DownloadTask[] = [];
  private isRunning: boolean = false;
  private currentTask: DownloadTask | null = null;
  private sshService: SSHService | null = null;
  private listeners: ((queue: DownloadQueue) => void)[] = [];

  constructor() {
    this.loadQueueFromStorage();
  }

  setSSHService(sshService: SSHService): void {
    this.sshService = sshService;
  }

  addListener(callback: (queue: DownloadQueue) => void): void {
    this.listeners.push(callback);
  }

  removeListener(callback: (queue: DownloadQueue) => void): void {
    const index = this.listeners.indexOf(callback);
    if (index > -1) {
      this.listeners.splice(index, 1);
    }
  }

  private notifyListeners(): void {
    const queueState = this.getQueueState();
    this.listeners.forEach(listener => listener(queueState));
  }

  addDownload(entry: M3UEntry, remotePath: string, fileName?: string): string {
    const task: DownloadTask = {
      id: this.generateId(),
      name: entry.title,
      url: entry.url,
      remotePath,
      fileName: fileName || this.extractFileName(entry.url, entry.title),
      progress: 0,
      status: 'pending',
    };

    this.queue.push(task);
    this.saveQueueToStorage();
    this.notifyListeners();

    return task.id;
  }

  addBulkDownload(entries: M3UEntry[], remotePath: string): string[] {
    const taskIds: string[] = [];

    entries.forEach(entry => {
      const fileName = this.extractFileName(entry.url, entry.title);
      const taskId = this.addDownload(entry, remotePath, fileName);
      taskIds.push(taskId);
    });

    return taskIds;
  }

  addSeriesDownload(entries: M3UEntry[], remotePath: string, seriesTitle: string): string[] {
    const taskIds: string[] = [];

    // Crear carpeta para la serie
    const seriesPath = `${remotePath}/${this.sanitizeFileName(seriesTitle)}`;

    entries.forEach(entry => {
      let fileName = this.extractFileName(entry.url, entry.title);
      
      // Mejorar el nombre del archivo para episodios
      if (entry.metadata?.season && entry.metadata?.episode) {
        const season = entry.metadata.season.toString().padStart(2, '0');
        const episode = entry.metadata.episode.toString().padStart(2, '0');
        const extension = fileName.split('.').pop() || 'mkv';
        fileName = `S${season}E${episode} - ${entry.title}.${extension}`;
      }

      const task: DownloadTask = {
        id: this.generateId(),
        name: entry.title,
        url: entry.url,
        remotePath: seriesPath,
        fileName: this.sanitizeFileName(fileName),
        progress: 0,
        status: 'pending',
      };

      this.queue.push(task);
      taskIds.push(task.id);
    });

    this.saveQueueToStorage();
    this.notifyListeners();

    return taskIds;
  }

  async startQueue(): Promise<void> {
    if (this.isRunning || !this.sshService) {
      return;
    }

    // Verificar que hay una conexión SSH activa antes de iniciar la cola
    const currentConnection = this.sshService.getCurrentConnection();
    if (!currentConnection || !currentConnection.isConnected) {
      // Marcar todas las tareas pendientes como fallidas
      this.queue.forEach(task => {
        if (task.status === 'pending') {
          task.status = 'failed';
          task.error = 'No está conectado al SSH o SFTP';
        }
      });
      this.notifyListeners();
      return;
    }

    this.isRunning = true;
    this.notifyListeners();

    while (this.isRunning && this.queue.length > 0) {
      const nextTask = this.queue.find(task => task.status === 'pending');
      
      if (!nextTask) {
        break;
      }

      await this.processTask(nextTask);
    }

    this.isRunning = false;
    this.currentTask = null;
    this.notifyListeners();
  }

  stopQueue(): void {
    this.isRunning = false;

    if (this.currentTask) {
      this.currentTask.status = 'paused';
    }

    this.notifyListeners();
  }

  pauseTask(taskId: string): void {
    const task = this.queue.find(t => t.id === taskId);
    if (task && task.status === 'downloading') {
      task.status = 'paused';
      this.notifyListeners();
    }
  }

  resumeTask(taskId: string): void {
    const task = this.queue.find(t => t.id === taskId);
    if (task && task.status === 'paused') {
      task.status = 'pending';
      this.notifyListeners();
    }
  }

  removeTask(taskId: string): void {
    const index = this.queue.findIndex(t => t.id === taskId);
    if (index > -1) {
      this.queue.splice(index, 1);
      this.saveQueueToStorage();
      this.notifyListeners();
    }
  }

  clearCompleted(): void {
    this.queue = this.queue.filter(task => task.status !== 'completed');
    this.saveQueueToStorage();
    this.notifyListeners();
  }

  clearFailed(): void {
    this.queue = this.queue.filter(task => task.status !== 'failed');
    this.saveQueueToStorage();
    this.notifyListeners();
  }

  private async processTask(task: DownloadTask): Promise<void> {
    if (!this.sshService) {
      task.status = 'failed';
      task.error = 'Servicio SSH no disponible';
      this.notifyListeners();
      return;
    }

    // Verificar que hay una conexión SSH activa
    const currentConnection = this.sshService.getCurrentConnection();
    if (!currentConnection || !currentConnection.isConnected) {
      task.status = 'failed';
      task.error = 'No está conectado al SSH o SFTP';
      this.notifyListeners();
      return;
    }

    this.currentTask = task;
    task.status = 'downloading';
    task.startTime = new Date();
    this.notifyListeners();

    try {
      // Crear directorio si no existe
      await this.ensureDirectoryExists(task.remotePath);

      // Iniciar descarga
      await this.sshService.downloadFile(task.remotePath, task.url, task.fileName);

      // Monitorear progreso
      await this.monitorProgress(task);

      task.status = 'completed';
      task.endTime = new Date();
      task.progress = 100;

    } catch (error) {
      task.status = 'failed';
      task.error = (error as Error).message;
      task.endTime = new Date();
    }

    this.saveQueueToStorage();
    this.notifyListeners();
  }

  private async ensureDirectoryExists(path: string): Promise<void> {
    if (!this.sshService) return;

    try {
      // Intentar crear el directorio (mkdir -p equivalente)
      await this.sshService.executeCommand(`mkdir -p "${path}"`);
    } catch (error) {
      // Ignorar errores si el directorio ya existe
      console.log('Directory creation info:', error);
    }
  }

  private async monitorProgress(task: DownloadTask): Promise<void> {
    // Simplificar el monitoreo para evitar bloqueos del hilo principal
    let attempts = 0;
    const maxAttempts = 60; // 60 segundos máximo

    while (attempts < maxAttempts && task.status === 'downloading') {
      try {
        // Esperar 1 segundo entre verificaciones
        await new Promise(resolve => setTimeout(resolve, 1000));

        if (!this.sshService || task.status !== 'downloading') {
          break;
        }

        const currentSize = await this.sshService.getDownloadProgress(task.remotePath, task.fileName);

        if (currentSize > 0) {
          // Calcular velocidad
          const timeDiff = (Date.now() - (task.startTime?.getTime() || Date.now())) / 1000;
          if (timeDiff > 0) {
            task.speed = currentSize / timeDiff;
          }

          // Actualizar progreso estimado
          task.progress = Math.min(50 + (attempts * 0.8), 95);
        }

        this.notifyListeners();
        attempts++;
      } catch (error) {
        console.log('Progress monitoring error:', error);
        break;
      }
    }
  }

  getQueueState(): DownloadQueue {
    return {
      tasks: [...this.queue],
      isRunning: this.isRunning,
      currentTask: this.currentTask?.id,
      totalTasks: this.queue.length,
      completedTasks: this.queue.filter(t => t.status === 'completed').length,
      failedTasks: this.queue.filter(t => t.status === 'failed').length,
    };
  }

  private extractFileName(url: string, title: string): string {
    try {
      const urlObj = new URL(url);
      const pathname = urlObj.pathname;
      let fileName = pathname.split('/').pop() || title;
      
      // Si no tiene extensión, usar el título y agregar extensión
      if (!fileName.includes('.')) {
        fileName = `${this.sanitizeFileName(title)}.mkv`;
      }
      
      return this.sanitizeFileName(fileName);
    } catch (error) {
      return `${this.sanitizeFileName(title)}.mkv`;
    }
  }

  private sanitizeFileName(fileName: string): string {
    return fileName
      .replace(/[<>:"/\\|?*]/g, '_')
      .replace(/\s+/g, ' ')
      .trim();
  }

  private generateId(): string {
    return Math.random().toString(36).substr(2, 9);
  }

  private saveQueueToStorage(): void {
    try {
      localStorage.setItem('downloadQueue', JSON.stringify(this.queue));
    } catch (error) {
      console.error('Failed to save queue to storage:', error);
    }
  }

  private loadQueueFromStorage(): void {
    try {
      const saved = localStorage.getItem('downloadQueue');
      if (saved) {
        this.queue = JSON.parse(saved);
        // Resetear estados de descarga en progreso
        this.queue.forEach(task => {
          if (task.status === 'downloading') {
            task.status = 'pending';
          }
        });
      }
    } catch (error) {
      console.error('Failed to load queue from storage:', error);
      this.queue = [];
    }
  }
}
