// Estado global de la aplicación
const AppState = {
    m3uData: [],
    groupedData: [],
    sshConnection: null,
    downloadQueue: [],
    isConnected: false,
    currentPath: '/',
    fileSystem: {}
};

// Utilidades
const Utils = {
    updateStatus(element, active) {
        const statusDot = document.getElementById(element);
        if (active) {
            statusDot.classList.add('active');
        } else {
            statusDot.classList.remove('active');
        }
    },

    showLoading(buttonId, loadingId) {
        document.getElementById(buttonId).style.display = 'none';
        document.getElementById(loadingId).classList.remove('hidden');
    },

    hideLoading(buttonId, loadingId) {
        document.getElementById(buttonId).style.display = 'inline';
        document.getElementById(loadingId).classList.add('hidden');
    },

    formatFileSize(bytes) {
        if (bytes === 0) return '0 Bytes';
        const k = 1024;
        const sizes = ['Bytes', 'KB', 'MB', 'GB'];
        const i = Math.floor(Math.log(bytes) / Math.log(k));
        return parseFloat((bytes / Math.pow(k, i)).toFixed(2)) + ' ' + sizes[i];
    },

    detectContentType(name, groupTitle) {
        const lowerName = name.toLowerCase();
        const lowerGroup = (groupTitle || '').toLowerCase();

        // Detectar series por patrones S##E##
        if (/s\d+e\d+/i.test(lowerName)) {
            return 'series';
        }

        // Detectar por grupo
        if (lowerGroup.includes('movie') || lowerGroup.includes('pelicula')) {
            return 'movie';
        }

        if (lowerGroup.includes('series') || lowerGroup.includes('tv show')) {
            return 'series';
        }

        if (lowerGroup.includes('live') || lowerGroup.includes('tv') || lowerGroup.includes('canal')) {
            return 'live';
        }

        // Por defecto, si no tiene patrones de serie, es película
        return 'movie';
    },

    extractSeriesInfo(name) {
        // Extraer información de series: nombre, temporada, episodio
        const match = name.match(/^(.*?)\s*[Ss](\d+)[Ee](\d+)(.*)$/);
        if (match) {
            const [, seriesName, season, episode, extra] = match;
            return {
                seriesName: seriesName.trim(),
                season: parseInt(season),
                episode: parseInt(episode),
                extra: extra.trim(),
                fullName: name
            };
        }
        return null;
    },

    groupSeries(data) {
        const grouped = [];
        const seriesMap = new Map();

        data.forEach(item => {
            if (item.type === 'series') {
                const seriesInfo = this.extractSeriesInfo(item.title);
                if (seriesInfo) {
                    const key = seriesInfo.seriesName.toLowerCase();

                    if (!seriesMap.has(key)) {
                        seriesMap.set(key, {
                            type: 'series-group',
                            seriesName: seriesInfo.seriesName,
                            seasons: new Map(),
                            totalEpisodes: 0,
                            groupTitle: item.groupTitle
                        });
                    }

                    const series = seriesMap.get(key);
                    const seasonKey = seriesInfo.season;

                    if (!series.seasons.has(seasonKey)) {
                        series.seasons.set(seasonKey, {
                            season: seasonKey,
                            episodes: []
                        });
                    }

                    series.seasons.get(seasonKey).episodes.push({
                        ...item,
                        seriesInfo
                    });
                    series.totalEpisodes++;
                }
            } else {
                // Películas y TV en vivo se mantienen individuales
                grouped.push(item);
            }
        });

        // Convertir series agrupadas a array
        seriesMap.forEach(series => {
            grouped.push(series);
        });

        return grouped;
    }
};

// Parser M3U
const M3UParser = {
    parse(content) {
        const lines = content.split('\n').map(line => line.trim()).filter(line => line);
        const entries = [];
        let currentEntry = null;

        for (let i = 0; i < lines.length; i++) {
            const line = lines[i];
            
            if (line.startsWith('#EXTINF:')) {
                // Parsear información del canal
                const match = line.match(/#EXTINF:(-?\d+(?:\.\d+)?)\s*(?:tvg-id="([^"]*)")?\s*(?:tvg-name="([^"]*)")?\s*(?:tvg-logo="([^"]*)")?\s*(?:group-title="([^"]*)")?\s*,(.*)$/);
                
                if (match) {
                    const [, duration, tvgId, tvgName, tvgLogo, groupTitle, title] = match;
                    
                    currentEntry = {
                        duration: parseFloat(duration),
                        tvgId: tvgId || '',
                        tvgName: tvgName || '',
                        tvgLogo: tvgLogo || '',
                        groupTitle: groupTitle || '',
                        title: title || '',
                        url: '',
                        type: ''
                    };
                }
            } else if (line.startsWith('http') && currentEntry) {
                // URL del stream
                currentEntry.url = line;
                currentEntry.type = Utils.detectContentType(currentEntry.title, currentEntry.groupTitle);
                entries.push(currentEntry);
                currentEntry = null;
            }
        }

        return entries;
    }
};

// Manejador de archivos M3U
const M3UHandler = {
    loadFile(file) {
        const reader = new FileReader();
        reader.onload = (e) => {
            this.processContent(e.target.result);
        };
        reader.readAsText(file);
    },

    async loadFromURL(url) {
        try {
            Utils.updateStatus('m3u-status', false);
            const response = await fetch(url);
            const content = await response.text();
            this.processContent(content);
        } catch (error) {
            alert('Error al cargar M3U desde URL: ' + error.message);
        }
    },

    processContent(content) {
        try {
            AppState.m3uData = M3UParser.parse(content);
            AppState.groupedData = Utils.groupSeries(AppState.m3uData);
            this.updateUI();
            Utils.updateStatus('m3u-status', true);

            // Mostrar contenido
            document.getElementById('m3u-content').classList.remove('hidden');
            document.getElementById('m3u-content').classList.add('fade-in');
        } catch (error) {
            alert('Error al procesar M3U: ' + error.message);
        }
    },

    updateUI() {
        const data = AppState.m3uData;
        
        // Actualizar estadísticas
        const movieCount = data.filter(item => item.type === 'movie').length;
        const seriesCount = data.filter(item => item.type === 'series').length;
        const liveCount = data.filter(item => item.type === 'live').length;
        
        document.getElementById('total-count').textContent = data.length;
        document.getElementById('movie-count').textContent = movieCount;
        document.getElementById('series-count').textContent = seriesCount;
        document.getElementById('live-count').textContent = liveCount;
        
        // Actualizar lista de contenido
        this.renderContentList();
    },

    renderContentList() {
        const container = document.getElementById('content-list');
        container.innerHTML = '';

        AppState.groupedData.forEach((item, index) => {
            const div = document.createElement('div');
            div.className = 'content-item';
            div.setAttribute('data-index', index);

            if (item.type === 'series-group') {
                // Renderizar grupo de series
                div.oncontextmenu = (e) => this.showSeriesContextMenu(e, item, index);

                const seasonCount = item.seasons.size;
                div.innerHTML = `
                    <div class="content-title">📺 ${item.seriesName}</div>
                    <div class="content-meta">
                        <span><span class="badge series">SERIES</span> ${seasonCount} temporada${seasonCount > 1 ? 's' : ''}</span>
                        <span>${item.totalEpisodes} episodios</span>
                    </div>
                `;

                // Agregar contenedor para episodios (siempre visible)
                const episodesContainer = document.createElement('div');
                episodesContainer.className = 'episodes-container';
                episodesContainer.id = `episodes-${index}`;

                // Renderizar temporadas y episodios
                const sortedSeasons = Array.from(item.seasons.values()).sort((a, b) => a.season - b.season);
                sortedSeasons.forEach(season => {
                    const seasonDiv = document.createElement('div');
                    seasonDiv.className = 'season-group';

                    // Header de temporada con menú contextual
                    const seasonHeader = document.createElement('div');
                    seasonHeader.className = 'season-title';
                    seasonHeader.innerHTML = `📁 Temporada ${season.season} (${season.episodes.length} episodios)`;
                    seasonHeader.oncontextmenu = (e) => this.showSeasonContextMenu(e, season, item);
                    seasonDiv.appendChild(seasonHeader);

                    // Episodios de la temporada
                    season.episodes.sort((a, b) => a.seriesInfo.episode - b.seriesInfo.episode).forEach(episode => {
                        const episodeDiv = document.createElement('div');
                        episodeDiv.className = 'episode-item';
                        episodeDiv.onclick = (e) => {
                            e.stopPropagation();
                            this.selectEpisode(episode);
                        };
                        episodeDiv.oncontextmenu = (e) => this.showEpisodeContextMenu(e, episode, season, item);

                        const episodeTitle = episode.seriesInfo.extra || episode.title.replace(episode.seriesInfo.fullName, '').trim();
                        episodeDiv.innerHTML = `
                            <div class="episode-title">📄 S${episode.seriesInfo.season.toString().padStart(2, '0')}E${episode.seriesInfo.episode.toString().padStart(2, '0')}: ${episodeTitle}</div>
                            <div class="episode-meta">${episode.duration}s • ${episode.groupTitle}</div>
                        `;

                        seasonDiv.appendChild(episodeDiv);
                    });

                    episodesContainer.appendChild(seasonDiv);
                });

                container.appendChild(div);
                container.appendChild(episodesContainer);
            } else {
                // Renderizar película o TV en vivo
                div.onclick = () => this.selectContent(item);
                div.oncontextmenu = (e) => this.showContentContextMenu(e, item);

                div.innerHTML = `
                    <div class="content-title">${item.title}</div>
                    <div class="content-meta">
                        <span><span class="badge ${item.type}">${item.type.toUpperCase()}</span> ${item.groupTitle}</span>
                        <span>${item.duration}s</span>
                    </div>
                `;

                container.appendChild(div);
            }
        });
    },



    selectContent(item) {
        DownloadManager.addToQueue(item);
    },

    selectEpisode(episode) {
        DownloadManager.addToQueue(episode);
    },

    showSeriesContextMenu(e, series, index) {
        e.preventDefault();
        this.createContextMenu(e, [
            {
                text: `📥 Descargar toda la serie (${series.totalEpisodes} episodios)`,
                action: () => this.downloadWholeSeries(series)
            },
            {
                text: '📂 Seleccionar temporadas...',
                action: () => this.showSeasonSelector(series)
            }
        ]);
    },

    showSeasonContextMenu(e, season, series) {
        e.preventDefault();
        e.stopPropagation();
        this.createContextMenu(e, [
            {
                text: `📥 Descargar temporada ${season.season} completa (${season.episodes.length} episodios)`,
                action: () => this.downloadSeason(season)
            },
            {
                text: `📥 Descargar toda la serie (${series.totalEpisodes} episodios)`,
                action: () => this.downloadWholeSeries(series)
            }
        ]);
    },

    showEpisodeContextMenu(e, episode, season, series) {
        e.preventDefault();
        e.stopPropagation();
        this.createContextMenu(e, [
            {
                text: '📥 Descargar este episodio',
                action: () => this.selectEpisode(episode)
            },
            {
                text: `📥 Descargar temporada ${season.season} completa (${season.episodes.length} episodios)`,
                action: () => this.downloadSeason(season)
            },
            {
                text: `📥 Descargar toda la serie (${series.totalEpisodes} episodios)`,
                action: () => this.downloadWholeSeries(series)
            }
        ]);
    },

    showContentContextMenu(e, item) {
        e.preventDefault();
        this.createContextMenu(e, [
            {
                text: '📥 Descargar',
                action: () => this.selectContent(item)
            }
        ]);
    },

    createContextMenu(e, options) {
        // Remover menú anterior si existe
        const existingMenu = document.getElementById('context-menu');
        if (existingMenu) {
            existingMenu.remove();
        }

        const menu = document.createElement('div');
        menu.id = 'context-menu';
        menu.className = 'context-menu';
        menu.style.left = e.pageX + 'px';
        menu.style.top = e.pageY + 'px';

        options.forEach(option => {
            const item = document.createElement('div');
            item.className = 'context-menu-item';
            item.textContent = option.text;
            item.onclick = () => {
                option.action();
                menu.remove();
            };
            menu.appendChild(item);
        });

        document.body.appendChild(menu);

        // Remover menú al hacer click fuera
        setTimeout(() => {
            document.addEventListener('click', () => {
                if (menu.parentNode) {
                    menu.remove();
                }
            }, { once: true });
        }, 100);
    },

    downloadWholeSeries(series) {
        series.seasons.forEach(season => {
            season.episodes.forEach(episode => {
                DownloadManager.addToQueue(episode);
            });
        });
        alert(`Se agregaron ${series.totalEpisodes} episodios de "${series.seriesName}" a la cola de descarga`);
    },

    downloadSeason(season) {
        season.episodes.forEach(episode => {
            DownloadManager.addToQueue(episode);
        });
        alert(`Se agregaron ${season.episodes.length} episodios de la temporada ${season.season} a la cola de descarga`);
    },

    showSeasonSelector(series) {
        // Crear modal para seleccionar temporadas
        const modal = document.createElement('div');
        modal.className = 'modal-overlay';

        const modalContent = document.createElement('div');
        modalContent.className = 'modal-content';

        modalContent.innerHTML = `
            <h3>Seleccionar Temporadas - ${series.seriesName}</h3>
            <div class="season-selector">
                ${Array.from(series.seasons.values()).map(season => `
                    <label class="season-checkbox">
                        <input type="checkbox" value="${season.season}">
                        Temporada ${season.season} (${season.episodes.length} episodios)
                    </label>
                `).join('')}
            </div>
            <div class="modal-buttons">
                <button class="btn btn-success" onclick="this.downloadSelectedSeasons('${series.seriesName}')">Descargar Seleccionadas</button>
                <button class="btn" onclick="this.closeModal()">Cancelar</button>
            </div>
        `;

        modal.appendChild(modalContent);
        document.body.appendChild(modal);
    }
};

// Manejador SSH/SFTP
const SSHHandler = {
    async connect() {
        const host = document.getElementById('ssh-host').value;
        const port = document.getElementById('ssh-port').value;
        const user = document.getElementById('ssh-user').value;
        const password = document.getElementById('ssh-password').value;

        if (!host || !user || !password) {
            alert('Por favor completa todos los campos de conexión SSH');
            return;
        }

        Utils.showLoading('ssh-btn-text', 'ssh-loading');

        try {
            // En una aplicación real, esto se haría a través de un backend
            // Por ahora simularemos la conexión pero con estructura real de Ubuntu
            await new Promise(resolve => setTimeout(resolve, 2000));

            AppState.sshConnection = { host, port, user, password };
            AppState.isConnected = true;
            AppState.currentPath = `/home/<USER>

            Utils.updateStatus('ssh-status', true);
            document.getElementById('ssh-content').classList.remove('hidden');
            document.getElementById('ssh-content').classList.add('fade-in');

            // Actualizar path display
            document.getElementById('current-path').textContent = AppState.currentPath;

            // Cargar lista de archivos inicial
            await this.loadFileList();

            alert(`Conexión SSH establecida correctamente con ${user}@${host}`);
        } catch (error) {
            alert('Error al conectar SSH: ' + error.message);
            Utils.updateStatus('ssh-status', false);
        } finally {
            Utils.hideLoading('ssh-btn-text', 'ssh-loading');
        }
    },

    async loadFileList() {
        const container = document.getElementById('file-list');

        // Mostrar indicador de carga
        container.innerHTML = `
            <div class="loading-indicator">
                🔄 Cargando contenido del directorio...<br>
                <small>${AppState.currentPath}</small>
            </div>
        `;

        try {
            // Simular llamada SFTP real con estructura de directorios más realista
            const files = await this.simulateSFTPList(AppState.currentPath);
            this.renderFileList(files);
        } catch (error) {
            container.innerHTML = `
                <div style="text-align: center; color: #e74c3c; margin-top: 50px;">
                    ❌ Error al cargar directorio<br>
                    <small>${error.message}</small><br>
                    <button class="btn" onclick="SSHHandler.loadFileList()" style="margin-top: 15px;">🔄 Reintentar</button>
                </div>
            `;
        }
    },

    async simulateSFTPList(path) {
        // Simular delay de red real
        await new Promise(resolve => setTimeout(resolve, 800));

        // En una implementación real, esto ejecutaría comandos SSH como:
        // ssh user@host "ls -la /path/to/directory"
        // Por ahora simulamos con estructura realista de Ubuntu

        const user = AppState.sshConnection.user;
        const userHome = `/home/<USER>

        // Generar estructura dinámica basada en el usuario conectado
        const generateUserStructure = () => {
            return {
                '/': [
                    { name: 'home', type: 'directory', size: 4096, permissions: 'drwxr-xr-x', owner: 'root', modified: '2024-01-15 10:30' },
                    { name: 'var', type: 'directory', size: 4096, permissions: 'drwxr-xr-x', owner: 'root', modified: '2024-01-10 08:15' },
                    { name: 'usr', type: 'directory', size: 4096, permissions: 'drwxr-xr-x', owner: 'root', modified: '2024-01-05 14:20' },
                    { name: 'etc', type: 'directory', size: 4096, permissions: 'drwxr-xr-x', owner: 'root', modified: '2024-01-12 16:45' },
                    { name: 'tmp', type: 'directory', size: 4096, permissions: 'drwxrwxrwt', owner: 'root', modified: '2024-01-20 09:10' },
                    { name: 'opt', type: 'directory', size: 4096, permissions: 'drwxr-xr-x', owner: 'root', modified: '2024-01-08 11:30' }
                ],
                '/home': [
                    { name: '..', type: 'directory', size: 4096, permissions: 'drwxr-xr-x', owner: 'root', modified: '2024-01-15 10:30' },
                    { name: user, type: 'directory', size: 4096, permissions: 'drwxr-xr-x', owner: user, modified: '2024-01-20 15:45' },
                    { name: 'ubuntu', type: 'directory', size: 4096, permissions: 'drwxr-xr-x', owner: 'ubuntu', modified: '2024-01-18 12:20' }
                ],
                [userHome]: [
                    { name: '..', type: 'directory', size: 4096, permissions: 'drwxr-xr-x', owner: 'root', modified: '2024-01-15 10:30' },
                    { name: 'Desktop', type: 'directory', size: 4096, permissions: 'drwxr-xr-x', owner: user, modified: '2024-01-20 09:15' },
                    { name: 'Documents', type: 'directory', size: 4096, permissions: 'drwxr-xr-x', owner: user, modified: '2024-01-19 14:30' },
                    { name: 'Downloads', type: 'directory', size: 4096, permissions: 'drwxr-xr-x', owner: user, modified: '2024-01-20 16:45' },
                    { name: 'Music', type: 'directory', size: 4096, permissions: 'drwxr-xr-x', owner: user, modified: '2024-01-18 11:20' },
                    { name: 'Pictures', type: 'directory', size: 4096, permissions: 'drwxr-xr-x', owner: user, modified: '2024-01-17 13:10' },
                    { name: 'Videos', type: 'directory', size: 4096, permissions: 'drwxr-xr-x', owner: user, modified: '2024-01-20 18:30' },
                    { name: 'Public', type: 'directory', size: 4096, permissions: 'drwxr-xr-x', owner: user, modified: '2024-01-15 10:45' },
                    { name: 'Templates', type: 'directory', size: 4096, permissions: 'drwxr-xr-x', owner: user, modified: '2024-01-15 10:45' },
                    { name: '.bashrc', type: 'file', size: 3771, permissions: '-rw-r--r--', owner: user, modified: '2024-01-15 10:45' },
                    { name: '.profile', type: 'file', size: 807, permissions: '-rw-r--r--', owner: user, modified: '2024-01-15 10:45' },
                    { name: '.ssh', type: 'directory', size: 4096, permissions: 'drwx------', owner: user, modified: '2024-01-16 08:30' }
                ],
                [`${userHome}/Downloads`]: [
                    { name: '..', type: 'directory', size: 4096, permissions: 'drwxr-xr-x', owner: user, modified: '2024-01-20 16:45' },
                    { name: 'Movies', type: 'directory', size: 4096, permissions: 'drwxr-xr-x', owner: user, modified: '2024-01-20 14:20' },
                    { name: 'Series', type: 'directory', size: 4096, permissions: 'drwxr-xr-x', owner: user, modified: '2024-01-20 16:30' },
                    { name: 'Software', type: 'directory', size: 4096, permissions: 'drwxr-xr-x', owner: user, modified: '2024-01-19 10:15' },
                    { name: 'Torrents', type: 'directory', size: 4096, permissions: 'drwxr-xr-x', owner: user, modified: '2024-01-20 12:45' },
                    { name: 'wget-log', type: 'file', size: 15432, permissions: '-rw-r--r--', owner: user, modified: '2024-01-20 18:45' }
                ],
                [`${userHome}/Downloads/Movies`]: [
                    { name: '..', type: 'directory', size: 4096, permissions: 'drwxr-xr-x', owner: user, modified: '2024-01-20 14:20' },
                    { name: '2023', type: 'directory', size: 4096, permissions: 'drwxr-xr-x', owner: user, modified: '2024-01-18 16:30' },
                    { name: '2024', type: 'directory', size: 4096, permissions: 'drwxr-xr-x', owner: user, modified: '2024-01-20 14:20' },
                    { name: 'Action', type: 'directory', size: 4096, permissions: 'drwxr-xr-x', owner: user, modified: '2024-01-19 11:45' },
                    { name: 'Comedy', type: 'directory', size: 4096, permissions: 'drwxr-xr-x', owner: user, modified: '2024-01-17 15:20' },
                    { name: 'Drama', type: 'directory', size: 4096, permissions: 'drwxr-xr-x', owner: user, modified: '2024-01-16 13:10' },
                    { name: 'Avengers.Endgame.2019.2160p.BluRay.x265.mkv', type: 'file', size: 8589934592, permissions: '-rw-r--r--', owner: user, modified: '2024-01-20 14:15' },
                    { name: 'Inception.2010.1080p.BluRay.x264.mp4', type: 'file', size: 4294967296, permissions: '-rw-r--r--', owner: user, modified: '2024-01-19 16:30' }
                ],
                [`${userHome}/Downloads/Series`]: [
                    { name: '..', type: 'directory', size: 4096, permissions: 'drwxr-xr-x', owner: user, modified: '2024-01-20 16:30' },
                    { name: 'Breaking.Bad', type: 'directory', size: 4096, permissions: 'drwxr-xr-x', owner: user, modified: '2024-01-20 16:30' },
                    { name: 'Game.of.Thrones', type: 'directory', size: 4096, permissions: 'drwxr-xr-x', owner: user, modified: '2024-01-19 14:45' },
                    { name: 'The.Office.US', type: 'directory', size: 4096, permissions: 'drwxr-xr-x', owner: user, modified: '2024-01-18 12:20' },
                    { name: 'Stranger.Things', type: 'directory', size: 4096, permissions: 'drwxr-xr-x', owner: user, modified: '2024-01-17 18:15' },
                    { name: 'Better.Call.Saul', type: 'directory', size: 4096, permissions: 'drwxr-xr-x', owner: user, modified: '2024-01-16 20:30' }
                ],
                [`${userHome}/Downloads/Series/Breaking.Bad`]: [
                    { name: '..', type: 'directory', size: 4096, permissions: 'drwxr-xr-x', owner: user, modified: '2024-01-20 16:30' },
                    { name: 'Season.01', type: 'directory', size: 4096, permissions: 'drwxr-xr-x', owner: user, modified: '2024-01-20 16:25' },
                    { name: 'Season.02', type: 'directory', size: 4096, permissions: 'drwxr-xr-x', owner: user, modified: '2024-01-20 15:45' },
                    { name: 'Season.03', type: 'directory', size: 4096, permissions: 'drwxr-xr-x', owner: user, modified: '2024-01-20 14:30' },
                    { name: 'Season.04', type: 'directory', size: 4096, permissions: 'drwxr-xr-x', owner: user, modified: '2024-01-20 13:15' },
                    { name: 'Season.05', type: 'directory', size: 4096, permissions: 'drwxr-xr-x', owner: user, modified: '2024-01-20 12:00' }
                ],
                [`${userHome}/Downloads/Series/Breaking.Bad/Season.01`]: [
                    { name: '..', type: 'directory', size: 4096, permissions: 'drwxr-xr-x', owner: user, modified: '2024-01-20 16:25' },
                    { name: 'Breaking.Bad.S01E01.Pilot.1080p.BluRay.x264.mkv', type: 'file', size: 1610612736, permissions: '-rw-r--r--', owner: user, modified: '2024-01-20 16:20' },
                    { name: 'Breaking.Bad.S01E02.Cat.in.the.Bag.1080p.BluRay.x264.mkv', type: 'file', size: 1543503872, permissions: '-rw-r--r--', owner: user, modified: '2024-01-20 16:15' },
                    { name: 'Breaking.Bad.S01E03.And.the.Bags.in.the.River.1080p.BluRay.x264.mkv', type: 'file', size: 1476395008, permissions: '-rw-r--r--', owner: user, modified: '2024-01-20 16:10' },
                    { name: 'Breaking.Bad.S01E04.Cancer.Man.1080p.BluRay.x264.mkv', type: 'file', size: 1409286144, permissions: '-rw-r--r--', owner: user, modified: '2024-01-20 16:05' },
                    { name: 'Breaking.Bad.S01E05.Gray.Matter.1080p.BluRay.x264.mkv', type: 'file', size: 1342177280, permissions: '-rw-r--r--', owner: user, modified: '2024-01-20 16:00' },
                    { name: 'Breaking.Bad.S01E06.Crazy.Handful.of.Nothin.1080p.BluRay.x264.mkv', type: 'file', size: 1275068416, permissions: '-rw-r--r--', owner: user, modified: '2024-01-20 15:55' },
                    { name: 'Breaking.Bad.S01E07.A.No-Rough-Stuff-Type.Deal.1080p.BluRay.x264.mkv', type: 'file', size: 1207959552, permissions: '-rw-r--r--', owner: user, modified: '2024-01-20 15:50' }
                ],
                [`${userHome}/Videos`]: [
                    { name: '..', type: 'directory', size: 4096, permissions: 'drwxr-xr-x', owner: user, modified: '2024-01-20 18:30' },
                    { name: 'Personal', type: 'directory', size: 4096, permissions: 'drwxr-xr-x', owner: user, modified: '2024-01-19 20:15' },
                    { name: 'Work', type: 'directory', size: 4096, permissions: 'drwxr-xr-x', owner: user, modified: '2024-01-18 09:30' },
                    { name: 'Webcam', type: 'directory', size: 4096, permissions: 'drwxr-xr-x', owner: user, modified: '2024-01-17 14:45' },
                    { name: 'sample_1080p.mp4', type: 'file', size: 104857600, permissions: '-rw-r--r--', owner: user, modified: '2024-01-20 18:25' }
                ],
                '/var': [
                    { name: '..', type: 'directory', size: 4096, permissions: 'drwxr-xr-x', owner: 'root', modified: '2024-01-10 08:15' },
                    { name: 'log', type: 'directory', size: 4096, permissions: 'drwxr-xr-x', owner: 'root', modified: '2024-01-20 19:00' },
                    { name: 'www', type: 'directory', size: 4096, permissions: 'drwxr-xr-x', owner: 'www-data', modified: '2024-01-19 16:30' },
                    { name: 'cache', type: 'directory', size: 4096, permissions: 'drwxr-xr-x', owner: 'root', modified: '2024-01-20 12:15' },
                    { name: 'lib', type: 'directory', size: 4096, permissions: 'drwxr-xr-x', owner: 'root', modified: '2024-01-15 14:20' }
                ]
            };
        };

        const fileSystem = generateUserStructure();

        // Normalizar path
        const normalizedPath = path === '/' ? '/' : path.replace(/\/$/, '');

        // Retornar archivos para el path actual o generar contenido dinámico
        return fileSystem[normalizedPath] || [
            { name: '..', type: 'directory', size: 4096, permissions: 'drwxr-xr-x', owner: user, modified: '2024-01-20 10:00' }
        ];
    },

    renderFileList(files) {
        const container = document.getElementById('file-list');

        if (files.length === 0) {
            container.innerHTML = `
                <div style="text-align: center; color: #bdc3c7; margin-top: 50px;">
                    📂 Directorio vacío<br>
                    <small>No hay archivos en esta ubicación</small>
                </div>
            `;
            return;
        }

        container.innerHTML = '';

        files.forEach(file => {
            const div = document.createElement('div');
            div.className = 'content-item';
            div.setAttribute('data-file-type', file.type);

            // Iconos específicos para Ubuntu
            let icon = '📄';
            if (file.type === 'directory') {
                if (file.name === '..') icon = '⬆️';
                else if (file.name.startsWith('.')) icon = '🔒';
                else icon = '📁';
            } else {
                const ext = file.name.toLowerCase().split('.').pop();
                if (['mkv', 'mp4', 'avi', 'mov'].includes(ext)) icon = '🎬';
                else if (['mp3', 'flac', 'wav'].includes(ext)) icon = '🎵';
                else if (['jpg', 'png', 'gif'].includes(ext)) icon = '🖼️';
                else if (['txt', 'log'].includes(ext)) icon = '📝';
                else if (file.name.startsWith('.')) icon = '⚙️';
            }

            const sizeText = file.type === 'file' ? Utils.formatFileSize(file.size) : '';
            const permissionsText = file.permissions || '';
            const ownerText = file.owner || '';
            const modifiedText = file.modified || '';

            div.innerHTML = `
                <div class="content-title">${icon} ${file.name}</div>
                <div class="content-meta">
                    <span>
                        <span class="badge ${file.type}">${file.type.toUpperCase()}</span>
                        ${permissionsText ? `<span class="badge permissions">${permissionsText}</span>` : ''}
                        ${ownerText ? `<span class="badge owner">${ownerText}</span>` : ''}
                    </span>
                    <span>${sizeText} ${modifiedText}</span>
                </div>
            `;

            div.onclick = () => this.selectFile(file);

            // Menú contextual para archivos
            if (file.type === 'file') {
                div.oncontextmenu = (e) => this.showFileContextMenu(e, file);
            }

            container.appendChild(div);
        });
    },

    showFileContextMenu(e, file) {
        e.preventDefault();
        const options = [
            {
                text: '📥 Descargar archivo',
                action: () => this.selectFile(file)
            },
            {
                text: '📋 Copiar ruta',
                action: () => {
                    const fullPath = `${AppState.currentPath}/${file.name}`;
                    navigator.clipboard.writeText(fullPath);
                    alert(`Ruta copiada: ${fullPath}`);
                }
            },
            {
                text: '📊 Ver propiedades',
                action: () => this.showFileProperties(file)
            }
        ];

        M3UHandler.createContextMenu(e, options);
    },

    showFileProperties(file) {
        const modal = document.createElement('div');
        modal.className = 'modal-overlay';

        const modalContent = document.createElement('div');
        modalContent.className = 'modal-content';

        modalContent.innerHTML = `
            <h3>📊 Propiedades del Archivo</h3>
            <div style="text-align: left; margin: 20px 0;">
                <p><strong>Nombre:</strong> ${file.name}</p>
                <p><strong>Tipo:</strong> ${file.type}</p>
                <p><strong>Tamaño:</strong> ${Utils.formatFileSize(file.size)}</p>
                <p><strong>Permisos:</strong> ${file.permissions || 'N/A'}</p>
                <p><strong>Propietario:</strong> ${file.owner || 'N/A'}</p>
                <p><strong>Modificado:</strong> ${file.modified || 'N/A'}</p>
                <p><strong>Ruta completa:</strong> ${AppState.currentPath}/${file.name}</p>
            </div>
            <div class="modal-buttons">
                <button class="btn" onclick="closeModal()">Cerrar</button>
            </div>
        `;

        modal.appendChild(modalContent);
        document.body.appendChild(modal);
    },

    selectFile(file) {
        if (file.type === 'directory') {
            if (file.name === '..') {
                // Navegar hacia arriba
                const pathParts = AppState.currentPath.split('/').filter(p => p);
                if (pathParts.length > 0) {
                    pathParts.pop();
                    AppState.currentPath = pathParts.length > 0 ? '/' + pathParts.join('/') : '/';
                }
            } else {
                // Entrar al directorio
                if (AppState.currentPath === '/') {
                    AppState.currentPath = '/' + file.name;
                } else {
                    AppState.currentPath += '/' + file.name;
                }
            }

            document.getElementById('current-path').textContent = AppState.currentPath;
            this.loadFileList(); // Recargar lista
        } else {
            // Seleccionar archivo para descarga
            const downloadItem = {
                id: Date.now(),
                title: file.name,
                url: `sftp://${AppState.sshConnection.host}${AppState.currentPath}/${file.name}`,
                type: this.detectFileType(file.name),
                status: 'pending',
                progress: 0,
                size: file.size,
                downloaded: 0,
                source: 'sftp'
            };

            DownloadManager.addToQueue(downloadItem);
            alert(`Archivo "${file.name}" agregado a la cola de descarga`);
        }
    },

    detectFileType(filename) {
        const ext = filename.toLowerCase().split('.').pop();
        const videoExts = ['mp4', 'mkv', 'avi', 'mov', 'wmv', 'flv', 'm4v'];
        const audioExts = ['mp3', 'flac', 'wav', 'aac', 'm4a'];

        if (videoExts.includes(ext)) {
            // Detectar si es serie por patrón en el nombre
            if (/s\d+e\d+/i.test(filename)) {
                return 'series';
            }
            return 'movie';
        } else if (audioExts.includes(ext)) {
            return 'audio';
        }

        return 'file';
    }
};

// Manejador de descargas
const DownloadManager = {
    addToQueue(item) {
        // Evitar duplicados
        const exists = AppState.downloadQueue.find(d =>
            d.url === item.url && d.title === item.title
        );

        if (exists) {
            alert(`"${item.title}" ya está en la cola de descarga`);
            return;
        }

        const download = {
            id: Date.now() + Math.random(),
            title: item.title,
            url: item.url,
            type: item.type || 'file',
            status: 'pending',
            progress: 0,
            size: item.size || 0,
            downloaded: 0,
            source: item.source || 'm3u',
            groupTitle: item.groupTitle || '',
            seriesInfo: item.seriesInfo || null
        };

        AppState.downloadQueue.push(download);
        this.updateUI();
        Utils.updateStatus('download-status', true);
    },

    updateUI() {
        const queue = AppState.downloadQueue;
        
        // Actualizar estadísticas
        const total = queue.length;
        const active = queue.filter(d => d.status === 'downloading').length;
        const completed = queue.filter(d => d.status === 'completed').length;
        const failed = queue.filter(d => d.status === 'failed').length;
        
        document.getElementById('queue-total').textContent = total;
        document.getElementById('queue-active').textContent = active;
        document.getElementById('queue-completed').textContent = completed;
        document.getElementById('queue-failed').textContent = failed;
        
        // Renderizar lista
        this.renderDownloadList();
    },

    renderDownloadList() {
        const container = document.getElementById('download-list');

        if (AppState.downloadQueue.length === 0) {
            container.innerHTML = `
                <div style="text-align: center; color: #bdc3c7; margin-top: 50px;">
                    No hay descargas en la cola.<br>
                    Selecciona contenido desde el panel M3U o SFTP para comenzar.
                </div>
            `;
            return;
        }

        container.innerHTML = '';

        AppState.downloadQueue.forEach(download => {
            const div = document.createElement('div');
            div.className = 'content-item';

            const statusIcon = {
                'pending': '⏳',
                'downloading': '📥',
                'completed': '✅',
                'failed': '❌',
                'paused': '⏸️'
            }[download.status];

            const sourceIcon = download.source === 'sftp' ? '🔐' : '📺';
            const sizeText = download.size > 0 ? Utils.formatFileSize(download.size) : '';

            // Información adicional para series
            let extraInfo = '';
            if (download.seriesInfo) {
                extraInfo = `S${download.seriesInfo.season.toString().padStart(2, '0')}E${download.seriesInfo.episode.toString().padStart(2, '0')}`;
            }

            div.innerHTML = `
                <div class="content-title">${statusIcon} ${sourceIcon} ${download.title}</div>
                <div class="content-meta">
                    <span>
                        <span class="badge ${download.type}">${download.type.toUpperCase()}</span>
                        ${extraInfo ? `<span class="badge series">${extraInfo}</span>` : ''}
                        ${download.groupTitle}
                    </span>
                    <span>${download.progress}% ${sizeText}</span>
                </div>
                <div class="progress-bar">
                    <div class="progress-fill" style="width: ${download.progress}%"></div>
                </div>
            `;

            // Agregar menú contextual para descargas
            div.oncontextmenu = (e) => this.showDownloadContextMenu(e, download);

            container.appendChild(div);
        });
    },

    showDownloadContextMenu(e, download) {
        e.preventDefault();
        const options = [
            {
                text: '❌ Cancelar descarga',
                action: () => this.cancelDownload(download.id)
            }
        ];

        if (download.status === 'paused') {
            options.unshift({
                text: '▶️ Reanudar',
                action: () => this.resumeDownload(download.id)
            });
        } else if (download.status === 'downloading') {
            options.unshift({
                text: '⏸️ Pausar',
                action: () => this.pauseDownload(download.id)
            });
        } else if (download.status === 'failed') {
            options.unshift({
                text: '🔄 Reintentar',
                action: () => this.retryDownload(download.id)
            });
        }

        M3UHandler.createContextMenu(e, options);
    },

    cancelDownload(id) {
        AppState.downloadQueue = AppState.downloadQueue.filter(d => d.id !== id);
        this.updateUI();
    },

    pauseDownload(id) {
        const download = AppState.downloadQueue.find(d => d.id === id);
        if (download) {
            download.status = 'paused';
            this.updateUI();
        }
    },

    resumeDownload(id) {
        const download = AppState.downloadQueue.find(d => d.id === id);
        if (download) {
            download.status = 'downloading';
            this.simulateDownload(download);
            this.updateUI();
        }
    },

    retryDownload(id) {
        const download = AppState.downloadQueue.find(d => d.id === id);
        if (download) {
            download.status = 'pending';
            download.progress = 0;
            this.updateUI();
        }
    },

    start() {
        AppState.downloadQueue.forEach(download => {
            if (download.status === 'pending') {
                download.status = 'downloading';
                this.simulateDownload(download);
            }
        });
        this.updateUI();
    },

    pause() {
        AppState.downloadQueue.forEach(download => {
            if (download.status === 'downloading') {
                download.status = 'paused';
            }
        });
        this.updateUI();
    },

    clearCompleted() {
        AppState.downloadQueue = AppState.downloadQueue.filter(d => d.status !== 'completed');
        this.updateUI();
    },

    simulateDownload(download) {
        const interval = setInterval(() => {
            if (download.status !== 'downloading') {
                clearInterval(interval);
                return;
            }
            
            download.progress += Math.random() * 10;
            if (download.progress >= 100) {
                download.progress = 100;
                download.status = 'completed';
                clearInterval(interval);
            }
            
            this.updateUI();
        }, 500);
    }
};

// Event Listeners
document.addEventListener('DOMContentLoaded', () => {
    // Drag & Drop para M3U
    const dropZone = document.getElementById('m3u-drop-zone');
    
    dropZone.addEventListener('dragover', (e) => {
        e.preventDefault();
        dropZone.classList.add('dragover');
    });
    
    dropZone.addEventListener('dragleave', () => {
        dropZone.classList.remove('dragover');
    });
    
    dropZone.addEventListener('drop', (e) => {
        e.preventDefault();
        dropZone.classList.remove('dragover');
        
        const files = e.dataTransfer.files;
        if (files.length > 0) {
            M3UHandler.loadFile(files[0]);
        }
    });
    
    // Input de archivo
    document.getElementById('file-input').addEventListener('change', (e) => {
        if (e.target.files.length > 0) {
            M3UHandler.loadFile(e.target.files[0]);
        }
    });
});

// Funciones globales para los botones
function selectM3UFile() {
    document.getElementById('file-input').click();
}

function loadM3UFromURL() {
    const url = document.getElementById('m3u-url').value;
    if (url) {
        M3UHandler.loadFromURL(url);
    } else {
        alert('Por favor ingresa una URL válida');
    }
}

function connectSSH() {
    SSHHandler.connect();
}

function startDownloads() {
    DownloadManager.start();
}

function pauseDownloads() {
    DownloadManager.pause();
}

function clearCompleted() {
    DownloadManager.clearCompleted();
}

// Funciones para modales
function closeModal() {
    const modal = document.querySelector('.modal-overlay');
    if (modal) {
        modal.remove();
    }
}

function downloadSelectedSeasons(seriesName) {
    const checkboxes = document.querySelectorAll('.season-checkbox input:checked');
    const selectedSeasons = Array.from(checkboxes).map(cb => parseInt(cb.value));

    if (selectedSeasons.length === 0) {
        alert('Por favor selecciona al menos una temporada');
        return;
    }

    // Encontrar la serie en los datos agrupados
    const series = AppState.groupedData.find(item =>
        item.type === 'series-group' && item.seriesName === seriesName
    );

    if (series) {
        let totalEpisodes = 0;
        selectedSeasons.forEach(seasonNum => {
            const season = series.seasons.get(seasonNum);
            if (season) {
                season.episodes.forEach(episode => {
                    DownloadManager.addToQueue(episode);
                    totalEpisodes++;
                });
            }
        });

        alert(`Se agregaron ${totalEpisodes} episodios de ${selectedSeasons.length} temporada(s) a la cola de descarga`);
    }

    closeModal();
}

// Prevenir menú contextual del navegador en toda la aplicación
document.addEventListener('contextmenu', (e) => {
    // Solo prevenir si no es en un input o textarea
    if (!e.target.matches('input, textarea')) {
        e.preventDefault();
    }
});

// Cerrar menús contextuales al hacer scroll
document.addEventListener('scroll', () => {
    const contextMenu = document.getElementById('context-menu');
    if (contextMenu) {
        contextMenu.remove();
    }
}, true);
