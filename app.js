// Estado global de la aplicación
const AppState = {
    m3uData: [],
    sshConnection: null,
    downloadQueue: [],
    isConnected: false,
    currentPath: '/home/<USER>'
};

// Utilidades
const Utils = {
    updateStatus(element, active) {
        const statusDot = document.getElementById(element);
        if (active) {
            statusDot.classList.add('active');
        } else {
            statusDot.classList.remove('active');
        }
    },

    showLoading(buttonId, loadingId) {
        document.getElementById(buttonId).style.display = 'none';
        document.getElementById(loadingId).classList.remove('hidden');
    },

    hideLoading(buttonId, loadingId) {
        document.getElementById(buttonId).style.display = 'inline';
        document.getElementById(loadingId).classList.add('hidden');
    },

    formatFileSize(bytes) {
        if (bytes === 0) return '0 Bytes';
        const k = 1024;
        const sizes = ['Bytes', 'KB', 'MB', 'GB'];
        const i = Math.floor(Math.log(bytes) / Math.log(k));
        return parseFloat((bytes / Math.pow(k, i)).toFixed(2)) + ' ' + sizes[i];
    },

    detectContentType(name, groupTitle) {
        const lowerName = name.toLowerCase();
        const lowerGroup = (groupTitle || '').toLowerCase();
        
        // Detectar series por patrones S##E##
        if (/s\d+e\d+/i.test(lowerName)) {
            return 'series';
        }
        
        // Detectar por grupo
        if (lowerGroup.includes('movie') || lowerGroup.includes('pelicula')) {
            return 'movie';
        }
        
        if (lowerGroup.includes('series') || lowerGroup.includes('tv show')) {
            return 'series';
        }
        
        if (lowerGroup.includes('live') || lowerGroup.includes('tv') || lowerGroup.includes('canal')) {
            return 'live';
        }
        
        // Por defecto, si no tiene patrones de serie, es película
        return 'movie';
    }
};

// Parser M3U
const M3UParser = {
    parse(content) {
        const lines = content.split('\n').map(line => line.trim()).filter(line => line);
        const entries = [];
        let currentEntry = null;

        for (let i = 0; i < lines.length; i++) {
            const line = lines[i];
            
            if (line.startsWith('#EXTINF:')) {
                // Parsear información del canal
                const match = line.match(/#EXTINF:(-?\d+(?:\.\d+)?)\s*(?:tvg-id="([^"]*)")?\s*(?:tvg-name="([^"]*)")?\s*(?:tvg-logo="([^"]*)")?\s*(?:group-title="([^"]*)")?\s*,(.*)$/);
                
                if (match) {
                    const [, duration, tvgId, tvgName, tvgLogo, groupTitle, title] = match;
                    
                    currentEntry = {
                        duration: parseFloat(duration),
                        tvgId: tvgId || '',
                        tvgName: tvgName || '',
                        tvgLogo: tvgLogo || '',
                        groupTitle: groupTitle || '',
                        title: title || '',
                        url: '',
                        type: ''
                    };
                }
            } else if (line.startsWith('http') && currentEntry) {
                // URL del stream
                currentEntry.url = line;
                currentEntry.type = Utils.detectContentType(currentEntry.title, currentEntry.groupTitle);
                entries.push(currentEntry);
                currentEntry = null;
            }
        }

        return entries;
    }
};

// Manejador de archivos M3U
const M3UHandler = {
    loadFile(file) {
        const reader = new FileReader();
        reader.onload = (e) => {
            this.processContent(e.target.result);
        };
        reader.readAsText(file);
    },

    async loadFromURL(url) {
        try {
            Utils.updateStatus('m3u-status', false);
            const response = await fetch(url);
            const content = await response.text();
            this.processContent(content);
        } catch (error) {
            alert('Error al cargar M3U desde URL: ' + error.message);
        }
    },

    processContent(content) {
        try {
            AppState.m3uData = M3UParser.parse(content);
            this.updateUI();
            Utils.updateStatus('m3u-status', true);
            
            // Mostrar contenido
            document.getElementById('m3u-content').classList.remove('hidden');
            document.getElementById('m3u-content').classList.add('fade-in');
        } catch (error) {
            alert('Error al procesar M3U: ' + error.message);
        }
    },

    updateUI() {
        const data = AppState.m3uData;
        
        // Actualizar estadísticas
        const movieCount = data.filter(item => item.type === 'movie').length;
        const seriesCount = data.filter(item => item.type === 'series').length;
        const liveCount = data.filter(item => item.type === 'live').length;
        
        document.getElementById('total-count').textContent = data.length;
        document.getElementById('movie-count').textContent = movieCount;
        document.getElementById('series-count').textContent = seriesCount;
        document.getElementById('live-count').textContent = liveCount;
        
        // Actualizar lista de contenido
        this.renderContentList();
    },

    renderContentList() {
        const container = document.getElementById('content-list');
        container.innerHTML = '';
        
        AppState.m3uData.forEach((item, index) => {
            const div = document.createElement('div');
            div.className = 'content-item';
            div.onclick = () => this.selectContent(index);
            
            div.innerHTML = `
                <div class="content-title">${item.title}</div>
                <div class="content-meta">
                    <span><span class="badge ${item.type}">${item.type.toUpperCase()}</span> ${item.groupTitle}</span>
                    <span>${item.duration}s</span>
                </div>
            `;
            
            container.appendChild(div);
        });
    },

    selectContent(index) {
        // Remover selección anterior
        document.querySelectorAll('.content-item').forEach(item => {
            item.classList.remove('selected');
        });
        
        // Seleccionar nuevo item
        document.querySelectorAll('.content-item')[index].classList.add('selected');
        
        // Agregar a cola de descarga
        const item = AppState.m3uData[index];
        DownloadManager.addToQueue(item);
    }
};

// Manejador SSH/SFTP
const SSHHandler = {
    async connect() {
        const host = document.getElementById('ssh-host').value;
        const port = document.getElementById('ssh-port').value;
        const user = document.getElementById('ssh-user').value;
        const password = document.getElementById('ssh-password').value;
        
        if (!host || !user || !password) {
            alert('Por favor completa todos los campos de conexión SSH');
            return;
        }
        
        Utils.showLoading('ssh-btn-text', 'ssh-loading');
        
        try {
            // Simular conexión SSH (en una implementación real usarías una API backend)
            await new Promise(resolve => setTimeout(resolve, 2000));
            
            AppState.sshConnection = { host, port, user };
            AppState.isConnected = true;
            
            Utils.updateStatus('ssh-status', true);
            document.getElementById('ssh-content').classList.remove('hidden');
            document.getElementById('ssh-content').classList.add('fade-in');
            
            // Cargar lista de archivos inicial
            this.loadFileList();
            
            alert('Conexión SSH establecida correctamente');
        } catch (error) {
            alert('Error al conectar SSH: ' + error.message);
        } finally {
            Utils.hideLoading('ssh-btn-text', 'ssh-loading');
        }
    },

    loadFileList() {
        // Simular lista de archivos (en implementación real sería via SSH)
        const mockFiles = [
            { name: '..', type: 'directory', size: 0 },
            { name: 'Downloads', type: 'directory', size: 0 },
            { name: 'Documents', type: 'directory', size: 0 },
            { name: 'Videos', type: 'directory', size: 0 },
            { name: 'movie1.mp4', type: 'file', size: 1024000000 },
            { name: 'series_s01e01.mkv', type: 'file', size: 2048000000 },
            { name: 'config.txt', type: 'file', size: 1024 }
        ];
        
        this.renderFileList(mockFiles);
    },

    renderFileList(files) {
        const container = document.getElementById('file-list');
        container.innerHTML = '';
        
        files.forEach(file => {
            const div = document.createElement('div');
            div.className = 'content-item';
            div.onclick = () => this.selectFile(file);
            
            const icon = file.type === 'directory' ? '📁' : '📄';
            const size = file.type === 'directory' ? '' : Utils.formatFileSize(file.size);
            
            div.innerHTML = `
                <div class="content-title">${icon} ${file.name}</div>
                <div class="content-meta">
                    <span>${file.type}</span>
                    <span>${size}</span>
                </div>
            `;
            
            container.appendChild(div);
        });
    },

    selectFile(file) {
        if (file.type === 'directory') {
            if (file.name === '..') {
                // Navegar hacia arriba
                const pathParts = AppState.currentPath.split('/').filter(p => p);
                pathParts.pop();
                AppState.currentPath = '/' + pathParts.join('/');
            } else {
                // Entrar al directorio
                AppState.currentPath += '/' + file.name;
            }
            
            document.getElementById('current-path').textContent = AppState.currentPath;
            this.loadFileList(); // Recargar lista
        } else {
            // Seleccionar archivo para descarga
            alert(`Archivo seleccionado: ${file.name}`);
        }
    }
};

// Manejador de descargas
const DownloadManager = {
    addToQueue(item) {
        const download = {
            id: Date.now(),
            title: item.title,
            url: item.url,
            type: item.type,
            status: 'pending',
            progress: 0,
            size: 0,
            downloaded: 0
        };
        
        AppState.downloadQueue.push(download);
        this.updateUI();
        Utils.updateStatus('download-status', true);
    },

    updateUI() {
        const queue = AppState.downloadQueue;
        
        // Actualizar estadísticas
        const total = queue.length;
        const active = queue.filter(d => d.status === 'downloading').length;
        const completed = queue.filter(d => d.status === 'completed').length;
        const failed = queue.filter(d => d.status === 'failed').length;
        
        document.getElementById('queue-total').textContent = total;
        document.getElementById('queue-active').textContent = active;
        document.getElementById('queue-completed').textContent = completed;
        document.getElementById('queue-failed').textContent = failed;
        
        // Renderizar lista
        this.renderDownloadList();
    },

    renderDownloadList() {
        const container = document.getElementById('download-list');
        
        if (AppState.downloadQueue.length === 0) {
            container.innerHTML = `
                <div style="text-align: center; color: #bdc3c7; margin-top: 50px;">
                    No hay descargas en la cola.<br>
                    Selecciona contenido desde el panel M3U para comenzar.
                </div>
            `;
            return;
        }
        
        container.innerHTML = '';
        
        AppState.downloadQueue.forEach(download => {
            const div = document.createElement('div');
            div.className = 'content-item';
            
            const statusIcon = {
                'pending': '⏳',
                'downloading': '📥',
                'completed': '✅',
                'failed': '❌',
                'paused': '⏸️'
            }[download.status];
            
            div.innerHTML = `
                <div class="content-title">${statusIcon} ${download.title}</div>
                <div class="content-meta">
                    <span class="badge ${download.type}">${download.type.toUpperCase()}</span>
                    <span>${download.progress}%</span>
                </div>
                <div class="progress-bar">
                    <div class="progress-fill" style="width: ${download.progress}%"></div>
                </div>
            `;
            
            container.appendChild(div);
        });
    },

    start() {
        AppState.downloadQueue.forEach(download => {
            if (download.status === 'pending') {
                download.status = 'downloading';
                this.simulateDownload(download);
            }
        });
        this.updateUI();
    },

    pause() {
        AppState.downloadQueue.forEach(download => {
            if (download.status === 'downloading') {
                download.status = 'paused';
            }
        });
        this.updateUI();
    },

    clearCompleted() {
        AppState.downloadQueue = AppState.downloadQueue.filter(d => d.status !== 'completed');
        this.updateUI();
    },

    simulateDownload(download) {
        const interval = setInterval(() => {
            if (download.status !== 'downloading') {
                clearInterval(interval);
                return;
            }
            
            download.progress += Math.random() * 10;
            if (download.progress >= 100) {
                download.progress = 100;
                download.status = 'completed';
                clearInterval(interval);
            }
            
            this.updateUI();
        }, 500);
    }
};

// Event Listeners
document.addEventListener('DOMContentLoaded', () => {
    // Drag & Drop para M3U
    const dropZone = document.getElementById('m3u-drop-zone');
    
    dropZone.addEventListener('dragover', (e) => {
        e.preventDefault();
        dropZone.classList.add('dragover');
    });
    
    dropZone.addEventListener('dragleave', () => {
        dropZone.classList.remove('dragover');
    });
    
    dropZone.addEventListener('drop', (e) => {
        e.preventDefault();
        dropZone.classList.remove('dragover');
        
        const files = e.dataTransfer.files;
        if (files.length > 0) {
            M3UHandler.loadFile(files[0]);
        }
    });
    
    // Input de archivo
    document.getElementById('file-input').addEventListener('change', (e) => {
        if (e.target.files.length > 0) {
            M3UHandler.loadFile(e.target.files[0]);
        }
    });
});

// Funciones globales para los botones
function selectM3UFile() {
    document.getElementById('file-input').click();
}

function loadM3UFromURL() {
    const url = document.getElementById('m3u-url').value;
    if (url) {
        M3UHandler.loadFromURL(url);
    } else {
        alert('Por favor ingresa una URL válida');
    }
}

function connectSSH() {
    SSHHandler.connect();
}

function startDownloads() {
    DownloadManager.start();
}

function pauseDownloads() {
    DownloadManager.pause();
}

function clearCompleted() {
    DownloadManager.clearCompleted();
}
