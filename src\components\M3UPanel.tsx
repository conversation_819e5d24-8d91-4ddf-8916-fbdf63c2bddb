import React, { useState, useCallback } from 'react';
import styled from 'styled-components';
import { M3UPlaylist, M3UEntry, SeriesInfo } from '../types';
import { M3UParser } from '../services/M3UParser';

declare global {
  interface Window {
    require: any;
  }
}

const { ipc<PERSON>enderer } = window.require('electron');

interface M3UPanelProps {
  playlists: M3UPlaylist[];
  currentPlaylist?: string;
  onPlaylistUpdate: (playlist: M3UPlaylist) => void;
  onDownloadRequest: (entries: M3UEntry[]) => void;
}

const PanelContainer = styled.div`
  height: 100%;
  background: #252525;
  border-right: 1px solid #404040;
  display: flex;
  flex-direction: column;
`;

const Header = styled.div`
  padding: 15px;
  background: #2d2d2d;
  border-bottom: 1px solid #404040;
`;

const Title = styled.h2`
  font-size: 16px;
  font-weight: 600;
  margin: 0 0 10px 0;
  color: #ffffff;
`;

const ButtonGroup = styled.div`
  display: flex;
  gap: 10px;
`;

const Button = styled.button`
  padding: 8px 16px;
  background: #0078d4;
  color: white;
  border: none;
  border-radius: 4px;
  cursor: pointer;
  font-size: 12px;
  transition: background-color 0.2s;

  &:hover {
    background: #106ebe;
  }

  &:disabled {
    background: #404040;
    cursor: not-allowed;
  }
`;

const Content = styled.div`
  flex: 1;
  overflow: hidden;
  display: flex;
  flex-direction: column;
`;

const PlaylistSelector = styled.select`
  width: 100%;
  padding: 8px;
  margin-bottom: 10px;
  background: #404040;
  color: white;
  border: 1px solid #606060;
  border-radius: 4px;
  font-size: 12px;

  option {
    background: #404040;
    color: white;
  }
`;

const FilterContainer = styled.div`
  padding: 10px 15px;
  background: #2a2a2a;
  border-bottom: 1px solid #404040;
`;

const FilterInput = styled.input`
  width: 100%;
  padding: 8px;
  background: #404040;
  color: white;
  border: 1px solid #606060;
  border-radius: 4px;
  font-size: 12px;

  &::placeholder {
    color: #888;
  }
`;

const CategoryTabs = styled.div`
  display: flex;
  background: #2a2a2a;
  border-bottom: 1px solid #404040;
`;

const CategoryTab = styled.button<{ active: boolean }>`
  flex: 1;
  padding: 10px;
  background: ${props => props.active ? '#0078d4' : 'transparent'};
  color: ${props => props.active ? 'white' : '#ccc'};
  border: none;
  cursor: pointer;
  font-size: 12px;
  transition: all 0.2s;

  &:hover {
    background: ${props => props.active ? '#106ebe' : '#404040'};
  }
`;

const EntryList = styled.div`
  flex: 1;
  overflow-y: auto;
  padding: 10px;
`;

const EntryItem = styled.div<{ selected: boolean; indent?: number }>`
  padding: 8px;
  padding-left: ${props => 8 + (props.indent || 0) * 20}px;
  margin-bottom: 4px;
  background: ${props => props.selected ? '#0078d4' : '#353535'};
  border-radius: 4px;
  cursor: pointer;
  transition: background-color 0.2s;
  font-size: 12px;
  line-height: 1.4;

  &:hover {
    background: ${props => props.selected ? '#106ebe' : '#404040'};
  }
`;

const SeriesGroup = styled.div<{ expanded: boolean }>`
  margin-bottom: 4px;
`;

const SeriesHeader = styled.div<{ selected: boolean }>`
  padding: 8px;
  background: ${props => props.selected ? '#0078d4' : '#404040'};
  border-radius: 4px;
  cursor: pointer;
  transition: background-color 0.2s;
  font-size: 12px;
  line-height: 1.4;
  display: flex;
  align-items: center;

  &:hover {
    background: ${props => props.selected ? '#106ebe' : '#505050'};
  }
`;

const ExpandIcon = styled.span<{ expanded: boolean }>`
  margin-right: 8px;
  transition: transform 0.2s;
  transform: ${props => props.expanded ? 'rotate(90deg)' : 'rotate(0deg)'};
`;

const EntryTitle = styled.div`
  font-weight: 500;
  color: #ffffff;
  margin-bottom: 2px;
`;

const EntryInfo = styled.div`
  color: #ccc;
  font-size: 11px;
`;

const SeriesTitle = styled.div`
  font-weight: 600;
  color: #ffffff;
`;

const SeriesInfo = styled.div`
  color: #ccc;
  font-size: 11px;
  margin-top: 2px;
`;

const M3UPanel: React.FC<M3UPanelProps> = ({
  playlists,
  currentPlaylist,
  onPlaylistUpdate,
  onDownloadRequest,
}) => {
  const [selectedEntries, setSelectedEntries] = useState<Set<string>>(new Set());
  const [filter, setFilter] = useState('');
  const [activeCategory, setActiveCategory] = useState<'all' | 'movies' | 'series' | 'live'>('all');
  const [expandedSeries, setExpandedSeries] = useState<Set<string>>(new Set());

  const currentPlaylistData = playlists.find(p => p.id === currentPlaylist);

  const handleLoadM3U = useCallback(async () => {
    try {
      const result = await ipcRenderer.invoke('select-m3u-file');
      if (result.success) {
        const playlist = M3UParser.parseM3U(result.content, 
          result.filePath.split('\\').pop() || 'Playlist', 
          result.filePath
        );
        onPlaylistUpdate(playlist);
      }
    } catch (error) {
      console.error('Error loading M3U file:', error);
    }
  }, [onPlaylistUpdate]);

  const handleLoadFromURL = useCallback(() => {
    const url = prompt('Ingrese la URL de la lista M3U:');
    if (url) {
      fetch(url)
        .then(response => response.text())
        .then(content => {
          const playlist = M3UParser.parseM3U(content, 'URL Playlist', url);
          onPlaylistUpdate(playlist);
        })
        .catch(error => {
          console.error('Error loading M3U from URL:', error);
          alert('Error al cargar la lista M3U desde la URL');
        });
    }
  }, [onPlaylistUpdate]);

  const filteredEntries = currentPlaylistData?.entries.filter(entry => {
    const matchesFilter = entry.title.toLowerCase().includes(filter.toLowerCase()) ||
                         entry.group.toLowerCase().includes(filter.toLowerCase());

    const matchesCategory = activeCategory === 'all' || entry.type === activeCategory;

    return matchesFilter && matchesCategory;
  }) || [];

  // Agrupar series para vista de árbol
  const groupedSeries = React.useMemo(() => {
    if (activeCategory !== 'series') return new Map();

    const seriesMap = new Map<string, M3UEntry[]>();

    filteredEntries
      .filter(entry => entry.type === 'series')
      .forEach(entry => {
        const seriesTitle = M3UParser.extractSeriesTitle ?
          M3UParser.extractSeriesTitle(entry.title) :
          entry.title.replace(/[Ss]\d+[Ee]\d+.*$/, '').trim();

        if (!seriesMap.has(seriesTitle)) {
          seriesMap.set(seriesTitle, []);
        }
        seriesMap.get(seriesTitle)!.push(entry);
      });

    // Ordenar episodios dentro de cada serie
    seriesMap.forEach(episodes => {
      episodes.sort((a, b) => {
        const aS = a.metadata?.season || 1;
        const bS = b.metadata?.season || 1;
        const aE = a.metadata?.episode || 1;
        const bE = b.metadata?.episode || 1;

        if (aS !== bS) return aS - bS;
        return aE - bE;
      });
    });

    return seriesMap;
  }, [filteredEntries, activeCategory]);

  const handleEntryClick = (entryId: string, ctrlKey: boolean) => {
    if (ctrlKey) {
      const newSelected = new Set(selectedEntries);
      if (newSelected.has(entryId)) {
        newSelected.delete(entryId);
      } else {
        newSelected.add(entryId);
      }
      setSelectedEntries(newSelected);
    } else {
      setSelectedEntries(new Set([entryId]));
    }
  };

  const handleSeriesClick = (seriesTitle: string, episodes: M3UEntry[]) => {
    const episodeIds = episodes.map(e => e.id);
    setSelectedEntries(new Set(episodeIds));
  };

  const toggleSeriesExpansion = (seriesTitle: string) => {
    const newExpanded = new Set(expandedSeries);
    if (newExpanded.has(seriesTitle)) {
      newExpanded.delete(seriesTitle);
    } else {
      newExpanded.add(seriesTitle);
    }
    setExpandedSeries(newExpanded);
  };

  const handleContextMenu = (e: React.MouseEvent, entryId?: string, seriesTitle?: string, episodes?: M3UEntry[]) => {
    e.preventDefault();

    if (seriesTitle && episodes) {
      // Menú contextual para serie completa
      const episodeIds = episodes.map(ep => ep.id);
      setSelectedEntries(new Set(episodeIds));
      console.log('Context menu for series:', seriesTitle, episodes.length, 'episodes');
    } else if (entryId) {
      // Menú contextual para entrada individual
      if (!selectedEntries.has(entryId)) {
        setSelectedEntries(new Set([entryId]));
      }
      console.log('Context menu for entries:', Array.from(selectedEntries));
    }
  };

  const getCategoryCount = (category: 'all' | 'movies' | 'series' | 'live') => {
    if (!currentPlaylistData) return 0;
    if (category === 'all') return currentPlaylistData.entries.length;
    return currentPlaylistData.entries.filter(e => e.type === category).length;
  };

  return (
    <PanelContainer>
      <Header>
        <Title>Listas M3U</Title>
        <ButtonGroup>
          <Button onClick={handleLoadM3U}>Cargar Archivo</Button>
          <Button onClick={handleLoadFromURL}>Cargar URL</Button>
        </ButtonGroup>
      </Header>

      <Content>
        {playlists.length > 0 && (
          <div style={{ padding: '10px 15px' }}>
            <PlaylistSelector
              value={currentPlaylist || ''}
              onChange={(e) => {
                // Cambiar playlist actual
                console.log('Change playlist to:', e.target.value);
              }}
            >
              <option value="">Seleccionar playlist...</option>
              {playlists.map(playlist => (
                <option key={playlist.id} value={playlist.id}>
                  {playlist.name} ({playlist.entries.length} elementos)
                </option>
              ))}
            </PlaylistSelector>
          </div>
        )}

        {currentPlaylistData && (
          <>
            <FilterContainer>
              <FilterInput
                type="text"
                placeholder="Buscar contenido..."
                value={filter}
                onChange={(e) => setFilter(e.target.value)}
              />
            </FilterContainer>

            <CategoryTabs>
              <CategoryTab
                active={activeCategory === 'all'}
                onClick={() => setActiveCategory('all')}
              >
                Todos ({getCategoryCount('all')})
              </CategoryTab>
              <CategoryTab
                active={activeCategory === 'movies'}
                onClick={() => setActiveCategory('movies')}
              >
                Películas ({getCategoryCount('movies')})
              </CategoryTab>
              <CategoryTab
                active={activeCategory === 'series'}
                onClick={() => setActiveCategory('series')}
              >
                Series ({getCategoryCount('series')})
              </CategoryTab>
              <CategoryTab
                active={activeCategory === 'live'}
                onClick={() => setActiveCategory('live')}
              >
                En Vivo ({getCategoryCount('live')})
              </CategoryTab>
            </CategoryTabs>

            <EntryList>
              {activeCategory === 'series' ? (
                // Vista de árbol para series
                Array.from(groupedSeries.entries()).map(([seriesTitle, episodes]) => (
                  <SeriesGroup key={seriesTitle} expanded={expandedSeries.has(seriesTitle)}>
                    <SeriesHeader
                      selected={episodes.every(ep => selectedEntries.has(ep.id))}
                      onClick={() => handleSeriesClick(seriesTitle, episodes)}
                      onContextMenu={(e) => handleContextMenu(e, undefined, seriesTitle, episodes)}
                    >
                      <ExpandIcon
                        expanded={expandedSeries.has(seriesTitle)}
                        onClick={(e) => {
                          e.stopPropagation();
                          toggleSeriesExpansion(seriesTitle);
                        }}
                      >
                        ▶
                      </ExpandIcon>
                      <div>
                        <SeriesTitle>{seriesTitle}</SeriesTitle>
                        <SeriesInfo>{episodes.length} episodios</SeriesInfo>
                      </div>
                    </SeriesHeader>
                    {expandedSeries.has(seriesTitle) && episodes.map(episode => (
                      <EntryItem
                        key={episode.id}
                        selected={selectedEntries.has(episode.id)}
                        indent={1}
                        onClick={(e) => handleEntryClick(episode.id, e.ctrlKey)}
                        onContextMenu={(e) => handleContextMenu(e, episode.id)}
                      >
                        <EntryTitle>{episode.title}</EntryTitle>
                        <EntryInfo>
                          {episode.group} • {episode.type}
                          {episode.metadata?.season && episode.metadata?.episode &&
                            ` • S${episode.metadata.season}E${episode.metadata.episode}`
                          }
                        </EntryInfo>
                      </EntryItem>
                    ))}
                  </SeriesGroup>
                ))
              ) : (
                // Vista normal para otros tipos de contenido
                filteredEntries.map(entry => (
                  <EntryItem
                    key={entry.id}
                    selected={selectedEntries.has(entry.id)}
                    onClick={(e) => handleEntryClick(entry.id, e.ctrlKey)}
                    onContextMenu={(e) => handleContextMenu(e, entry.id)}
                  >
                    <EntryTitle>{entry.title}</EntryTitle>
                    <EntryInfo>
                      {entry.group} • {entry.type}
                      {entry.metadata?.season && entry.metadata?.episode &&
                        ` • S${entry.metadata.season}E${entry.metadata.episode}`
                      }
                    </EntryInfo>
                  </EntryItem>
                ))
              )}
            </EntryList>
          </>
        )}
      </Content>
    </PanelContainer>
  );
};

export default M3UPanel;
