import React, { useState } from 'react';
import styled from 'styled-components';
import { SSHConnection } from '../types';

const Container = styled.div`
  height: 100%;
  background: #2d2d2d;
  border-right: 1px solid #404040;
  display: flex;
  flex-direction: column;
`;

const Header = styled.div`
  padding: 15px;
  background: #3d3d3d;
  border-bottom: 1px solid #404040;
`;

const Title = styled.h3`
  margin: 0;
  color: #ffffff;
  font-size: 16px;
`;

const Content = styled.div`
  flex: 1;
  padding: 15px;
  overflow-y: auto;
`;

const Form = styled.div`
  display: flex;
  flex-direction: column;
  gap: 10px;
  margin-bottom: 20px;
`;

const Input = styled.input`
  padding: 8px;
  border: 1px solid #555;
  border-radius: 4px;
  background: #404040;
  color: white;
  
  &::placeholder {
    color: #aaa;
  }
`;

const Button = styled.button`
  background: #0078d4;
  color: white;
  border: none;
  padding: 10px 15px;
  border-radius: 4px;
  cursor: pointer;
  
  &:hover {
    background: #106ebe;
  }
  
  &:disabled {
    background: #555;
    cursor: not-allowed;
  }
`;

const ConnectionItem = styled.div`
  background: #404040;
  padding: 10px;
  margin: 5px 0;
  border-radius: 4px;
  cursor: pointer;
  
  &:hover {
    background: #505050;
  }
`;

const StatusIndicator = styled.span<{ connected: boolean }>`
  display: inline-block;
  width: 8px;
  height: 8px;
  border-radius: 50%;
  background: ${props => props.connected ? '#00ff00' : '#ff0000'};
  margin-right: 8px;
`;

const FolderItem = styled.div`
  padding: 5px 10px;
  cursor: pointer;
  color: #ccc;
  
  &:hover {
    background: #404040;
    color: white;
  }
`;

interface Props {
  connections: SSHConnection[];
  currentConnection?: string;
  selectedDestination?: string;
  onConnectionUpdate: (connection: SSHConnection) => void;
  onDestinationSelect: (path: string) => void;
}

const SFTPPanelSimple: React.FC<Props> = ({ 
  connections, 
  currentConnection, 
  selectedDestination,
  onConnectionUpdate, 
  onDestinationSelect 
}) => {
  const [formData, setFormData] = useState({
    name: '',
    host: '',
    port: '22',
    username: '',
    password: ''
  });

  const [mockFolders] = useState([
    '/home/<USER>/downloads',
    '/home/<USER>/movies',
    '/home/<USER>/series',
    '/var/media',
    '/tmp'
  ]);

  const handleInputChange = (field: string, value: string) => {
    setFormData(prev => ({ ...prev, [field]: value }));
  };

  const handleConnect = () => {
    if (!formData.name || !formData.host || !formData.username) {
      alert('Por favor completa todos los campos requeridos');
      return;
    }

    const newConnection: SSHConnection = {
      id: Date.now().toString(),
      name: formData.name,
      host: formData.host,
      port: parseInt(formData.port),
      username: formData.username,
      password: formData.password,
      isConnected: true,
      currentPath: '/home/' + formData.username
    };

    onConnectionUpdate(newConnection);
    
    // Limpiar formulario
    setFormData({
      name: '',
      host: '',
      port: '22',
      username: '',
      password: ''
    });
  };

  const handleFolderSelect = (path: string) => {
    onDestinationSelect(path);
  };

  const currentConn = connections.find(c => c.id === currentConnection);

  return (
    <Container>
      <Header>
        <Title>🔐 Panel SSH/SFTP</Title>
      </Header>
      <Content>
        <div>
          <h4 style={{ color: 'white', marginBottom: '10px' }}>Nueva Conexión SSH</h4>
          <Form>
            <Input
              type="text"
              placeholder="Nombre de la conexión"
              value={formData.name}
              onChange={(e) => handleInputChange('name', e.target.value)}
            />
            <Input
              type="text"
              placeholder="Host/IP"
              value={formData.host}
              onChange={(e) => handleInputChange('host', e.target.value)}
            />
            <Input
              type="number"
              placeholder="Puerto"
              value={formData.port}
              onChange={(e) => handleInputChange('port', e.target.value)}
            />
            <Input
              type="text"
              placeholder="Usuario"
              value={formData.username}
              onChange={(e) => handleInputChange('username', e.target.value)}
            />
            <Input
              type="password"
              placeholder="Contraseña"
              value={formData.password}
              onChange={(e) => handleInputChange('password', e.target.value)}
            />
            <Button onClick={handleConnect}>
              🔗 Conectar
            </Button>
          </Form>
        </div>

        <div>
          <h4 style={{ color: 'white', marginBottom: '10px' }}>
            Conexiones ({connections.length})
          </h4>
          {connections.map(conn => (
            <ConnectionItem key={conn.id}>
              <div style={{ color: 'white', fontWeight: 'bold' }}>
                <StatusIndicator connected={conn.isConnected} />
                {conn.name}
              </div>
              <div style={{ fontSize: '12px', color: '#ccc', marginTop: '5px' }}>
                {conn.username}@{conn.host}:{conn.port}
              </div>
            </ConnectionItem>
          ))}
        </div>

        {currentConn && (
          <div style={{ marginTop: '20px' }}>
            <h4 style={{ color: 'white', marginBottom: '10px' }}>
              📁 Explorador de Archivos
            </h4>
            <div style={{ background: '#404040', borderRadius: '4px', padding: '10px' }}>
              <div style={{ color: '#ccc', fontSize: '12px', marginBottom: '10px' }}>
                Conectado a: {currentConn.name}
              </div>
              {mockFolders.map(folder => (
                <FolderItem 
                  key={folder}
                  onClick={() => handleFolderSelect(folder)}
                  style={{ 
                    background: selectedDestination === folder ? '#0078d4' : 'transparent',
                    color: selectedDestination === folder ? 'white' : '#ccc'
                  }}
                >
                  📁 {folder}
                </FolderItem>
              ))}
            </div>
            {selectedDestination && (
              <div style={{ 
                marginTop: '10px', 
                padding: '10px', 
                background: '#0078d4', 
                borderRadius: '4px',
                color: 'white',
                fontSize: '12px'
              }}>
                📍 Destino seleccionado: {selectedDestination}
              </div>
            )}
          </div>
        )}

        {connections.length === 0 && (
          <div style={{ 
            color: '#888', 
            textAlign: 'center', 
            marginTop: '50px',
            fontStyle: 'italic'
          }}>
            No hay conexiones SSH configuradas.<br />
            Crea una nueva conexión para comenzar.
          </div>
        )}
      </Content>
    </Container>
  );
};

export default SFTPPanelSimple;
