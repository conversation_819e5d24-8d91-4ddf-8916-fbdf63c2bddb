import React, { useState, useEffect } from 'react';
import styled from 'styled-components';
import { SSHConnection, RemoteDirectory, RemoteFile } from '../types';
import { SimpleSSHService } from '../services/SimpleSSHService';

const Container = styled.div`
  height: 100%;
  background: #2d2d2d;
  border-right: 1px solid #404040;
  display: flex;
  flex-direction: column;
`;

const Header = styled.div`
  padding: 15px;
  background: #3d3d3d;
  border-bottom: 1px solid #404040;
`;

const Title = styled.h3`
  margin: 0;
  color: #ffffff;
  font-size: 16px;
`;

const Content = styled.div`
  flex: 1;
  padding: 15px;
  overflow-y: auto;
`;

const Form = styled.div`
  display: flex;
  flex-direction: column;
  gap: 10px;
  margin-bottom: 20px;
`;

const Input = styled.input`
  padding: 8px;
  border: 1px solid #555;
  border-radius: 4px;
  background: #404040;
  color: white;
  
  &::placeholder {
    color: #aaa;
  }
`;

const Button = styled.button`
  background: #0078d4;
  color: white;
  border: none;
  padding: 10px 15px;
  border-radius: 4px;
  cursor: pointer;
  
  &:hover {
    background: #106ebe;
  }
  
  &:disabled {
    background: #555;
    cursor: not-allowed;
  }
`;

const ConnectionItem = styled.div`
  background: #404040;
  padding: 10px;
  margin: 5px 0;
  border-radius: 4px;
  cursor: pointer;

  &:hover {
    background: #505050;
  }
`;

const FileExplorer = styled.div`
  margin-top: 15px;
  border: 1px solid #555;
  border-radius: 4px;
  background: #2d2d2d;
`;

const PathBar = styled.div`
  background: #404040;
  padding: 8px 12px;
  border-bottom: 1px solid #555;
  color: #ccc;
  font-size: 12px;
  font-family: monospace;
`;

const FileList = styled.div`
  max-height: 300px;
  overflow-y: auto;
`;

const FileItem = styled.div`
  padding: 8px 12px;
  border-bottom: 1px solid #333;
  cursor: pointer;
  display: flex;
  align-items: center;

  &:hover {
    background: #404040;
  }

  &:last-child {
    border-bottom: none;
  }
`;

const FileIcon = styled.span`
  margin-right: 8px;
  width: 16px;
  text-align: center;
`;

const FileName = styled.span`
  flex: 1;
  color: white;
  font-size: 13px;
`;

const FileSize = styled.span`
  color: #888;
  font-size: 11px;
  margin-left: 8px;
`;

const StatusBar = styled.div`
  background: #353535;
  padding: 6px 12px;
  border-top: 1px solid #555;
  color: #ccc;
  font-size: 11px;
  display: flex;
  justify-content: space-between;
`;

const StatusIndicator = styled.span<{ connected: boolean }>`
  display: inline-block;
  width: 8px;
  height: 8px;
  border-radius: 50%;
  background: ${props => props.connected ? '#00ff00' : '#ff0000'};
  margin-right: 8px;
`;

const FolderItem = styled.div`
  padding: 5px 10px;
  cursor: pointer;
  color: #ccc;
  
  &:hover {
    background: #404040;
    color: white;
  }
`;

interface Props {
  connections: SSHConnection[];
  currentConnection?: string;
  selectedDestination?: string;
  onConnectionUpdate: (connection: SSHConnection) => void;
  onDestinationSelect: (path: string) => void;
}

const SFTPPanelSimple: React.FC<Props> = ({ 
  connections, 
  currentConnection, 
  selectedDestination,
  onConnectionUpdate, 
  onDestinationSelect 
}) => {
  const [formData, setFormData] = useState({
    name: '',
    host: '',
    port: '22',
    username: '',
    password: ''
  });
  const [currentDirectory, setCurrentDirectory] = useState<RemoteDirectory | null>(null);
  const [isConnecting, setIsConnecting] = useState(false);
  const [sshService] = useState(() => new SimpleSSHService());

  // Mock folders removido - ahora usamos el explorador real

  const handleInputChange = (field: string, value: string) => {
    setFormData(prev => ({ ...prev, [field]: value }));
  };

  const handleConnect = async () => {
    if (!formData.name || !formData.host || !formData.username) {
      alert('Por favor completa todos los campos requeridos');
      return;
    }

    setIsConnecting(true);

    try {
      const newConnection: SSHConnection = {
        id: Date.now().toString(),
        name: formData.name || `${formData.username}@${formData.host}`,
        host: formData.host,
        port: parseInt(formData.port),
        username: formData.username,
        password: formData.password,
        isConnected: false,
        currentPath: `/home/<USER>
      };

      // Intentar conectar usando el servicio SSH
      const connected = await sshService.connect(newConnection);

      if (connected) {
        newConnection.isConnected = true;
        onConnectionUpdate(newConnection);

        // Cargar directorio inicial
        await loadDirectory(newConnection.id, newConnection.currentPath);

        // Limpiar formulario
        setFormData({
          name: '',
          host: '',
          port: '22',
          username: '',
          password: ''
        });
      }
    } catch (error) {
      console.error('❌ Error conectando:', error);
      alert(`Error de conexión: ${error}`);
    } finally {
      setIsConnecting(false);
    }
  };

  const loadDirectory = async (connectionId: string, path: string) => {
    try {
      const directory = await sshService.listFiles(connectionId, path);
      setCurrentDirectory(directory);
    } catch (error) {
      console.error('❌ Error cargando directorio:', error);
      alert(`Error cargando directorio: ${error}`);
    }
  };

  const handleFileClick = async (file: RemoteFile) => {
    if (!currentConnection) return;

    if (file.type === 'directory') {
      await loadDirectory(currentConnection, file.path);
    } else {
      // Seleccionar archivo como destino
      onDestinationSelect(file.path);
    }
  };

  const handleDisconnect = async () => {
    if (currentConnection) {
      await sshService.disconnect(currentConnection);
      setCurrentDirectory(null);
      // Actualizar conexión como desconectada
      const disconnectedConnection = connections.find(c => c.id === currentConnection);
      if (disconnectedConnection) {
        onConnectionUpdate({
          ...disconnectedConnection,
          isConnected: false
        });
      }
    }
  };

  // handleFolderSelect removido - ahora usamos handleFileClick

  const currentConn = connections.find(c => c.id === currentConnection);

  const formatFileSize = (bytes: number): string => {
    if (bytes === 0) return '-';
    const k = 1024;
    const sizes = ['B', 'KB', 'MB', 'GB'];
    const i = Math.floor(Math.log(bytes) / Math.log(k));
    return parseFloat((bytes / Math.pow(k, i)).toFixed(1)) + ' ' + sizes[i];
  };

  const getFileIcon = (file: RemoteFile): string => {
    if (file.name === '..') return '⬆️';
    if (file.type === 'directory') return '📁';

    const ext = file.name.split('.').pop()?.toLowerCase();
    switch (ext) {
      case 'mp4':
      case 'avi':
      case 'mkv':
      case 'mov':
        return '🎬';
      case 'mp3':
      case 'wav':
      case 'flac':
        return '🎵';
      case 'jpg':
      case 'png':
      case 'gif':
        return '🖼️';
      case 'txt':
      case 'log':
        return '📄';
      default:
        return '📄';
    }
  };

  return (
    <Container>
      <Header>
        <Title>🔐 Panel SSH/SFTP</Title>
      </Header>
      <Content>
        <div>
          <h4 style={{ color: 'white', marginBottom: '10px' }}>Nueva Conexión SSH</h4>
          <Form>
            <Input
              type="text"
              placeholder="Nombre de la conexión"
              value={formData.name}
              onChange={(e) => handleInputChange('name', e.target.value)}
            />
            <Input
              type="text"
              placeholder="Host/IP"
              value={formData.host}
              onChange={(e) => handleInputChange('host', e.target.value)}
            />
            <Input
              type="number"
              placeholder="Puerto"
              value={formData.port}
              onChange={(e) => handleInputChange('port', e.target.value)}
            />
            <Input
              type="text"
              placeholder="Usuario"
              value={formData.username}
              onChange={(e) => handleInputChange('username', e.target.value)}
            />
            <Input
              type="password"
              placeholder="Contraseña"
              value={formData.password}
              onChange={(e) => handleInputChange('password', e.target.value)}
            />
            <Button
              onClick={handleConnect}
              disabled={!formData.host || !formData.username || isConnecting}
            >
              {isConnecting ? '🔄 Conectando...' : '🔗 Conectar'}
            </Button>
          </Form>
        </div>

        <div>
          <h4 style={{ color: 'white', marginBottom: '10px' }}>
            Conexiones ({connections.length})
          </h4>
          {connections.map(conn => (
            <ConnectionItem key={conn.id}>
              <div style={{ color: 'white', fontWeight: 'bold' }}>
                <StatusIndicator connected={conn.isConnected} />
                {conn.name}
              </div>
              <div style={{ fontSize: '12px', color: '#ccc', marginTop: '5px' }}>
                {conn.username}@{conn.host}:{conn.port}
              </div>
            </ConnectionItem>
          ))}
        </div>

        {currentConn && currentConn.isConnected && (
          <div style={{ marginTop: '20px' }}>
            <div style={{ display: 'flex', justifyContent: 'space-between', alignItems: 'center', marginBottom: '10px' }}>
              <h4 style={{ color: 'white', margin: 0 }}>
                📁 Explorador de Archivos
              </h4>
              <Button onClick={handleDisconnect} style={{ fontSize: '11px', padding: '4px 8px' }}>
                🔌 Desconectar
              </Button>
            </div>

            <FileExplorer>
              {currentDirectory && (
                <>
                  <PathBar>
                    📍 {currentDirectory.path}
                  </PathBar>

                  <FileList>
                    {currentDirectory.files.map((file, index) => (
                      <FileItem
                        key={`${file.path}-${index}`}
                        onClick={() => handleFileClick(file)}
                        style={{
                          background: selectedDestination === file.path ? '#0078d4' : 'transparent'
                        }}
                      >
                        <FileIcon>{getFileIcon(file)}</FileIcon>
                        <FileName>{file.name}</FileName>
                        <FileSize>{formatFileSize(file.size)}</FileSize>
                      </FileItem>
                    ))}
                  </FileList>

                  <StatusBar>
                    <span>{currentDirectory.files.length} elementos</span>
                    <span>Conectado a {currentConn.name}</span>
                  </StatusBar>
                </>
              )}
            </FileExplorer>

            {selectedDestination && (
              <div style={{
                marginTop: '10px',
                padding: '10px',
                background: '#0078d4',
                borderRadius: '4px',
                color: 'white',
                fontSize: '12px'
              }}>
                ✅ Destino seleccionado: {selectedDestination}
              </div>
            )}
          </div>
        )}

        {connections.length === 0 && (
          <div style={{ 
            color: '#888', 
            textAlign: 'center', 
            marginTop: '50px',
            fontStyle: 'italic'
          }}>
            No hay conexiones SSH configuradas.<br />
            Crea una nueva conexión para comenzar.
          </div>
        )}
      </Content>
    </Container>
  );
};

export default SFTPPanelSimple;
