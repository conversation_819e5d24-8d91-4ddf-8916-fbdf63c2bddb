import { NodeSSH } from 'node-ssh';
import * as SftpClient from 'ssh2-sftp-client';
import { SSHConnection, RemoteFile, RemoteDirectory } from '../types';

export class SSHService {
  private ssh: NodeSSH;
  private sftp: SftpClient;
  private connection: SSHConnection | null = null;

  constructor() {
    this.ssh = new NodeSSH();
    this.sftp = new SftpClient();
  }

  async connect(connectionConfig: Omit<SSHConnection, 'id' | 'isConnected' | 'currentPath'>): Promise<SSHConnection> {
    try {
      const config: any = {
        host: connectionConfig.host,
        port: connectionConfig.port,
        username: connectionConfig.username,
      };

      if (connectionConfig.password) {
        config.password = connectionConfig.password;
      }

      if (connectionConfig.privateKey) {
        config.privateKey = connectionConfig.privateKey;
      }

      // Conectar SSH
      await this.ssh.connect(config);

      // Conectar SFTP
      await this.sftp.connect(config);

      // Determinar directorio inicial para servidores Ubuntu Linux
      let initialPath = '/';
      if (connectionConfig.username === 'root') {
        initialPath = '/root';
      } else {
        initialPath = `/home/<USER>
      }

      // Verificar si el directorio existe
      try {
        await this.sftp.list(initialPath);
      } catch {
        // Si no existe, usar directorio raíz
        initialPath = '/';
      }

      this.connection = {
        id: this.generateId(),
        name: connectionConfig.name,
        host: connectionConfig.host,
        port: connectionConfig.port,
        username: connectionConfig.username,
        password: connectionConfig.password,
        privateKey: connectionConfig.privateKey,
        isConnected: true,
        currentPath: initialPath,
      };

      return this.connection;
    } catch (error) {
      throw new Error(`Failed to connect: ${(error as Error).message}`);
    }
  }

  async disconnect(): Promise<void> {
    try {
      if (this.sftp) {
        await this.sftp.end();
      }
      if (this.ssh) {
        this.ssh.dispose();
      }
      if (this.connection) {
        this.connection.isConnected = false;
      }
    } catch (error) {
      console.error('Error disconnecting:', error);
    }
  }

  async listDirectory(path: string): Promise<RemoteDirectory> {
    if (!this.connection?.isConnected) {
      throw new Error('Not connected to SSH server');
    }

    try {
      const files = await this.sftp.list(path);
      
      const remoteFiles: RemoteFile[] = files.map(file => ({
        name: file.name,
        path: `${path}/${file.name}`.replace(/\/+/g, '/'),
        type: file.type === 'd' ? 'directory' : 'file',
        size: file.size,
        modified: new Date(file.modifyTime),
        permissions: file.rights?.toString() || '',
      }));

      // Ordenar: directorios primero, luego archivos
      remoteFiles.sort((a, b) => {
        if (a.type !== b.type) {
          return a.type === 'directory' ? -1 : 1;
        }
        return a.name.localeCompare(b.name);
      });

      return {
        path,
        files: remoteFiles,
        parent: path !== '/' ? path.split('/').slice(0, -1).join('/') || '/' : undefined,
      };
    } catch (error) {
      throw new Error(`Failed to list directory: ${(error as Error).message}`);
    }
  }

  async createDirectory(path: string, name: string): Promise<void> {
    if (!this.connection?.isConnected) {
      throw new Error('Not connected to SSH server');
    }

    try {
      const fullPath = `${path}/${name}`.replace(/\/+/g, '/');
      await this.sftp.mkdir(fullPath);
    } catch (error) {
      throw new Error(`Failed to create directory: ${(error as Error).message}`);
    }
  }

  async deleteFile(path: string): Promise<void> {
    if (!this.connection?.isConnected) {
      throw new Error('Not connected to SSH server');
    }

    try {
      const stat = await this.sftp.stat(path);
      if (stat.isDirectory()) {
        await this.sftp.rmdir(path, true);
      } else {
        await this.sftp.delete(path);
      }
    } catch (error) {
      throw new Error(`Failed to delete: ${(error as Error).message}`);
    }
  }

  async renameFile(oldPath: string, newPath: string): Promise<void> {
    if (!this.connection?.isConnected) {
      throw new Error('Not connected to SSH server');
    }

    try {
      await this.sftp.rename(oldPath, newPath);
    } catch (error) {
      throw new Error(`Failed to rename: ${(error as Error).message}`);
    }
  }

  async executeCommand(command: string): Promise<string> {
    if (!this.connection?.isConnected) {
      throw new Error('Not connected to SSH server');
    }

    try {
      const result = await this.ssh.execCommand(command);
      if (result.stderr) {
        throw new Error(result.stderr);
      }
      return result.stdout;
    } catch (error) {
      throw new Error(`Command failed: ${(error as Error).message}`);
    }
  }

  async downloadFile(remotePath: string, url: string, fileName?: string): Promise<string> {
    if (!this.connection?.isConnected) {
      throw new Error('Not connected to SSH server');
    }

    try {
      // Generar nombre de archivo si no se proporciona
      const finalFileName = fileName || this.extractFileNameFromUrl(url);
      const fullPath = `${remotePath}/${finalFileName}`.replace(/\/+/g, '/');

      // Usar wget para descargar el archivo
      const command = `cd "${remotePath}" && wget -O "${finalFileName}" "${url}"`;
      const result = await this.executeCommand(command);
      
      return fullPath;
    } catch (error) {
      throw new Error(`Download failed: ${(error as Error).message}`);
    }
  }

  async getDownloadProgress(remotePath: string, fileName: string): Promise<number> {
    if (!this.connection?.isConnected) {
      throw new Error('Not connected to SSH server');
    }

    try {
      const fullPath = `${remotePath}/${fileName}`.replace(/\/+/g, '/');
      const stat = await this.sftp.stat(fullPath);
      return stat.size;
    } catch (error) {
      // Archivo no existe o no se puede acceder
      return 0;
    }
  }

  async checkWgetProgress(processId: string): Promise<{ progress: number; speed: string; eta: string }> {
    if (!this.connection?.isConnected) {
      throw new Error('Not connected to SSH server');
    }

    try {
      // Verificar el progreso de wget usando ps y archivos de log
      const command = `ps aux | grep wget | grep -v grep`;
      const result = await this.executeCommand(command);
      
      // Esto es una implementación básica - en un caso real necesitarías
      // un mecanismo más sofisticado para rastrear el progreso
      return {
        progress: 0,
        speed: '0 KB/s',
        eta: 'Unknown'
      };
    } catch (error) {
      return {
        progress: 0,
        speed: '0 KB/s',
        eta: 'Unknown'
      };
    }
  }

  getCurrentConnection(): SSHConnection | null {
    return this.connection;
  }

  private generateId(): string {
    return Math.random().toString(36).substr(2, 9);
  }

  private extractFileNameFromUrl(url: string): string {
    try {
      const urlObj = new URL(url);
      const pathname = urlObj.pathname;
      const fileName = pathname.split('/').pop() || 'download';
      
      // Si no tiene extensión, intentar detectarla del tipo de contenido
      if (!fileName.includes('.')) {
        return `${fileName}.mkv`; // Extensión por defecto para video
      }
      
      return fileName;
    } catch (error) {
      return `download_${Date.now()}.mkv`;
    }
  }

  async testConnection(config: Omit<SSHConnection, 'id' | 'isConnected' | 'currentPath'>): Promise<boolean> {
    const testSSH = new NodeSSH();
    
    try {
      const connectionConfig: any = {
        host: config.host,
        port: config.port,
        username: config.username,
      };

      if (config.password) {
        connectionConfig.password = config.password;
      }

      if (config.privateKey) {
        connectionConfig.privateKey = config.privateKey;
      }

      await testSSH.connect(connectionConfig);
      testSSH.dispose();
      return true;
    } catch (error) {
      return false;
    }
  }
}
