<!DOCTYPE html>
<html lang="es">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>M3U Import Cloud X2</title>
    <style>
        * {
            margin: 0;
            padding: 0;
            box-sizing: border-box;
        }

        body {
            font-family: 'Segoe UI', Tahoma, Geneva, Verdana, sans-serif;
            background: linear-gradient(135deg, #1a1a2e 0%, #16213e 50%, #0f3460 100%);
            color: #ffffff;
            height: 100vh;
            overflow: hidden;
        }

        .app-container {
            display: grid;
            grid-template-columns: 1fr 1fr 1fr;
            grid-template-rows: 60px 1fr;
            height: 100vh;
            gap: 1px;
            background: #333;
        }

        .header {
            grid-column: 1 / -1;
            background: linear-gradient(90deg, #2c3e50 0%, #3498db 100%);
            display: flex;
            align-items: center;
            justify-content: space-between;
            padding: 0 20px;
            box-shadow: 0 2px 10px rgba(0,0,0,0.3);
        }

        .logo {
            font-size: 24px;
            font-weight: bold;
            color: #ecf0f1;
            text-shadow: 2px 2px 4px rgba(0,0,0,0.5);
        }

        .status {
            display: flex;
            gap: 15px;
            align-items: center;
        }

        .status-item {
            display: flex;
            align-items: center;
            gap: 5px;
            font-size: 14px;
            color: #bdc3c7;
        }

        .status-dot {
            width: 8px;
            height: 8px;
            border-radius: 50%;
            background: #e74c3c;
        }

        .status-dot.active {
            background: #2ecc71;
            box-shadow: 0 0 10px rgba(46, 204, 113, 0.5);
        }

        .panel {
            background: rgba(44, 62, 80, 0.9);
            backdrop-filter: blur(10px);
            border-radius: 8px;
            margin: 10px;
            padding: 20px;
            overflow-y: auto;
            box-shadow: 0 4px 20px rgba(0,0,0,0.3);
            border: 1px solid rgba(255,255,255,0.1);
        }

        .panel-title {
            font-size: 18px;
            font-weight: bold;
            margin-bottom: 20px;
            color: #3498db;
            border-bottom: 2px solid #3498db;
            padding-bottom: 10px;
            display: flex;
            align-items: center;
            gap: 10px;
        }

        .btn {
            background: linear-gradient(45deg, #3498db, #2980b9);
            color: white;
            border: none;
            padding: 10px 20px;
            border-radius: 6px;
            cursor: pointer;
            font-size: 14px;
            transition: all 0.3s ease;
            box-shadow: 0 2px 10px rgba(52, 152, 219, 0.3);
        }

        .btn:hover {
            transform: translateY(-2px);
            box-shadow: 0 4px 20px rgba(52, 152, 219, 0.5);
        }

        .btn:active {
            transform: translateY(0);
        }

        .btn-success {
            background: linear-gradient(45deg, #2ecc71, #27ae60);
            box-shadow: 0 2px 10px rgba(46, 204, 113, 0.3);
        }

        .btn-danger {
            background: linear-gradient(45deg, #e74c3c, #c0392b);
            box-shadow: 0 2px 10px rgba(231, 76, 60, 0.3);
        }

        .input-group {
            margin-bottom: 15px;
        }

        .input-group label {
            display: block;
            margin-bottom: 5px;
            color: #bdc3c7;
            font-size: 14px;
        }

        .input-group input, .input-group select, .input-group textarea {
            width: 100%;
            padding: 10px;
            border: 1px solid rgba(255,255,255,0.2);
            border-radius: 6px;
            background: rgba(255,255,255,0.1);
            color: white;
            font-size: 14px;
            transition: all 0.3s ease;
        }

        .input-group input:focus, .input-group select:focus, .input-group textarea:focus {
            outline: none;
            border-color: #3498db;
            box-shadow: 0 0 10px rgba(52, 152, 219, 0.3);
        }

        .file-drop-zone {
            border: 2px dashed rgba(52, 152, 219, 0.5);
            border-radius: 8px;
            padding: 40px 20px;
            text-align: center;
            margin-bottom: 20px;
            transition: all 0.3s ease;
            cursor: pointer;
        }

        .file-drop-zone:hover {
            border-color: #3498db;
            background: rgba(52, 152, 219, 0.1);
        }

        .file-drop-zone.dragover {
            border-color: #2ecc71;
            background: rgba(46, 204, 113, 0.1);
        }

        .content-list {
            max-height: 400px;
            overflow-y: auto;
        }

        .content-item {
            background: rgba(255,255,255,0.1);
            margin: 8px 0;
            padding: 12px;
            border-radius: 6px;
            border-left: 4px solid #3498db;
            transition: all 0.3s ease;
            cursor: pointer;
        }

        .content-item:hover {
            background: rgba(255,255,255,0.2);
            transform: translateX(5px);
        }

        .content-item.selected {
            border-left-color: #2ecc71;
            background: rgba(46, 204, 113, 0.2);
        }

        .content-title {
            font-weight: bold;
            margin-bottom: 5px;
        }

        .content-meta {
            font-size: 12px;
            color: #bdc3c7;
            display: flex;
            justify-content: space-between;
        }

        .badge {
            background: #3498db;
            color: white;
            padding: 2px 8px;
            border-radius: 12px;
            font-size: 11px;
            font-weight: bold;
        }

        .badge.movie { background: #e67e22; }
        .badge.series { background: #9b59b6; }
        .badge.live { background: #e74c3c; }

        /* Scrollbar personalizado */
        ::-webkit-scrollbar {
            width: 8px;
        }

        ::-webkit-scrollbar-track {
            background: rgba(255,255,255,0.1);
            border-radius: 4px;
        }

        ::-webkit-scrollbar-thumb {
            background: rgba(52, 152, 219, 0.6);
            border-radius: 4px;
        }

        ::-webkit-scrollbar-thumb:hover {
            background: rgba(52, 152, 219, 0.8);
        }

        .progress-bar {
            width: 100%;
            height: 6px;
            background: rgba(255,255,255,0.2);
            border-radius: 3px;
            overflow: hidden;
            margin: 10px 0;
        }

        .progress-fill {
            height: 100%;
            background: linear-gradient(90deg, #2ecc71, #27ae60);
            border-radius: 3px;
            transition: width 0.3s ease;
        }

        .stats-grid {
            display: grid;
            grid-template-columns: repeat(auto-fit, minmax(120px, 1fr));
            gap: 15px;
            margin-bottom: 20px;
        }

        .stat-card {
            background: rgba(255,255,255,0.1);
            padding: 15px;
            border-radius: 8px;
            text-align: center;
            border: 1px solid rgba(255,255,255,0.2);
        }

        .stat-value {
            font-size: 24px;
            font-weight: bold;
            color: #3498db;
            margin-bottom: 5px;
        }

        .stat-label {
            font-size: 12px;
            color: #bdc3c7;
        }

        .loading {
            display: inline-block;
            width: 20px;
            height: 20px;
            border: 3px solid rgba(255,255,255,0.3);
            border-radius: 50%;
            border-top-color: #3498db;
            animation: spin 1s ease-in-out infinite;
        }

        @keyframes spin {
            to { transform: rotate(360deg); }
        }

        .hidden {
            display: none;
        }

        .fade-in {
            animation: fadeIn 0.5s ease-in;
        }

        @keyframes fadeIn {
            from { opacity: 0; transform: translateY(20px); }
            to { opacity: 1; transform: translateY(0); }
        }

        /* Estilos para series agrupadas */
        .episodes-container {
            margin-left: 20px;
            border-left: 2px solid rgba(52, 152, 219, 0.3);
            padding-left: 15px;
            margin-top: 10px;
        }

        .season-group {
            margin-bottom: 15px;
        }

        .season-title {
            font-weight: bold;
            color: #3498db;
            margin-bottom: 8px;
            padding: 5px 10px;
            background: rgba(52, 152, 219, 0.2);
            border-radius: 4px;
            font-size: 14px;
        }

        .episode-item {
            background: rgba(255,255,255,0.05);
            margin: 4px 0;
            padding: 8px 12px;
            border-radius: 4px;
            border-left: 3px solid #9b59b6;
            transition: all 0.3s ease;
            cursor: pointer;
            font-size: 13px;
        }

        .episode-item:hover {
            background: rgba(255,255,255,0.1);
            transform: translateX(5px);
        }

        .episode-title {
            font-weight: 500;
            margin-bottom: 2px;
        }

        .episode-meta {
            font-size: 11px;
            color: #bdc3c7;
        }

        /* Menú contextual */
        .context-menu {
            position: absolute;
            background: rgba(44, 62, 80, 0.95);
            backdrop-filter: blur(10px);
            border: 1px solid rgba(255,255,255,0.2);
            border-radius: 8px;
            box-shadow: 0 8px 32px rgba(0,0,0,0.5);
            z-index: 1000;
            min-width: 200px;
            overflow: hidden;
        }

        .context-menu-item {
            padding: 12px 16px;
            cursor: pointer;
            transition: all 0.3s ease;
            border-bottom: 1px solid rgba(255,255,255,0.1);
            font-size: 14px;
        }

        .context-menu-item:last-child {
            border-bottom: none;
        }

        .context-menu-item:hover {
            background: rgba(52, 152, 219, 0.3);
            color: #ffffff;
        }

        /* Modal */
        .modal-overlay {
            position: fixed;
            top: 0;
            left: 0;
            width: 100%;
            height: 100%;
            background: rgba(0,0,0,0.7);
            backdrop-filter: blur(5px);
            display: flex;
            align-items: center;
            justify-content: center;
            z-index: 2000;
        }

        .modal-content {
            background: rgba(44, 62, 80, 0.95);
            backdrop-filter: blur(10px);
            border: 1px solid rgba(255,255,255,0.2);
            border-radius: 12px;
            padding: 30px;
            max-width: 500px;
            width: 90%;
            box-shadow: 0 20px 60px rgba(0,0,0,0.5);
        }

        .modal-content h3 {
            margin-bottom: 20px;
            color: #3498db;
            text-align: center;
        }

        .season-selector {
            margin: 20px 0;
        }

        .season-checkbox {
            display: block;
            margin: 10px 0;
            cursor: pointer;
            padding: 10px;
            border-radius: 6px;
            transition: all 0.3s ease;
        }

        .season-checkbox:hover {
            background: rgba(255,255,255,0.1);
        }

        .season-checkbox input {
            margin-right: 10px;
        }

        .modal-buttons {
            display: flex;
            gap: 15px;
            justify-content: center;
            margin-top: 25px;
        }

        /* Indicadores de carga para SFTP */
        .loading-indicator {
            text-align: center;
            padding: 20px;
            color: #bdc3c7;
        }

        .file-icon {
            margin-right: 8px;
        }

        /* Mejoras visuales para tipos de archivo */
        .content-item[data-file-type="video"] {
            border-left-color: #e74c3c;
        }

        .content-item[data-file-type="audio"] {
            border-left-color: #f39c12;
        }

        .content-item[data-file-type="directory"] {
            border-left-color: #3498db;
        }

        .content-item[data-file-type="series-group"] {
            border-left-color: #9b59b6;
            background: rgba(155, 89, 182, 0.1);
        }
    </style>
</head>
<body>
    <div class="app-container">
        <!-- Header -->
        <div class="header">
            <div class="logo">🎬 M3U Import Cloud X2</div>
            <div class="status">
                <div class="status-item">
                    <div class="status-dot" id="m3u-status"></div>
                    <span>M3U</span>
                </div>
                <div class="status-item">
                    <div class="status-dot" id="ssh-status"></div>
                    <span>SSH</span>
                </div>
                <div class="status-item">
                    <div class="status-dot" id="download-status"></div>
                    <span>Downloads</span>
                </div>
            </div>
        </div>

        <!-- Panel M3U -->
        <div class="panel">
            <div class="panel-title">
                📺 Panel M3U
            </div>
            
            <div class="file-drop-zone" id="m3u-drop-zone">
                <div>📁 Arrastra un archivo M3U aquí</div>
                <div style="margin: 10px 0;">o</div>
                <button class="btn" onclick="selectM3UFile()">Seleccionar Archivo</button>
            </div>
            
            <div class="input-group">
                <label>URL de M3U:</label>
                <input type="url" id="m3u-url" placeholder="https://ejemplo.com/playlist.m3u">
                <button class="btn" onclick="loadM3UFromURL()" style="margin-top: 10px;">Cargar desde URL</button>
            </div>

            <div id="m3u-content" class="hidden">
                <div class="stats-grid">
                    <div class="stat-card">
                        <div class="stat-value" id="total-count">0</div>
                        <div class="stat-label">Total</div>
                    </div>
                    <div class="stat-card">
                        <div class="stat-value" id="movie-count">0</div>
                        <div class="stat-label">Películas</div>
                    </div>
                    <div class="stat-card">
                        <div class="stat-value" id="series-count">0</div>
                        <div class="stat-label">Series</div>
                    </div>
                    <div class="stat-card">
                        <div class="stat-value" id="live-count">0</div>
                        <div class="stat-label">TV en Vivo</div>
                    </div>
                </div>

                <div class="content-list" id="content-list">
                    <!-- Contenido M3U se cargará aquí -->
                </div>
            </div>
        </div>

        <!-- Panel SSH/SFTP -->
        <div class="panel">
            <div class="panel-title">
                🔐 Panel SSH/SFTP
            </div>
            
            <div class="input-group">
                <label>Servidor:</label>
                <input type="text" id="ssh-host" placeholder="*************">
            </div>
            
            <div class="input-group">
                <label>Puerto:</label>
                <input type="number" id="ssh-port" value="22">
            </div>
            
            <div class="input-group">
                <label>Usuario:</label>
                <input type="text" id="ssh-user" placeholder="usuario">
            </div>
            
            <div class="input-group">
                <label>Contraseña:</label>
                <input type="password" id="ssh-password" placeholder="contraseña">
            </div>
            
            <button class="btn btn-success" onclick="connectSSH()">
                <span id="ssh-btn-text">Conectar SSH</span>
                <span id="ssh-loading" class="loading hidden"></span>
            </button>

            <div id="ssh-content" class="hidden">
                <div style="margin: 20px 0;">
                    <strong>📁 Explorador de Archivos:</strong>
                    <div id="current-path" style="color: #3498db; margin: 10px 0;">/home/<USER>/div>
                </div>
                
                <div class="content-list" id="file-list">
                    <!-- Lista de archivos se cargará aquí -->
                </div>
            </div>
        </div>

        <!-- Panel Descargas -->
        <div class="panel">
            <div class="panel-title">
                📥 Panel de Descargas
            </div>
            
            <div class="stats-grid">
                <div class="stat-card">
                    <div class="stat-value" id="queue-total">0</div>
                    <div class="stat-label">En Cola</div>
                </div>
                <div class="stat-card">
                    <div class="stat-value" id="queue-active">0</div>
                    <div class="stat-label">Activas</div>
                </div>
                <div class="stat-card">
                    <div class="stat-value" id="queue-completed">0</div>
                    <div class="stat-label">Completadas</div>
                </div>
                <div class="stat-card">
                    <div class="stat-value" id="queue-failed">0</div>
                    <div class="stat-label">Fallidas</div>
                </div>
            </div>

            <div style="margin-bottom: 20px;">
                <button class="btn btn-success" onclick="startDownloads()">▶️ Iniciar Descargas</button>
                <button class="btn btn-danger" onclick="pauseDownloads()">⏸️ Pausar</button>
                <button class="btn" onclick="clearCompleted()">🧹 Limpiar</button>
            </div>

            <div class="content-list" id="download-list">
                <div style="text-align: center; color: #bdc3c7; margin-top: 50px;">
                    No hay descargas en la cola.<br>
                    Selecciona contenido desde el panel M3U para comenzar.
                </div>
            </div>
        </div>
    </div>

    <input type="file" id="file-input" accept=".m3u,.m3u8" style="display: none;">

    <script src="app.js"></script>
</body>
</html>
