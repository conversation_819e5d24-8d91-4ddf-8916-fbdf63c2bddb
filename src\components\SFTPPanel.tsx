import React, { useState, useEffect, useCallback } from 'react';
import styled from 'styled-components';
import { SSHConnection, RemoteFile, RemoteDirectory } from '../types';
import { SSHService } from '../services/SSHService';

interface SFTPPanelProps {
  connections: SSHConnection[];
  currentConnection?: string;
  selectedDestination?: string;
  onConnectionUpdate: (connection: SSHConnection) => void;
  onDestinationSelect: (path: string) => void;
}

const PanelContainer = styled.div`
  height: 100%;
  background: #252525;
  display: flex;
  flex-direction: column;
`;

const Header = styled.div`
  padding: 15px;
  background: #2d2d2d;
  border-bottom: 1px solid #404040;
`;

const Title = styled.h2`
  font-size: 16px;
  font-weight: 600;
  margin: 0 0 10px 0;
  color: #ffffff;
`;

const ButtonGroup = styled.div`
  display: flex;
  gap: 10px;
  margin-bottom: 10px;
`;

const Button = styled.button<{ variant?: 'primary' | 'secondary' | 'danger' }>`
  padding: 8px 16px;
  background: ${props => {
    switch (props.variant) {
      case 'danger': return '#dc3545';
      case 'secondary': return '#6c757d';
      default: return '#0078d4';
    }
  }};
  color: white;
  border: none;
  border-radius: 4px;
  cursor: pointer;
  font-size: 12px;
  transition: background-color 0.2s;

  &:hover {
    background: ${props => {
      switch (props.variant) {
        case 'danger': return '#c82333';
        case 'secondary': return '#5a6268';
        default: return '#106ebe';
      }
    }};
  }

  &:disabled {
    background: #404040;
    cursor: not-allowed;
  }
`;

const ConnectionForm = styled.div`
  padding: 15px;
  background: #2a2a2a;
  border-bottom: 1px solid #404040;
`;

const FormRow = styled.div`
  display: flex;
  gap: 10px;
  margin-bottom: 10px;
`;

const Input = styled.input`
  flex: 1;
  padding: 8px;
  background: #404040;
  color: white;
  border: 1px solid #606060;
  border-radius: 4px;
  font-size: 12px;

  &::placeholder {
    color: #888;
  }
`;

const Select = styled.select`
  flex: 1;
  padding: 8px;
  background: #404040;
  color: white;
  border: 1px solid #606060;
  border-radius: 4px;
  font-size: 12px;

  option {
    background: #404040;
    color: white;
  }
`;

const Content = styled.div`
  flex: 1;
  overflow: hidden;
  display: flex;
  flex-direction: column;
`;

const PathBar = styled.div`
  padding: 10px 15px;
  background: #2a2a2a;
  border-bottom: 1px solid #404040;
  display: flex;
  align-items: center;
  gap: 10px;
`;

const PathInput = styled.input`
  flex: 1;
  padding: 6px 8px;
  background: #404040;
  color: white;
  border: 1px solid #606060;
  border-radius: 4px;
  font-size: 12px;
  font-family: monospace;
`;

const FileList = styled.div`
  flex: 1;
  overflow-y: auto;
  padding: 10px;
`;

const FileItem = styled.div<{ selected: boolean; isDirectory: boolean }>`
  padding: 8px;
  margin-bottom: 2px;
  background: ${props => props.selected ? '#0078d4' : '#353535'};
  border-radius: 4px;
  cursor: pointer;
  transition: background-color 0.2s;
  font-size: 12px;
  line-height: 1.4;
  display: flex;
  align-items: center;
  gap: 8px;

  &:hover {
    background: ${props => props.selected ? '#106ebe' : '#404040'};
  }
`;

const FileIcon = styled.span<{ isDirectory: boolean }>`
  color: ${props => props.isDirectory ? '#ffd700' : '#87ceeb'};
  font-weight: bold;
`;

const FileName = styled.div`
  flex: 1;
  color: #ffffff;
  font-weight: 500;
`;

const FileInfo = styled.div`
  color: #ccc;
  font-size: 11px;
  text-align: right;
`;

const StatusBar = styled.div`
  padding: 8px 15px;
  background: #2d2d2d;
  border-top: 1px solid #404040;
  font-size: 11px;
  color: #ccc;
  display: flex;
  justify-content: space-between;
`;

const SFTPPanel: React.FC<SFTPPanelProps> = ({
  connections,
  currentConnection,
  selectedDestination,
  onConnectionUpdate,
  onDestinationSelect,
}) => {
  const [sshService] = useState(() => new SSHService());
  const [isConnecting, setIsConnecting] = useState(false);
  const [currentDirectory, setCurrentDirectory] = useState<RemoteDirectory | null>(null);
  const [selectedFiles, setSelectedFiles] = useState<Set<string>>(new Set());
  const [currentPath, setCurrentPath] = useState('/');

  // Form state
  const [connectionForm, setConnectionForm] = useState({
    name: '',
    host: '',
    port: 22,
    username: '',
    password: '',
    privateKey: '',
  });

  const currentConnectionData = connections.find(c => c.id === currentConnection);

  const handleConnect = useCallback(async () => {
    if (!connectionForm.host || !connectionForm.username) {
      alert('Por favor complete los campos requeridos');
      return;
    }

    setIsConnecting(true);
    try {
      const connection = await sshService.connect(connectionForm);
      onConnectionUpdate(connection);

      // Cargar directorio inicial
      const directory = await sshService.listDirectory(connection.currentPath);
      setCurrentDirectory(directory);
      setCurrentPath(connection.currentPath);
    } catch (error) {
      alert(`Error de conexión: ${(error as Error).message}`);
    } finally {
      setIsConnecting(false);
    }
  }, [connectionForm, sshService, onConnectionUpdate]);

  const handleDisconnect = useCallback(async () => {
    try {
      await sshService.disconnect();
      setCurrentDirectory(null);
      setCurrentPath('/');
      setSelectedFiles(new Set());
    } catch (error) {
      console.error('Error disconnecting:', error);
    }
  }, [sshService]);

  const navigateToDirectory = useCallback(async (path: string) => {
    if (!currentConnectionData?.isConnected) return;

    try {
      const directory = await sshService.listDirectory(path);
      setCurrentDirectory(directory);
      setCurrentPath(path);
      setSelectedFiles(new Set());
    } catch (error) {
      alert(`Error navegando a ${path}: ${(error as Error).message}`);
    }
  }, [currentConnectionData, sshService]);

  const handleFileDoubleClick = useCallback((file: RemoteFile) => {
    if (file.type === 'directory') {
      navigateToDirectory(file.path);
    }
  }, [navigateToDirectory]);

  const handleFileClick = useCallback((fileName: string, ctrlKey: boolean) => {
    if (ctrlKey) {
      const newSelected = new Set(selectedFiles);
      if (newSelected.has(fileName)) {
        newSelected.delete(fileName);
      } else {
        newSelected.add(fileName);
      }
      setSelectedFiles(newSelected);
    } else {
      setSelectedFiles(new Set([fileName]));
    }
  }, [selectedFiles]);

  const handleSetDestination = useCallback(() => {
    if (currentPath) {
      onDestinationSelect(currentPath);
    }
  }, [currentPath, onDestinationSelect]);

  const formatFileSize = (bytes: number): string => {
    if (bytes === 0) return '0 B';
    const k = 1024;
    const sizes = ['B', 'KB', 'MB', 'GB'];
    const i = Math.floor(Math.log(bytes) / Math.log(k));
    return parseFloat((bytes / Math.pow(k, i)).toFixed(1)) + ' ' + sizes[i];
  };

  return (
    <PanelContainer>
      <Header>
        <Title>Conexión SSH/SFTP</Title>
        <ButtonGroup>
          {!currentConnectionData?.isConnected ? (
            <Button onClick={handleConnect} disabled={isConnecting}>
              {isConnecting ? 'Conectando...' : 'Conectar'}
            </Button>
          ) : (
            <>
              <Button onClick={handleDisconnect} variant="danger">
                Desconectar
              </Button>
              <Button onClick={handleSetDestination} variant="secondary">
                Fijar como Destino
              </Button>
            </>
          )}
        </ButtonGroup>
      </Header>

      {!currentConnectionData?.isConnected && (
        <ConnectionForm>
          <FormRow>
            <Input
              type="text"
              placeholder="Nombre de conexión"
              value={connectionForm.name}
              onChange={(e) => setConnectionForm(prev => ({ ...prev, name: e.target.value }))}
            />
          </FormRow>
          <FormRow>
            <Input
              type="text"
              placeholder="Host/IP"
              value={connectionForm.host}
              onChange={(e) => setConnectionForm(prev => ({ ...prev, host: e.target.value }))}
            />
            <Input
              type="number"
              placeholder="Puerto"
              value={connectionForm.port}
              onChange={(e) => setConnectionForm(prev => ({ ...prev, port: parseInt(e.target.value) || 22 }))}
            />
          </FormRow>
          <FormRow>
            <Input
              type="text"
              placeholder="Usuario"
              value={connectionForm.username}
              onChange={(e) => setConnectionForm(prev => ({ ...prev, username: e.target.value }))}
            />
            <Input
              type="password"
              placeholder="Contraseña"
              value={connectionForm.password}
              onChange={(e) => setConnectionForm(prev => ({ ...prev, password: e.target.value }))}
            />
          </FormRow>
        </ConnectionForm>
      )}

      <Content>
        {currentConnectionData?.isConnected && currentDirectory && (
          <>
            <PathBar>
              <Button
                onClick={() => currentDirectory.parent && navigateToDirectory(currentDirectory.parent)}
                disabled={!currentDirectory.parent}
                variant="secondary"
              >
                ↑
              </Button>
              <PathInput
                value={currentPath}
                onChange={(e) => setCurrentPath(e.target.value)}
                onKeyPress={(e) => {
                  if (e.key === 'Enter') {
                    navigateToDirectory(currentPath);
                  }
                }}
              />
            </PathBar>

            <FileList>
              {currentDirectory.files.map(file => (
                <FileItem
                  key={file.name}
                  selected={selectedFiles.has(file.name)}
                  isDirectory={file.type === 'directory'}
                  onClick={(e) => handleFileClick(file.name, e.ctrlKey)}
                  onDoubleClick={() => handleFileDoubleClick(file)}
                >
                  <FileIcon isDirectory={file.type === 'directory'}>
                    {file.type === 'directory' ? '📁' : '📄'}
                  </FileIcon>
                  <FileName>{file.name}</FileName>
                  <FileInfo>
                    {file.type === 'file' && formatFileSize(file.size)}
                  </FileInfo>
                </FileItem>
              ))}
            </FileList>

            <StatusBar>
              <span>
                {currentDirectory.files.length} elementos
                {selectedFiles.size > 0 && ` • ${selectedFiles.size} seleccionados`}
              </span>
              <span>
                {selectedDestination && `Destino: ${selectedDestination}`}
              </span>
            </StatusBar>
          </>
        )}
      </Content>
    </PanelContainer>
  );
};

export default SFTPPanel;
