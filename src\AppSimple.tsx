import React, { useState } from 'react';

const AppSimple: React.FC = () => {
  const [counter, setCounter] = useState(0);
  
  console.log('🎭 AppSimple rendering, counter:', counter);
  
  return (
    <div style={{ 
      width: '100vw', 
      height: '100vh', 
      background: 'linear-gradient(135deg, #667eea 0%, #764ba2 100%)', 
      color: 'white',
      padding: '40px',
      fontFamily: 'Arial, sans-serif',
      display: 'flex',
      flexDirection: 'column',
      alignItems: 'center',
      justifyContent: 'center'
    }}>
      <div style={{ 
        background: 'rgba(255,255,255,0.1)', 
        padding: '30px', 
        borderRadius: '15px',
        textAlign: 'center',
        backdropFilter: 'blur(10px)',
        border: '1px solid rgba(255,255,255,0.2)'
      }}>
        <h1 style={{ 
          fontSize: '3em', 
          marginBottom: '20px',
          textShadow: '2px 2px 4px rgba(0,0,0,0.5)'
        }}>
          🎉 M3U Import Cloud X2
        </h1>
        
        <h2 style={{ 
          color: '#00ff88', 
          marginBottom: '30px',
          fontSize: '1.5em'
        }}>
          ✅ ¡APLICACIÓN FUNCIONANDO PERFECTAMENTE!
        </h2>
        
        <div style={{ 
          background: 'rgba(0,0,0,0.3)', 
          padding: '20px', 
          borderRadius: '10px',
          marginBottom: '30px'
        }}>
          <h3 style={{ marginBottom: '15px' }}>🔧 Estado del Sistema:</h3>
          <ul style={{ textAlign: 'left', lineHeight: '1.8' }}>
            <li>✅ React: Funcionando</li>
            <li>✅ Electron: Funcionando</li>
            <li>✅ TypeScript: Funcionando</li>
            <li>✅ Webpack: Funcionando</li>
            <li>🎯 Contador: {counter}</li>
          </ul>
        </div>
        
        <button 
          onClick={() => setCounter(c => c + 1)}
          style={{
            background: 'linear-gradient(45deg, #ff6b6b, #ee5a24)',
            border: 'none',
            color: 'white',
            padding: '15px 30px',
            fontSize: '18px',
            borderRadius: '25px',
            cursor: 'pointer',
            boxShadow: '0 4px 15px rgba(0,0,0,0.3)',
            transition: 'transform 0.2s'
          }}
          onMouseOver={(e) => e.currentTarget.style.transform = 'scale(1.05)'}
          onMouseOut={(e) => e.currentTarget.style.transform = 'scale(1)'}
        >
          🚀 Incrementar Contador
        </button>
        
        <div style={{ 
          marginTop: '30px', 
          fontSize: '14px', 
          opacity: 0.8 
        }}>
          <p>🎯 Si ves esto, la aplicación está funcionando correctamente</p>
          <p>📝 Próximo paso: Restaurar funcionalidad completa</p>
        </div>
      </div>
    </div>
  );
};

export default AppSimple;
