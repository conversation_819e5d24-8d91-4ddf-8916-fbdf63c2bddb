import React, { useState } from 'react';
import styled from 'styled-components';
import { M3UPlaylist, M3UEntry } from '../types';

const Container = styled.div`
  height: 100%;
  background: #2d2d2d;
  border-right: 1px solid #404040;
  display: flex;
  flex-direction: column;
`;

const Header = styled.div`
  padding: 15px;
  background: #3d3d3d;
  border-bottom: 1px solid #404040;
`;

const Title = styled.h3`
  margin: 0;
  color: #ffffff;
  font-size: 16px;
`;

const Content = styled.div`
  flex: 1;
  padding: 15px;
  overflow-y: auto;
`;

const Button = styled.button`
  background: #0078d4;
  color: white;
  border: none;
  padding: 10px 15px;
  border-radius: 4px;
  cursor: pointer;
  margin: 5px 0;
  
  &:hover {
    background: #106ebe;
  }
`;

const FileInput = styled.input`
  margin: 10px 0;
  color: white;
`;

const PlaylistItem = styled.div`
  background: #404040;
  padding: 10px;
  margin: 5px 0;
  border-radius: 4px;
  cursor: pointer;
  
  &:hover {
    background: #505050;
  }
`;

interface Props {
  playlists: M3UPlaylist[];
  currentPlaylist?: string;
  onPlaylistUpdate: (playlist: M3UPlaylist) => void;
  onDownloadRequest: (entries: M3UEntry[], seriesTitle?: string) => void;
}

const M3UPanelSimple: React.FC<Props> = ({ 
  playlists, 
  currentPlaylist, 
  onPlaylistUpdate, 
  onDownloadRequest 
}) => {
  const [selectedFile, setSelectedFile] = useState<File | null>(null);

  const handleFileSelect = (event: React.ChangeEvent<HTMLInputElement>) => {
    const file = event.target.files?.[0];
    if (file) {
      setSelectedFile(file);
    }
  };

  const handleLoadPlaylist = () => {
    if (!selectedFile) return;

    // Simular carga de playlist
    const mockPlaylist: M3UPlaylist = {
      id: Date.now().toString(),
      name: selectedFile.name,
      url: '',
      entries: [
        {
          id: '1',
          title: 'Película de Ejemplo 1',
          url: 'http://example.com/movie1.mp4',
          duration: 7200,
          group: 'Películas',
          type: 'movie' as const,
          logo: ''
        },
        {
          id: '2',
          title: 'Serie Ejemplo S01E01',
          url: 'http://example.com/serie1.mp4',
          duration: 2700,
          group: 'Series',
          type: 'series' as const,
          logo: ''
        }
      ],
      groups: ['Películas', 'Series'],
      lastUpdated: new Date()
    };

    onPlaylistUpdate(mockPlaylist);
    setSelectedFile(null);
  };

  const handleDownloadAll = () => {
    const currentList = playlists.find(p => p.id === currentPlaylist);
    if (currentList) {
      onDownloadRequest(currentList.entries);
    }
  };

  return (
    <Container>
      <Header>
        <Title>📺 Panel M3U</Title>
      </Header>
      <Content>
        <div>
          <h4 style={{ color: 'white', marginBottom: '10px' }}>Cargar Playlist M3U</h4>
          <FileInput
            type="file"
            accept=".m3u,.m3u8"
            onChange={handleFileSelect}
          />
          <Button onClick={handleLoadPlaylist} disabled={!selectedFile}>
            📂 Cargar Playlist
          </Button>
        </div>

        <div style={{ marginTop: '20px' }}>
          <h4 style={{ color: 'white', marginBottom: '10px' }}>
            Playlists Cargadas ({playlists.length})
          </h4>
          {playlists.map(playlist => (
            <PlaylistItem key={playlist.id}>
              <div style={{ fontWeight: 'bold', color: 'white' }}>
                {playlist.name}
              </div>
              <div style={{ fontSize: '12px', color: '#ccc', marginTop: '5px' }}>
                {playlist.entries.length} elementos • {playlist.groups.length} categorías
              </div>
              {currentPlaylist === playlist.id && (
                <Button onClick={handleDownloadAll} style={{ marginTop: '10px' }}>
                  📥 Descargar Todo
                </Button>
              )}
            </PlaylistItem>
          ))}
        </div>

        {playlists.length === 0 && (
          <div style={{ 
            color: '#888', 
            textAlign: 'center', 
            marginTop: '50px',
            fontStyle: 'italic'
          }}>
            No hay playlists cargadas.<br />
            Selecciona un archivo M3U para comenzar.
          </div>
        )}
      </Content>
    </Container>
  );
};

export default M3UPanelSimple;
