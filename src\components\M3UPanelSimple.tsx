import React, { useState } from 'react';
import styled from 'styled-components';
import { M3UPlaylist, M3UEntry } from '../types';
import { M3UParser } from '../services/M3UParser';

const Container = styled.div`
  height: 100%;
  background: #2d2d2d;
  border-right: 1px solid #404040;
  display: flex;
  flex-direction: column;
`;

const Header = styled.div`
  padding: 15px;
  background: #3d3d3d;
  border-bottom: 1px solid #404040;
`;

const Title = styled.h3`
  margin: 0;
  color: #ffffff;
  font-size: 16px;
`;

const Content = styled.div`
  flex: 1;
  padding: 15px;
  overflow-y: auto;
`;

const Button = styled.button`
  background: #0078d4;
  color: white;
  border: none;
  padding: 10px 15px;
  border-radius: 4px;
  cursor: pointer;
  margin: 5px 0;
  
  &:hover {
    background: #106ebe;
  }
`;

const FileInput = styled.input`
  margin: 10px 0;
  color: white;
`;

const PlaylistItem = styled.div`
  background: #404040;
  padding: 10px;
  margin: 5px 0;
  border-radius: 4px;
  cursor: pointer;

  &:hover {
    background: #505050;
  }
`;

const EntryList = styled.div`
  max-height: 300px;
  overflow-y: auto;
  margin-top: 10px;
  border: 1px solid #555;
  border-radius: 4px;
`;

const EntryItem = styled.div`
  padding: 8px 12px;
  border-bottom: 1px solid #555;
  cursor: pointer;

  &:hover {
    background: #505050;
  }

  &:last-child {
    border-bottom: none;
  }
`;

const EntryTitle = styled.div`
  color: white;
  font-size: 13px;
  font-weight: bold;
  margin-bottom: 4px;
`;

const EntryDetails = styled.div`
  color: #ccc;
  font-size: 11px;
  display: flex;
  justify-content: space-between;
`;

const TypeBadge = styled.span<{ type: string }>`
  background: ${props => {
    switch (props.type) {
      case 'movie': return '#e74c3c';
      case 'series': return '#3498db';
      case 'live': return '#2ecc71';
      default: return '#95a5a6';
    }
  }};
  color: white;
  padding: 2px 6px;
  border-radius: 10px;
  font-size: 10px;
  font-weight: bold;
  margin-left: 8px;
`;

interface Props {
  playlists: M3UPlaylist[];
  currentPlaylist?: string;
  onPlaylistUpdate: (playlist: M3UPlaylist) => void;
  onDownloadRequest: (entries: M3UEntry[], seriesTitle?: string) => void;
}

const M3UPanelSimple: React.FC<Props> = ({ 
  playlists, 
  currentPlaylist, 
  onPlaylistUpdate, 
  onDownloadRequest 
}) => {
  const [selectedFile, setSelectedFile] = useState<File | null>(null);
  const [urlInput, setUrlInput] = useState('');

  const handleFileSelect = (event: React.ChangeEvent<HTMLInputElement>) => {
    const file = event.target.files?.[0];
    if (file) {
      setSelectedFile(file);
    }
  };

  const handleLoadPlaylist = async () => {
    if (!selectedFile) return;

    try {
      // Leer el archivo M3U
      const content = await readFileContent(selectedFile);

      // Parsear con el parser real
      const playlist = M3UParser.parseM3U(content, selectedFile.name);

      console.log('📺 Playlist cargada:', playlist.name, 'con', playlist.entries.length, 'entradas');

      onPlaylistUpdate(playlist);
      setSelectedFile(null);
    } catch (error) {
      console.error('❌ Error cargando playlist:', error);
      alert(`Error cargando playlist: ${error}`);
    }
  };

  const readFileContent = (file: File): Promise<string> => {
    return new Promise((resolve, reject) => {
      const reader = new FileReader();
      reader.onload = (e) => {
        const content = e.target?.result as string;
        resolve(content);
      };
      reader.onerror = () => reject(new Error('Error leyendo archivo'));
      reader.readAsText(file, 'utf-8');
    });
  };

  const handleLoadFromURL = async () => {
    if (!urlInput.trim()) {
      alert('Por favor ingresa una URL válida');
      return;
    }

    try {
      console.log('🌐 Cargando M3U desde URL:', urlInput);

      const response = await fetch(urlInput);
      if (!response.ok) {
        throw new Error(`HTTP ${response.status}: ${response.statusText}`);
      }

      const content = await response.text();
      const fileName = urlInput.split('/').pop() || 'playlist.m3u';

      const playlist = M3UParser.parseM3U(content, fileName, urlInput);

      console.log('📺 Playlist desde URL cargada:', playlist.name, 'con', playlist.entries.length, 'entradas');

      onPlaylistUpdate(playlist);
      setUrlInput('');
    } catch (error) {
      console.error('❌ Error cargando desde URL:', error);
      alert(`Error cargando desde URL: ${error}`);
    }
  };

  const handleDownloadAll = () => {
    const currentList = playlists.find(p => p.id === currentPlaylist);
    if (currentList) {
      onDownloadRequest(currentList.entries);
    }
  };

  return (
    <Container>
      <Header>
        <Title>📺 Panel M3U</Title>
      </Header>
      <Content>
        <div>
          <h4 style={{ color: 'white', marginBottom: '10px' }}>Cargar Playlist M3U</h4>
          <FileInput
            type="file"
            accept=".m3u,.m3u8"
            onChange={handleFileSelect}
          />
          <Button onClick={handleLoadPlaylist} disabled={!selectedFile}>
            📂 Cargar Playlist
          </Button>

          <div style={{ marginTop: '15px', padding: '10px', background: '#353535', borderRadius: '4px' }}>
            <div style={{ color: 'white', fontWeight: 'bold', marginBottom: '8px' }}>
              🌐 Cargar desde URL
            </div>
            <input
              type="text"
              value={urlInput}
              onChange={(e) => setUrlInput(e.target.value)}
              placeholder="https://ejemplo.com/playlist.m3u"
              style={{
                width: '100%',
                padding: '8px',
                background: '#2d2d2d',
                border: '1px solid #555',
                borderRadius: '4px',
                color: 'white',
                fontSize: '12px'
              }}
              onKeyPress={(e) => e.key === 'Enter' && handleLoadFromURL()}
            />
            <Button
              onClick={handleLoadFromURL}
              style={{ marginTop: '8px', width: '100%' }}
              disabled={!urlInput.trim()}
            >
              🔗 Cargar desde URL
            </Button>
          </div>
        </div>

        <div style={{ marginTop: '20px' }}>
          <h4 style={{ color: 'white', marginBottom: '10px' }}>
            Playlists Cargadas ({playlists.length})
          </h4>
          {playlists.map(playlist => (
            <PlaylistItem key={playlist.id}>
              <div style={{ fontWeight: 'bold', color: 'white' }}>
                {playlist.name}
              </div>
              <div style={{ fontSize: '12px', color: '#ccc', marginTop: '5px' }}>
                {playlist.entries.length} elementos • {playlist.groups.length} categorías
              </div>

              {currentPlaylist === playlist.id && (
                <>
                  <Button onClick={handleDownloadAll} style={{ marginTop: '10px' }}>
                    📥 Descargar Todo ({playlist.entries.length})
                  </Button>

                  <EntryList>
                    {playlist.entries.slice(0, 50).map(entry => (
                      <EntryItem
                        key={entry.id}
                        onClick={() => onDownloadRequest([entry])}
                      >
                        <EntryTitle>
                          {entry.title}
                          <TypeBadge type={entry.type}>
                            {entry.type === 'movie' ? 'PEL' :
                             entry.type === 'series' ? 'SER' :
                             entry.type === 'live' ? 'TV' : '?'}
                          </TypeBadge>
                        </EntryTitle>
                        <EntryDetails>
                          <span>📁 {entry.group}</span>
                          <span>⏱️ {Math.floor(entry.duration / 60)}min</span>
                        </EntryDetails>
                      </EntryItem>
                    ))}
                    {playlist.entries.length > 50 && (
                      <div style={{
                        padding: '10px',
                        textAlign: 'center',
                        color: '#888',
                        fontStyle: 'italic'
                      }}>
                        ... y {playlist.entries.length - 50} elementos más
                      </div>
                    )}
                  </EntryList>
                </>
              )}
            </PlaylistItem>
          ))}
        </div>

        {playlists.length === 0 && (
          <div style={{ 
            color: '#888', 
            textAlign: 'center', 
            marginTop: '50px',
            fontStyle: 'italic'
          }}>
            No hay playlists cargadas.<br />
            Selecciona un archivo M3U para comenzar.
          </div>
        )}
      </Content>
    </Container>
  );
};

export default M3UPanelSimple;
