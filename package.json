{"name": "m3u-import-cloud-x2", "version": "1.0.0", "main": "dist/electron/main.js", "homepage": "./", "scripts": {"dev": "webpack serve --mode development", "build": "webpack --mode production && tsc electron/main.ts --outDir dist/electron --target es2020 --module commonjs --esModuleInterop --allowSyntheticDefaultImports --skipLibCheck", "electron": "npm run build && electron .", "electron-dev": "concurrently \"npm run dev\" \"wait-on http://localhost:3000 && npm run electron\"", "build-electron": "npm run build", "dist": "npm run build-electron && electron-builder", "test": "echo \"Error: no test specified\" && exit 1"}, "keywords": ["m3u", "xtream", "ssh", "sftp", "downloader"], "author": "", "license": "ISC", "description": "M3U Import Cloud X2 - Gestor de listas M3U con descargas remotas SSH/SFTP", "dependencies": {"@types/node": "^24.0.10", "@types/react": "^19.1.8", "@types/react-dom": "^19.1.6", "@types/ssh2": "^1.15.5", "@types/styled-components": "^5.1.34", "electron": "^37.2.0", "m3u8-parser": "^7.2.0", "node-ssh": "^13.2.1", "react": "^19.1.0", "react-dom": "^19.1.0", "react-resizable-panels": "^3.0.3", "ssh2": "^1.16.0", "ssh2-sftp-client": "^12.0.1", "styled-components": "^6.1.19", "typescript": "^5.8.3"}, "devDependencies": {"concurrently": "^9.2.0", "copy-webpack-plugin": "^13.0.0", "css-loader": "^7.1.2", "electron-builder": "^26.0.12", "html-webpack-plugin": "^5.6.3", "style-loader": "^4.0.0", "ts-loader": "^9.5.2", "wait-on": "^8.0.3", "webpack": "^5.99.9", "webpack-cli": "^6.0.1", "webpack-dev-server": "^5.2.2"}}